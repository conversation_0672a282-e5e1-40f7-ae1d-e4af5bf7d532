<template>
  <div class="student-list-container">
    <el-button @click="dialogVisible = true">打开对话框</el-button>
    
    <el-dialog
      v-model="dialogVisible"
      title="测试对话框"
      width="80%"
      top="5vh"
      :close-on-click-modal="false"
      class="student-dialog"
    >
      <div class="dialog-container">
        <div class="dialog-sidebar">
          <div 
            v-for="(section, index) in formSections" 
            :key="index"
            class="sidebar-item"
            :class="{ active: activeSection === index }"
            @click="scrollToSection(index)"
          >
            <div class="sidebar-item-number">{{ index + 1 }}</div>
            <div class="sidebar-item-text">{{ section }}</div>
          </div>
        </div>
        
        <div class="dialog-content" ref="formContent">
          <el-form
            :model="studentForm"
            label-width="100px"
            ref="studentFormRef"
            class="student-form"
          >
            <div class="form-section" v-for="(section, index) in formSections" :key="'section-'+index">
              <div class="section-header">
                <div class="section-title">{{ index + 1 }}. {{ section }}</div>
              </div>
              <div class="section-content">
                <el-form-item :label="section">
                  <el-input v-model="studentForm.name" :placeholder="'请输入'+section"></el-input>
                </el-form-item>
              </div>
            </div>
          </el-form>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="dialogVisible = false">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, nextTick } from "vue";

// 对话框相关
const dialogVisible = ref(false);
const studentFormRef = ref(null);

// 表单部分相关
const formContent = ref(null);
const formSections = [
  "个人基础信息",
  "本科成绩情况",
  "英语基础",
  "目标院校倾向",
  "考研成绩预估",
  "个性化需求"
];
const activeSection = ref(0);

// 学员表单
const studentForm = reactive({
  name: "",
});

// 滚动到指定部分
const scrollToSection = (index) => {
  activeSection.value = index;
  const sections = document.querySelectorAll('.form-section');
  if (sections[index]) {
    formContent.value.scrollTop = sections[index].offsetTop - 20;
  }
};

// 监听滚动事件，更新活动部分
const handleScroll = () => {
  if (!formContent.value) return;
  
  const sections = document.querySelectorAll('.form-section');
  const scrollTop = formContent.value.scrollTop;
  
  for (let i = sections.length - 1; i >= 0; i--) {
    if (scrollTop >= sections[i].offsetTop - 50) {
      activeSection.value = i;
      break;
    }
  }
};

// 监听对话框打开，初始化滚动事件
watch(dialogVisible, (val) => {
  if (val) {
    nextTick(() => {
      if (formContent.value) {
        formContent.value.addEventListener('scroll', handleScroll);
      }
    });
  } else {
    if (formContent.value) {
      formContent.value.removeEventListener('scroll', handleScroll);
    }
  }
});
</script>

<style scoped>
.student-dialog {
  --el-dialog-padding-primary: 0;
}

.dialog-container {
  display: flex;
  height: 75vh;
}

.dialog-sidebar {
  width: 200px;
  background-color: #f5f7fa;
  border-right: 1px solid #e4e7ed;
  padding: 20px 0;
  overflow-y: auto;
}

.sidebar-item {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
}

.sidebar-item:hover {
  background-color: #e6f7f1;
}

.sidebar-item.active {
  background-color: #e6f7f1;
  color: #1bb394;
  font-weight: bold;
}

.sidebar-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background-color: #1bb394;
}

.sidebar-item-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #1bb394;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  font-size: 14px;
}

.sidebar-item.active .sidebar-item-number {
  background-color: #19a588;
}

.sidebar-item-text {
  font-size: 14px;
}

.dialog-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 20px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  overflow: hidden;
}

.section-header {
  background-color: #dcf7f0;
  padding: 10px 15px;
  border-bottom: 1px solid #ebeef5;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.section-content {
  padding: 15px;
  background-color: #fff;
}
</style>
