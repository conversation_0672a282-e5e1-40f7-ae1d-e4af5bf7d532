import{_ as De}from"./_plugin-vue_export-helper-BCEt7Ct6.js";/* empty css                 *//* empty css                  */import{r as d,n as Ue,a9 as Ve,aa as Me,c as x,o as N,b as k,l as P,R as Fe,t as I,e as D,w as Y,j as $e,F as fe,s as pe,i as He,h as Ke}from"./index-Cu_gl4pu.js";import{q as We,t as ze}from"./report-CU5jWwkq.js";const S=Object.create(null);S.open="0";S.close="1";S.ping="2";S.pong="3";S.message="4";S.upgrade="5";S.noop="6";const M=Object.create(null);Object.keys(S).forEach(n=>{M[S[n]]=n});const Q={type:"error",data:"parser error"},ve=typeof Blob=="function"||typeof Blob<"u"&&Object.prototype.toString.call(Blob)==="[object BlobConstructor]",be=typeof ArrayBuffer=="function",we=n=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(n):n&&n.buffer instanceof ArrayBuffer,ne=({type:n,data:e},t,s)=>ve&&e instanceof Blob?t?s(e):de(e,s):be&&(e instanceof ArrayBuffer||we(e))?t?s(e):de(new Blob([e]),s):s(S[n]+(e||"")),de=(n,e)=>{const t=new FileReader;return t.onload=function(){const s=t.result.split(",")[1];e("b"+(s||""))},t.readAsDataURL(n)};function ye(n){return n instanceof Uint8Array?n:n instanceof ArrayBuffer?new Uint8Array(n):new Uint8Array(n.buffer,n.byteOffset,n.byteLength)}let J;function Ye(n,e){if(ve&&n.data instanceof Blob)return n.data.arrayBuffer().then(ye).then(e);if(be&&(n.data instanceof ArrayBuffer||we(n.data)))return e(ye(n.data));ne(n,!1,t=>{J||(J=new TextEncoder),e(J.encode(t))})}const me="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",q=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let n=0;n<me.length;n++)q[me.charCodeAt(n)]=n;const Je=n=>{let e=n.length*.75,t=n.length,s,i=0,r,a,c,h;n[n.length-1]==="="&&(e--,n[n.length-2]==="="&&e--);const y=new ArrayBuffer(e),p=new Uint8Array(y);for(s=0;s<t;s+=4)r=q[n.charCodeAt(s)],a=q[n.charCodeAt(s+1)],c=q[n.charCodeAt(s+2)],h=q[n.charCodeAt(s+3)],p[i++]=r<<2|a>>4,p[i++]=(a&15)<<4|c>>2,p[i++]=(c&3)<<6|h&63;return y},Xe=typeof ArrayBuffer=="function",ie=(n,e)=>{if(typeof n!="string")return{type:"message",data:ke(n,e)};const t=n.charAt(0);return t==="b"?{type:"message",data:je(n.substring(1),e)}:M[t]?n.length>1?{type:M[t],data:n.substring(1)}:{type:M[t]}:Q},je=(n,e)=>{if(Xe){const t=Je(n);return ke(t,e)}else return{base64:!0,data:n}},ke=(n,e)=>{switch(e){case"blob":return n instanceof Blob?n:new Blob([n]);case"arraybuffer":default:return n instanceof ArrayBuffer?n:n.buffer}},Ee="",Qe=(n,e)=>{const t=n.length,s=new Array(t);let i=0;n.forEach((r,a)=>{ne(r,!1,c=>{s[a]=c,++i===t&&e(s.join(Ee))})})},Ze=(n,e)=>{const t=n.split(Ee),s=[];for(let i=0;i<t.length;i++){const r=ie(t[i],e);if(s.push(r),r.type==="error")break}return s};function Ge(){return new TransformStream({transform(n,e){Ye(n,t=>{const s=t.length;let i;if(s<126)i=new Uint8Array(1),new DataView(i.buffer).setUint8(0,s);else if(s<65536){i=new Uint8Array(3);const r=new DataView(i.buffer);r.setUint8(0,126),r.setUint16(1,s)}else{i=new Uint8Array(9);const r=new DataView(i.buffer);r.setUint8(0,127),r.setBigUint64(1,BigInt(s))}n.data&&typeof n.data!="string"&&(i[0]|=128),e.enqueue(i),e.enqueue(t)})}})}let X;function U(n){return n.reduce((e,t)=>e+t.length,0)}function V(n,e){if(n[0].length===e)return n.shift();const t=new Uint8Array(e);let s=0;for(let i=0;i<e;i++)t[i]=n[0][s++],s===n[0].length&&(n.shift(),s=0);return n.length&&s<n[0].length&&(n[0]=n[0].slice(s)),t}function et(n,e){X||(X=new TextDecoder);const t=[];let s=0,i=-1,r=!1;return new TransformStream({transform(a,c){for(t.push(a);;){if(s===0){if(U(t)<1)break;const h=V(t,1);r=(h[0]&128)===128,i=h[0]&127,i<126?s=3:i===126?s=1:s=2}else if(s===1){if(U(t)<2)break;const h=V(t,2);i=new DataView(h.buffer,h.byteOffset,h.length).getUint16(0),s=3}else if(s===2){if(U(t)<8)break;const h=V(t,8),y=new DataView(h.buffer,h.byteOffset,h.length),p=y.getUint32(0);if(p>Math.pow(2,21)-1){c.enqueue(Q);break}i=p*Math.pow(2,32)+y.getUint32(4),s=3}else{if(U(t)<i)break;const h=V(t,i);c.enqueue(ie(r?h:X.decode(h),e)),s=0}if(i===0||i>n){c.enqueue(Q);break}}}})}const Se=4;function f(n){if(n)return tt(n)}function tt(n){for(var e in f.prototype)n[e]=f.prototype[e];return n}f.prototype.on=f.prototype.addEventListener=function(n,e){return this._callbacks=this._callbacks||{},(this._callbacks["$"+n]=this._callbacks["$"+n]||[]).push(e),this};f.prototype.once=function(n,e){function t(){this.off(n,t),e.apply(this,arguments)}return t.fn=e,this.on(n,t),this};f.prototype.off=f.prototype.removeListener=f.prototype.removeAllListeners=f.prototype.removeEventListener=function(n,e){if(this._callbacks=this._callbacks||{},arguments.length==0)return this._callbacks={},this;var t=this._callbacks["$"+n];if(!t)return this;if(arguments.length==1)return delete this._callbacks["$"+n],this;for(var s,i=0;i<t.length;i++)if(s=t[i],s===e||s.fn===e){t.splice(i,1);break}return t.length===0&&delete this._callbacks["$"+n],this};f.prototype.emit=function(n){this._callbacks=this._callbacks||{};for(var e=new Array(arguments.length-1),t=this._callbacks["$"+n],s=1;s<arguments.length;s++)e[s-1]=arguments[s];if(t){t=t.slice(0);for(var s=0,i=t.length;s<i;++s)t[s].apply(this,e)}return this};f.prototype.emitReserved=f.prototype.emit;f.prototype.listeners=function(n){return this._callbacks=this._callbacks||{},this._callbacks["$"+n]||[]};f.prototype.hasListeners=function(n){return!!this.listeners(n).length};const K=typeof Promise=="function"&&typeof Promise.resolve=="function"?e=>Promise.resolve().then(e):(e,t)=>t(e,0),m=typeof self<"u"?self:typeof window<"u"?window:Function("return this")(),st="arraybuffer";function Re(n,...e){return e.reduce((t,s)=>(n.hasOwnProperty(s)&&(t[s]=n[s]),t),{})}const nt=m.setTimeout,it=m.clearTimeout;function W(n,e){e.useNativeTimers?(n.setTimeoutFn=nt.bind(m),n.clearTimeoutFn=it.bind(m)):(n.setTimeoutFn=m.setTimeout.bind(m),n.clearTimeoutFn=m.clearTimeout.bind(m))}const rt=1.33;function ot(n){return typeof n=="string"?at(n):Math.ceil((n.byteLength||n.size)*rt)}function at(n){let e=0,t=0;for(let s=0,i=n.length;s<i;s++)e=n.charCodeAt(s),e<128?t+=1:e<2048?t+=2:e<55296||e>=57344?t+=3:(s++,t+=4);return t}function Te(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}function ct(n){let e="";for(let t in n)n.hasOwnProperty(t)&&(e.length&&(e+="&"),e+=encodeURIComponent(t)+"="+encodeURIComponent(n[t]));return e}function ht(n){let e={},t=n.split("&");for(let s=0,i=t.length;s<i;s++){let r=t[s].split("=");e[decodeURIComponent(r[0])]=decodeURIComponent(r[1])}return e}class lt extends Error{constructor(e,t,s){super(e),this.description=t,this.context=s,this.type="TransportError"}}class re extends f{constructor(e){super(),this.writable=!1,W(this,e),this.opts=e,this.query=e.query,this.socket=e.socket,this.supportsBinary=!e.forceBase64}onError(e,t,s){return super.emitReserved("error",new lt(e,t,s)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return(this.readyState==="opening"||this.readyState==="open")&&(this.doClose(),this.onClose()),this}send(e){this.readyState==="open"&&this.write(e)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(e){const t=ie(e,this.socket.binaryType);this.onPacket(t)}onPacket(e){super.emitReserved("packet",e)}onClose(e){this.readyState="closed",super.emitReserved("close",e)}pause(e){}createUri(e,t={}){return e+"://"+this._hostname()+this._port()+this.opts.path+this._query(t)}_hostname(){const e=this.opts.hostname;return e.indexOf(":")===-1?e:"["+e+"]"}_port(){return this.opts.port&&(this.opts.secure&&+(this.opts.port!==443)||!this.opts.secure&&Number(this.opts.port)!==80)?":"+this.opts.port:""}_query(e){const t=ct(e);return t.length?"?"+t:""}}class ut extends re{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(e){this.readyState="pausing";const t=()=>{this.readyState="paused",e()};if(this._polling||!this.writable){let s=0;this._polling&&(s++,this.once("pollComplete",function(){--s||t()})),this.writable||(s++,this.once("drain",function(){--s||t()}))}else t()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(e){const t=s=>{if(this.readyState==="opening"&&s.type==="open"&&this.onOpen(),s.type==="close")return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(s)};Ze(e,this.socket.binaryType).forEach(t),this.readyState!=="closed"&&(this._polling=!1,this.emitReserved("pollComplete"),this.readyState==="open"&&this._poll())}doClose(){const e=()=>{this.write([{type:"close"}])};this.readyState==="open"?e():this.once("open",e)}write(e){this.writable=!1,Qe(e,t=>{this.doWrite(t,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){const e=this.opts.secure?"https":"http",t=this.query||{};return this.opts.timestampRequests!==!1&&(t[this.opts.timestampParam]=Te()),!this.supportsBinary&&!t.sid&&(t.b64=1),this.createUri(e,t)}}let Ae=!1;try{Ae=typeof XMLHttpRequest<"u"&&"withCredentials"in new XMLHttpRequest}catch{}const ft=Ae;function pt(){}class dt extends ut{constructor(e){if(super(e),typeof location<"u"){const t=location.protocol==="https:";let s=location.port;s||(s=t?"443":"80"),this.xd=typeof location<"u"&&e.hostname!==location.hostname||s!==e.port}}doWrite(e,t){const s=this.request({method:"POST",data:e});s.on("success",t),s.on("error",(i,r)=>{this.onError("xhr post error",i,r)})}doPoll(){const e=this.request();e.on("data",this.onData.bind(this)),e.on("error",(t,s)=>{this.onError("xhr poll error",t,s)}),this.pollXhr=e}}class E extends f{constructor(e,t,s){super(),this.createRequest=e,W(this,s),this._opts=s,this._method=s.method||"GET",this._uri=t,this._data=s.data!==void 0?s.data:null,this._create()}_create(){var e;const t=Re(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");t.xdomain=!!this._opts.xd;const s=this._xhr=this.createRequest(t);try{s.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders){s.setDisableHeaderCheck&&s.setDisableHeaderCheck(!0);for(let i in this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(i)&&s.setRequestHeader(i,this._opts.extraHeaders[i])}}catch{}if(this._method==="POST")try{s.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch{}try{s.setRequestHeader("Accept","*/*")}catch{}(e=this._opts.cookieJar)===null||e===void 0||e.addCookies(s),"withCredentials"in s&&(s.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(s.timeout=this._opts.requestTimeout),s.onreadystatechange=()=>{var i;s.readyState===3&&((i=this._opts.cookieJar)===null||i===void 0||i.parseCookies(s.getResponseHeader("set-cookie"))),s.readyState===4&&(s.status===200||s.status===1223?this._onLoad():this.setTimeoutFn(()=>{this._onError(typeof s.status=="number"?s.status:0)},0))},s.send(this._data)}catch(i){this.setTimeoutFn(()=>{this._onError(i)},0);return}typeof document<"u"&&(this._index=E.requestsCount++,E.requests[this._index]=this)}_onError(e){this.emitReserved("error",e,this._xhr),this._cleanup(!0)}_cleanup(e){if(!(typeof this._xhr>"u"||this._xhr===null)){if(this._xhr.onreadystatechange=pt,e)try{this._xhr.abort()}catch{}typeof document<"u"&&delete E.requests[this._index],this._xhr=null}}_onLoad(){const e=this._xhr.responseText;e!==null&&(this.emitReserved("data",e),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}E.requestsCount=0;E.requests={};if(typeof document<"u"){if(typeof attachEvent=="function")attachEvent("onunload",ge);else if(typeof addEventListener=="function"){const n="onpagehide"in m?"pagehide":"unload";addEventListener(n,ge,!1)}}function ge(){for(let n in E.requests)E.requests.hasOwnProperty(n)&&E.requests[n].abort()}const yt=function(){const n=Ce({xdomain:!1});return n&&n.responseType!==null}();class mt extends dt{constructor(e){super(e);const t=e&&e.forceBase64;this.supportsBinary=yt&&!t}request(e={}){return Object.assign(e,{xd:this.xd},this.opts),new E(Ce,this.uri(),e)}}function Ce(n){const e=n.xdomain;try{if(typeof XMLHttpRequest<"u"&&(!e||ft))return new XMLHttpRequest}catch{}if(!e)try{return new m[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch{}}const Oe=typeof navigator<"u"&&typeof navigator.product=="string"&&navigator.product.toLowerCase()==="reactnative";class gt extends re{get name(){return"websocket"}doOpen(){const e=this.uri(),t=this.opts.protocols,s=Oe?{}:Re(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(s.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(e,t,s)}catch(i){return this.emitReserved("error",i)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=e=>this.onClose({description:"websocket connection closed",context:e}),this.ws.onmessage=e=>this.onData(e.data),this.ws.onerror=e=>this.onError("websocket error",e)}write(e){this.writable=!1;for(let t=0;t<e.length;t++){const s=e[t],i=t===e.length-1;ne(s,this.supportsBinary,r=>{try{this.doWrite(s,r)}catch{}i&&K(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){typeof this.ws<"u"&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){const e=this.opts.secure?"wss":"ws",t=this.query||{};return this.opts.timestampRequests&&(t[this.opts.timestampParam]=Te()),this.supportsBinary||(t.b64=1),this.createUri(e,t)}}const j=m.WebSocket||m.MozWebSocket;class _t extends gt{createSocket(e,t,s){return Oe?new j(e,t,s):t?new j(e,t):new j(e)}doWrite(e,t){this.ws.send(t)}}class vt extends re{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(e){return this.emitReserved("error",e)}this._transport.closed.then(()=>{this.onClose()}).catch(e=>{this.onError("webtransport error",e)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(e=>{const t=et(Number.MAX_SAFE_INTEGER,this.socket.binaryType),s=e.readable.pipeThrough(t).getReader(),i=Ge();i.readable.pipeTo(e.writable),this._writer=i.writable.getWriter();const r=()=>{s.read().then(({done:c,value:h})=>{c||(this.onPacket(h),r())}).catch(c=>{})};r();const a={type:"open"};this.query.sid&&(a.data=`{"sid":"${this.query.sid}"}`),this._writer.write(a).then(()=>this.onOpen())})})}write(e){this.writable=!1;for(let t=0;t<e.length;t++){const s=e[t],i=t===e.length-1;this._writer.write(s).then(()=>{i&&K(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var e;(e=this._transport)===null||e===void 0||e.close()}}const bt={websocket:_t,webtransport:vt,polling:mt},wt=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,kt=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function Z(n){if(n.length>8e3)throw"URI too long";const e=n,t=n.indexOf("["),s=n.indexOf("]");t!=-1&&s!=-1&&(n=n.substring(0,t)+n.substring(t,s).replace(/:/g,";")+n.substring(s,n.length));let i=wt.exec(n||""),r={},a=14;for(;a--;)r[kt[a]]=i[a]||"";return t!=-1&&s!=-1&&(r.source=e,r.host=r.host.substring(1,r.host.length-1).replace(/;/g,":"),r.authority=r.authority.replace("[","").replace("]","").replace(/;/g,":"),r.ipv6uri=!0),r.pathNames=Et(r,r.path),r.queryKey=St(r,r.query),r}function Et(n,e){const t=/\/{2,9}/g,s=e.replace(t,"/").split("/");return(e.slice(0,1)=="/"||e.length===0)&&s.splice(0,1),e.slice(-1)=="/"&&s.splice(s.length-1,1),s}function St(n,e){const t={};return e.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(s,i,r){i&&(t[i]=r)}),t}const G=typeof addEventListener=="function"&&typeof removeEventListener=="function",F=[];G&&addEventListener("offline",()=>{F.forEach(n=>n())},!1);class T extends f{constructor(e,t){if(super(),this.binaryType=st,this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,e&&typeof e=="object"&&(t=e,e=null),e){const s=Z(e);t.hostname=s.host,t.secure=s.protocol==="https"||s.protocol==="wss",t.port=s.port,s.query&&(t.query=s.query)}else t.host&&(t.hostname=Z(t.host).host);W(this,t),this.secure=t.secure!=null?t.secure:typeof location<"u"&&location.protocol==="https:",t.hostname&&!t.port&&(t.port=this.secure?"443":"80"),this.hostname=t.hostname||(typeof location<"u"?location.hostname:"localhost"),this.port=t.port||(typeof location<"u"&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},t.transports.forEach(s=>{const i=s.prototype.name;this.transports.push(i),this._transportsByName[i]=s}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},t),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),typeof this.opts.query=="string"&&(this.opts.query=ht(this.opts.query)),G&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),this.hostname!=="localhost"&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},F.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(e){const t=Object.assign({},this.opts.query);t.EIO=Se,t.transport=e,this.id&&(t.sid=this.id);const s=Object.assign({},this.opts,{query:t,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[e]);return new this._transportsByName[e](s)}_open(){if(this.transports.length===0){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}const e=this.opts.rememberUpgrade&&T.priorWebsocketSuccess&&this.transports.indexOf("websocket")!==-1?"websocket":this.transports[0];this.readyState="opening";const t=this.createTransport(e);t.open(),this.setTransport(t)}setTransport(e){this.transport&&this.transport.removeAllListeners(),this.transport=e,e.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",t=>this._onClose("transport close",t))}onOpen(){this.readyState="open",T.priorWebsocketSuccess=this.transport.name==="websocket",this.emitReserved("open"),this.flush()}_onPacket(e){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing")switch(this.emitReserved("packet",e),this.emitReserved("heartbeat"),e.type){case"open":this.onHandshake(JSON.parse(e.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":const t=new Error("server error");t.code=e.data,this._onError(t);break;case"message":this.emitReserved("data",e.data),this.emitReserved("message",e.data);break}}onHandshake(e){this.emitReserved("handshake",e),this.id=e.sid,this.transport.query.sid=e.sid,this._pingInterval=e.pingInterval,this._pingTimeout=e.pingTimeout,this._maxPayload=e.maxPayload,this.onOpen(),this.readyState!=="closed"&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);const e=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+e,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},e),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,this.writeBuffer.length===0?this.emitReserved("drain"):this.flush()}flush(){if(this.readyState!=="closed"&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const e=this._getWritablePackets();this.transport.send(e),this._prevBufferLen=e.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&this.transport.name==="polling"&&this.writeBuffer.length>1))return this.writeBuffer;let t=1;for(let s=0;s<this.writeBuffer.length;s++){const i=this.writeBuffer[s].data;if(i&&(t+=ot(i)),s>0&&t>this._maxPayload)return this.writeBuffer.slice(0,s);t+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;const e=Date.now()>this._pingTimeoutTime;return e&&(this._pingTimeoutTime=0,K(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),e}write(e,t,s){return this._sendPacket("message",e,t,s),this}send(e,t,s){return this._sendPacket("message",e,t,s),this}_sendPacket(e,t,s,i){if(typeof t=="function"&&(i=t,t=void 0),typeof s=="function"&&(i=s,s=null),this.readyState==="closing"||this.readyState==="closed")return;s=s||{},s.compress=s.compress!==!1;const r={type:e,data:t,options:s};this.emitReserved("packetCreate",r),this.writeBuffer.push(r),i&&this.once("flush",i),this.flush()}close(){const e=()=>{this._onClose("forced close"),this.transport.close()},t=()=>{this.off("upgrade",t),this.off("upgradeError",t),e()},s=()=>{this.once("upgrade",t),this.once("upgradeError",t)};return(this.readyState==="opening"||this.readyState==="open")&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?s():e()}):this.upgrading?s():e()),this}_onError(e){if(T.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&this.readyState==="opening")return this.transports.shift(),this._open();this.emitReserved("error",e),this._onClose("transport error",e)}_onClose(e,t){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing"){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),G&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){const s=F.indexOf(this._offlineEventListener);s!==-1&&F.splice(s,1)}this.readyState="closed",this.id=null,this.emitReserved("close",e,t),this.writeBuffer=[],this._prevBufferLen=0}}}T.protocol=Se;class Rt extends T{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),this.readyState==="open"&&this.opts.upgrade)for(let e=0;e<this._upgrades.length;e++)this._probe(this._upgrades[e])}_probe(e){let t=this.createTransport(e),s=!1;T.priorWebsocketSuccess=!1;const i=()=>{s||(t.send([{type:"ping",data:"probe"}]),t.once("packet",_=>{if(!s)if(_.type==="pong"&&_.data==="probe"){if(this.upgrading=!0,this.emitReserved("upgrading",t),!t)return;T.priorWebsocketSuccess=t.name==="websocket",this.transport.pause(()=>{s||this.readyState!=="closed"&&(p(),this.setTransport(t),t.send([{type:"upgrade"}]),this.emitReserved("upgrade",t),t=null,this.upgrading=!1,this.flush())})}else{const A=new Error("probe error");A.transport=t.name,this.emitReserved("upgradeError",A)}}))};function r(){s||(s=!0,p(),t.close(),t=null)}const a=_=>{const A=new Error("probe error: "+_);A.transport=t.name,r(),this.emitReserved("upgradeError",A)};function c(){a("transport closed")}function h(){a("socket closed")}function y(_){t&&_.name!==t.name&&r()}const p=()=>{t.removeListener("open",i),t.removeListener("error",a),t.removeListener("close",c),this.off("close",h),this.off("upgrading",y)};t.once("open",i),t.once("error",a),t.once("close",c),this.once("close",h),this.once("upgrading",y),this._upgrades.indexOf("webtransport")!==-1&&e!=="webtransport"?this.setTimeoutFn(()=>{s||t.open()},200):t.open()}onHandshake(e){this._upgrades=this._filterUpgrades(e.upgrades),super.onHandshake(e)}_filterUpgrades(e){const t=[];for(let s=0;s<e.length;s++)~this.transports.indexOf(e[s])&&t.push(e[s]);return t}}let Tt=class extends Rt{constructor(e,t={}){const s=typeof e=="object"?e:t;(!s.transports||s.transports&&typeof s.transports[0]=="string")&&(s.transports=(s.transports||["polling","websocket","webtransport"]).map(i=>bt[i]).filter(i=>!!i)),super(e,s)}};function At(n,e="",t){let s=n;t=t||typeof location<"u"&&location,n==null&&(n=t.protocol+"//"+t.host),typeof n=="string"&&(n.charAt(0)==="/"&&(n.charAt(1)==="/"?n=t.protocol+n:n=t.host+n),/^(https?|wss?):\/\//.test(n)||(typeof t<"u"?n=t.protocol+"//"+n:n="https://"+n),s=Z(n)),s.port||(/^(http|ws)$/.test(s.protocol)?s.port="80":/^(http|ws)s$/.test(s.protocol)&&(s.port="443")),s.path=s.path||"/";const r=s.host.indexOf(":")!==-1?"["+s.host+"]":s.host;return s.id=s.protocol+"://"+r+":"+s.port+e,s.href=s.protocol+"://"+r+(t&&t.port===s.port?"":":"+s.port),s}const Ct=typeof ArrayBuffer=="function",Ot=n=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(n):n.buffer instanceof ArrayBuffer,Be=Object.prototype.toString,Bt=typeof Blob=="function"||typeof Blob<"u"&&Be.call(Blob)==="[object BlobConstructor]",xt=typeof File=="function"||typeof File<"u"&&Be.call(File)==="[object FileConstructor]";function oe(n){return Ct&&(n instanceof ArrayBuffer||Ot(n))||Bt&&n instanceof Blob||xt&&n instanceof File}function $(n,e){if(!n||typeof n!="object")return!1;if(Array.isArray(n)){for(let t=0,s=n.length;t<s;t++)if($(n[t]))return!0;return!1}if(oe(n))return!0;if(n.toJSON&&typeof n.toJSON=="function"&&arguments.length===1)return $(n.toJSON(),!0);for(const t in n)if(Object.prototype.hasOwnProperty.call(n,t)&&$(n[t]))return!0;return!1}function Nt(n){const e=[],t=n.data,s=n;return s.data=ee(t,e),s.attachments=e.length,{packet:s,buffers:e}}function ee(n,e){if(!n)return n;if(oe(n)){const t={_placeholder:!0,num:e.length};return e.push(n),t}else if(Array.isArray(n)){const t=new Array(n.length);for(let s=0;s<n.length;s++)t[s]=ee(n[s],e);return t}else if(typeof n=="object"&&!(n instanceof Date)){const t={};for(const s in n)Object.prototype.hasOwnProperty.call(n,s)&&(t[s]=ee(n[s],e));return t}return n}function Lt(n,e){return n.data=te(n.data,e),delete n.attachments,n}function te(n,e){if(!n)return n;if(n&&n._placeholder===!0){if(typeof n.num=="number"&&n.num>=0&&n.num<e.length)return e[n.num];throw new Error("illegal attachments")}else if(Array.isArray(n))for(let t=0;t<n.length;t++)n[t]=te(n[t],e);else if(typeof n=="object")for(const t in n)Object.prototype.hasOwnProperty.call(n,t)&&(n[t]=te(n[t],e));return n}const qt=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],Pt=5;var l;(function(n){n[n.CONNECT=0]="CONNECT",n[n.DISCONNECT=1]="DISCONNECT",n[n.EVENT=2]="EVENT",n[n.ACK=3]="ACK",n[n.CONNECT_ERROR=4]="CONNECT_ERROR",n[n.BINARY_EVENT=5]="BINARY_EVENT",n[n.BINARY_ACK=6]="BINARY_ACK"})(l||(l={}));class It{constructor(e){this.replacer=e}encode(e){return(e.type===l.EVENT||e.type===l.ACK)&&$(e)?this.encodeAsBinary({type:e.type===l.EVENT?l.BINARY_EVENT:l.BINARY_ACK,nsp:e.nsp,data:e.data,id:e.id}):[this.encodeAsString(e)]}encodeAsString(e){let t=""+e.type;return(e.type===l.BINARY_EVENT||e.type===l.BINARY_ACK)&&(t+=e.attachments+"-"),e.nsp&&e.nsp!=="/"&&(t+=e.nsp+","),e.id!=null&&(t+=e.id),e.data!=null&&(t+=JSON.stringify(e.data,this.replacer)),t}encodeAsBinary(e){const t=Nt(e),s=this.encodeAsString(t.packet),i=t.buffers;return i.unshift(s),i}}function _e(n){return Object.prototype.toString.call(n)==="[object Object]"}class ae extends f{constructor(e){super(),this.reviver=e}add(e){let t;if(typeof e=="string"){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");t=this.decodeString(e);const s=t.type===l.BINARY_EVENT;s||t.type===l.BINARY_ACK?(t.type=s?l.EVENT:l.ACK,this.reconstructor=new Dt(t),t.attachments===0&&super.emitReserved("decoded",t)):super.emitReserved("decoded",t)}else if(oe(e)||e.base64)if(this.reconstructor)t=this.reconstructor.takeBinaryData(e),t&&(this.reconstructor=null,super.emitReserved("decoded",t));else throw new Error("got binary data when not reconstructing a packet");else throw new Error("Unknown type: "+e)}decodeString(e){let t=0;const s={type:Number(e.charAt(0))};if(l[s.type]===void 0)throw new Error("unknown packet type "+s.type);if(s.type===l.BINARY_EVENT||s.type===l.BINARY_ACK){const r=t+1;for(;e.charAt(++t)!=="-"&&t!=e.length;);const a=e.substring(r,t);if(a!=Number(a)||e.charAt(t)!=="-")throw new Error("Illegal attachments");s.attachments=Number(a)}if(e.charAt(t+1)==="/"){const r=t+1;for(;++t&&!(e.charAt(t)===","||t===e.length););s.nsp=e.substring(r,t)}else s.nsp="/";const i=e.charAt(t+1);if(i!==""&&Number(i)==i){const r=t+1;for(;++t;){const a=e.charAt(t);if(a==null||Number(a)!=a){--t;break}if(t===e.length)break}s.id=Number(e.substring(r,t+1))}if(e.charAt(++t)){const r=this.tryParse(e.substr(t));if(ae.isPayloadValid(s.type,r))s.data=r;else throw new Error("invalid payload")}return s}tryParse(e){try{return JSON.parse(e,this.reviver)}catch{return!1}}static isPayloadValid(e,t){switch(e){case l.CONNECT:return _e(t);case l.DISCONNECT:return t===void 0;case l.CONNECT_ERROR:return typeof t=="string"||_e(t);case l.EVENT:case l.BINARY_EVENT:return Array.isArray(t)&&(typeof t[0]=="number"||typeof t[0]=="string"&&qt.indexOf(t[0])===-1);case l.ACK:case l.BINARY_ACK:return Array.isArray(t)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class Dt{constructor(e){this.packet=e,this.buffers=[],this.reconPack=e}takeBinaryData(e){if(this.buffers.push(e),this.buffers.length===this.reconPack.attachments){const t=Lt(this.reconPack,this.buffers);return this.finishedReconstruction(),t}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}const Ut=Object.freeze(Object.defineProperty({__proto__:null,Decoder:ae,Encoder:It,get PacketType(){return l},protocol:Pt},Symbol.toStringTag,{value:"Module"}));function g(n,e,t){return n.on(e,t),function(){n.off(e,t)}}const Vt=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class xe extends f{constructor(e,t,s){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=e,this.nsp=t,s&&s.auth&&(this.auth=s.auth),this._opts=Object.assign({},s),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;const e=this.io;this.subs=[g(e,"open",this.onopen.bind(this)),g(e,"packet",this.onpacket.bind(this)),g(e,"error",this.onerror.bind(this)),g(e,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected?this:(this.subEvents(),this.io._reconnecting||this.io.open(),this.io._readyState==="open"&&this.onopen(),this)}open(){return this.connect()}send(...e){return e.unshift("message"),this.emit.apply(this,e),this}emit(e,...t){var s,i,r;if(Vt.hasOwnProperty(e))throw new Error('"'+e.toString()+'" is a reserved event name');if(t.unshift(e),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(t),this;const a={type:l.EVENT,data:t};if(a.options={},a.options.compress=this.flags.compress!==!1,typeof t[t.length-1]=="function"){const p=this.ids++,_=t.pop();this._registerAckCallback(p,_),a.id=p}const c=(i=(s=this.io.engine)===null||s===void 0?void 0:s.transport)===null||i===void 0?void 0:i.writable,h=this.connected&&!(!((r=this.io.engine)===null||r===void 0)&&r._hasPingExpired());return this.flags.volatile&&!c||(h?(this.notifyOutgoingListeners(a),this.packet(a)):this.sendBuffer.push(a)),this.flags={},this}_registerAckCallback(e,t){var s;const i=(s=this.flags.timeout)!==null&&s!==void 0?s:this._opts.ackTimeout;if(i===void 0){this.acks[e]=t;return}const r=this.io.setTimeoutFn(()=>{delete this.acks[e];for(let c=0;c<this.sendBuffer.length;c++)this.sendBuffer[c].id===e&&this.sendBuffer.splice(c,1);t.call(this,new Error("operation has timed out"))},i),a=(...c)=>{this.io.clearTimeoutFn(r),t.apply(this,c)};a.withError=!0,this.acks[e]=a}emitWithAck(e,...t){return new Promise((s,i)=>{const r=(a,c)=>a?i(a):s(c);r.withError=!0,t.push(r),this.emit(e,...t)})}_addToQueue(e){let t;typeof e[e.length-1]=="function"&&(t=e.pop());const s={id:this._queueSeq++,tryCount:0,pending:!1,args:e,flags:Object.assign({fromQueue:!0},this.flags)};e.push((i,...r)=>s!==this._queue[0]?void 0:(i!==null?s.tryCount>this._opts.retries&&(this._queue.shift(),t&&t(i)):(this._queue.shift(),t&&t(null,...r)),s.pending=!1,this._drainQueue())),this._queue.push(s),this._drainQueue()}_drainQueue(e=!1){if(!this.connected||this._queue.length===0)return;const t=this._queue[0];t.pending&&!e||(t.pending=!0,t.tryCount++,this.flags=t.flags,this.emit.apply(this,t.args))}packet(e){e.nsp=this.nsp,this.io._packet(e)}onopen(){typeof this.auth=="function"?this.auth(e=>{this._sendConnectPacket(e)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(e){this.packet({type:l.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},e):e})}onerror(e){this.connected||this.emitReserved("connect_error",e)}onclose(e,t){this.connected=!1,delete this.id,this.emitReserved("disconnect",e,t),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(e=>{if(!this.sendBuffer.some(s=>String(s.id)===e)){const s=this.acks[e];delete this.acks[e],s.withError&&s.call(this,new Error("socket has been disconnected"))}})}onpacket(e){if(e.nsp===this.nsp)switch(e.type){case l.CONNECT:e.data&&e.data.sid?this.onconnect(e.data.sid,e.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case l.EVENT:case l.BINARY_EVENT:this.onevent(e);break;case l.ACK:case l.BINARY_ACK:this.onack(e);break;case l.DISCONNECT:this.ondisconnect();break;case l.CONNECT_ERROR:this.destroy();const s=new Error(e.data.message);s.data=e.data.data,this.emitReserved("connect_error",s);break}}onevent(e){const t=e.data||[];e.id!=null&&t.push(this.ack(e.id)),this.connected?this.emitEvent(t):this.receiveBuffer.push(Object.freeze(t))}emitEvent(e){if(this._anyListeners&&this._anyListeners.length){const t=this._anyListeners.slice();for(const s of t)s.apply(this,e)}super.emit.apply(this,e),this._pid&&e.length&&typeof e[e.length-1]=="string"&&(this._lastOffset=e[e.length-1])}ack(e){const t=this;let s=!1;return function(...i){s||(s=!0,t.packet({type:l.ACK,id:e,data:i}))}}onack(e){const t=this.acks[e.id];typeof t=="function"&&(delete this.acks[e.id],t.withError&&e.data.unshift(null),t.apply(this,e.data))}onconnect(e,t){this.id=e,this.recovered=t&&this._pid===t,this._pid=t,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(e=>this.emitEvent(e)),this.receiveBuffer=[],this.sendBuffer.forEach(e=>{this.notifyOutgoingListeners(e),this.packet(e)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(e=>e()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:l.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(e){return this.flags.compress=e,this}get volatile(){return this.flags.volatile=!0,this}timeout(e){return this.flags.timeout=e,this}onAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(e),this}prependAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(e),this}offAny(e){if(!this._anyListeners)return this;if(e){const t=this._anyListeners;for(let s=0;s<t.length;s++)if(e===t[s])return t.splice(s,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(e),this}prependAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(e),this}offAnyOutgoing(e){if(!this._anyOutgoingListeners)return this;if(e){const t=this._anyOutgoingListeners;for(let s=0;s<t.length;s++)if(e===t[s])return t.splice(s,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(e){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const t=this._anyOutgoingListeners.slice();for(const s of t)s.apply(this,e.data)}}}function O(n){n=n||{},this.ms=n.min||100,this.max=n.max||1e4,this.factor=n.factor||2,this.jitter=n.jitter>0&&n.jitter<=1?n.jitter:0,this.attempts=0}O.prototype.duration=function(){var n=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var e=Math.random(),t=Math.floor(e*this.jitter*n);n=(Math.floor(e*10)&1)==0?n-t:n+t}return Math.min(n,this.max)|0};O.prototype.reset=function(){this.attempts=0};O.prototype.setMin=function(n){this.ms=n};O.prototype.setMax=function(n){this.max=n};O.prototype.setJitter=function(n){this.jitter=n};class se extends f{constructor(e,t){var s;super(),this.nsps={},this.subs=[],e&&typeof e=="object"&&(t=e,e=void 0),t=t||{},t.path=t.path||"/socket.io",this.opts=t,W(this,t),this.reconnection(t.reconnection!==!1),this.reconnectionAttempts(t.reconnectionAttempts||1/0),this.reconnectionDelay(t.reconnectionDelay||1e3),this.reconnectionDelayMax(t.reconnectionDelayMax||5e3),this.randomizationFactor((s=t.randomizationFactor)!==null&&s!==void 0?s:.5),this.backoff=new O({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(t.timeout==null?2e4:t.timeout),this._readyState="closed",this.uri=e;const i=t.parser||Ut;this.encoder=new i.Encoder,this.decoder=new i.Decoder,this._autoConnect=t.autoConnect!==!1,this._autoConnect&&this.open()}reconnection(e){return arguments.length?(this._reconnection=!!e,e||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(e){return e===void 0?this._reconnectionAttempts:(this._reconnectionAttempts=e,this)}reconnectionDelay(e){var t;return e===void 0?this._reconnectionDelay:(this._reconnectionDelay=e,(t=this.backoff)===null||t===void 0||t.setMin(e),this)}randomizationFactor(e){var t;return e===void 0?this._randomizationFactor:(this._randomizationFactor=e,(t=this.backoff)===null||t===void 0||t.setJitter(e),this)}reconnectionDelayMax(e){var t;return e===void 0?this._reconnectionDelayMax:(this._reconnectionDelayMax=e,(t=this.backoff)===null||t===void 0||t.setMax(e),this)}timeout(e){return arguments.length?(this._timeout=e,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&this.backoff.attempts===0&&this.reconnect()}open(e){if(~this._readyState.indexOf("open"))return this;this.engine=new Tt(this.uri,this.opts);const t=this.engine,s=this;this._readyState="opening",this.skipReconnect=!1;const i=g(t,"open",function(){s.onopen(),e&&e()}),r=c=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",c),e?e(c):this.maybeReconnectOnOpen()},a=g(t,"error",r);if(this._timeout!==!1){const c=this._timeout,h=this.setTimeoutFn(()=>{i(),r(new Error("timeout")),t.close()},c);this.opts.autoUnref&&h.unref(),this.subs.push(()=>{this.clearTimeoutFn(h)})}return this.subs.push(i),this.subs.push(a),this}connect(e){return this.open(e)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");const e=this.engine;this.subs.push(g(e,"ping",this.onping.bind(this)),g(e,"data",this.ondata.bind(this)),g(e,"error",this.onerror.bind(this)),g(e,"close",this.onclose.bind(this)),g(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(e){try{this.decoder.add(e)}catch(t){this.onclose("parse error",t)}}ondecoded(e){K(()=>{this.emitReserved("packet",e)},this.setTimeoutFn)}onerror(e){this.emitReserved("error",e)}socket(e,t){let s=this.nsps[e];return s?this._autoConnect&&!s.active&&s.connect():(s=new xe(this,e,t),this.nsps[e]=s),s}_destroy(e){const t=Object.keys(this.nsps);for(const s of t)if(this.nsps[s].active)return;this._close()}_packet(e){const t=this.encoder.encode(e);for(let s=0;s<t.length;s++)this.engine.write(t[s],e.options)}cleanup(){this.subs.forEach(e=>e()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(e,t){var s;this.cleanup(),(s=this.engine)===null||s===void 0||s.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",e,t),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const e=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{const t=this.backoff.duration();this._reconnecting=!0;const s=this.setTimeoutFn(()=>{e.skipReconnect||(this.emitReserved("reconnect_attempt",e.backoff.attempts),!e.skipReconnect&&e.open(i=>{i?(e._reconnecting=!1,e.reconnect(),this.emitReserved("reconnect_error",i)):e.onreconnect()}))},t);this.opts.autoUnref&&s.unref(),this.subs.push(()=>{this.clearTimeoutFn(s)})}}onreconnect(){const e=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",e)}}const L={};function H(n,e){typeof n=="object"&&(e=n,n=void 0),e=e||{};const t=At(n,e.path||"/socket.io"),s=t.source,i=t.id,r=t.path,a=L[i]&&r in L[i].nsps,c=e.forceNew||e["force new connection"]||e.multiplex===!1||a;let h;return c?h=new se(s,e):(L[i]||(L[i]=new se(s,e)),h=L[i]),t.query&&!e.query&&(e.query=t.queryKey),h.socket(t.path,e)}Object.assign(H,{Manager:se,Socket:xe,io:H,connect:H});const Mt={class:"socket-container"},Ft={class:"connection-status"},$t={class:"control-panel"},Ht={class:"message-panel"},Kt={class:"message-list"},Wt={class:"message-time"},zt={class:"message-content"},Yt={class:"message-list"},Jt={class:"message-content"},Xt={class:"message-input"},jt={__name:"index",setup(n){const e=d(null),t=d(!1),s=d(""),i=d([]),r=d(""),a=d(""),c=d("");d("");const h=d("中国科学技术大学、中山大学"),y=d([]),p=d({base:{cls:"2026届",col:"安徽建筑大学",ele:"计算机科学与技术",is_kua:"是",tar_code:"080903",tar_get:"网络工程",is_all:"否"},will:{score1:"70",score2:"70",score3:"110",score4:"130"},tar:{tar_area:"A区",tar_school:"中国科学技术大学",school_type:"985"},english:{english1:"120",english2:{et4:"425",et6:"430"},seng:{seng4:"",seng8:""},isto:"",yuda:"",isys:""},math:{math2:"80",math3:"80",math:"80",math4:"80",math5:"80",math6:"130"},remark:"备注信息",school:""}),_=async()=>{const o=await We(p.value);console.log(o.data.string),s.value=o.data.string},A=()=>{console.log(B.value.token);try{e.value=H("wss://wss.lke.cloud.tencent.com",{path:"/v1/qbot/chat/conn/",transports:["websocket"],query:{EIO:4,transport:"websocket"},auth:{token:B.value.token}}),e.value.on("connect",()=>{t.value=!0,b("系统","连接成功"),r.value="测试",Ne(),console.log("Socket连接成功，ID:",e.value.id)}),e.value.on("connect_error",o=>{console.error("连接错误:",o),b("系统",`连接错误: ${o.message}`)}),e.value.on("disconnect",o=>{t.value=!1,b("系统",`断开连接: ${o}`),console.log("Socket断开连接:",o)}),e.value.on("message",o=>{b("服务器",o)}),e.value.on("reply",o=>{console.log("收到回复:",o),o&&o.payload&&o.payload&&!o.payload.is_from_self&&o.payload.content&&(o.payload.request_id!=""?(v.value[o.payload.request_id].str+=o.payload.content,o.payload.is_final&&(v.value[o.payload.request_id].strObj=JSON.parse(v.value[o.payload.request_id].str),b("推荐理由",`${v.value[o.payload.request_id].strObj.reason}`),b("专业的难度分析",`${v.value[o.payload.request_id].strObj.difficulty_analysis}`),b("备考目标建议",`${v.value[o.payload.request_id].strObj.suggest}`))):(h.value+=o.payload.content,b("AI助手",h.value),o.payload.is_final&&(y.value=h.value.split("、"),ce())))})}catch(o){console.error("创建Socket连接失败:",o),b("系统",`创建连接失败: ${o.message}`)}},Ne=()=>{y.value=h.value.split("、"),ce()},v=d({}),ce=async()=>{y.value.map(o=>{p.value.school=o,ze(p.value).then(u=>{console.log(u);let w=ue();v.value[w]={school_name:o,str:"",strObj:"",reqid:w,prompt:u.data.string},Le(v.value[w]),console.log(v.value)}).catch(u=>{console.error("获取报告原因失败:",u)})})},Le=o=>{console.log(o);let u={session_id:a.value,bot_app_key:"zNZkvmYnJtNJEcxEzrcBUAenZsbxkwYyRruQTyOKUCWwajXScCdWieSjPHwRWhsRtkLyestwexqCvKMlIwePSEMsnREIXdbEwPaeEapBkAFCVyiVMilsxqMSCbaiLhst",content:o.prompt,request_id:o.reqid,incremental:!0,streaming_throttle:10,visitor_labels:[]};console.log(u),e.value.emit("send",{payload:u})};d([]);const he=()=>{e.value&&e.value.connected&&e.value.disconnect()},le=()=>{if(!r.value.trim()||!t.value)return;let o={session_id:a.value,bot_app_key:"zNZkvmYnJtNJEcxEzrcBUAenZsbxkwYyRruQTyOKUCWwajXScCdWieSjPHwRWhsRtkLyestwexqCvKMlIwePSEMsnREIXdbEwPaeEapBkAFCVyiVMilsxqMSCbaiLhst",content:s.value,incremental:!0,streaming_throttle:10,visitor_labels:[]};e.value.emit("send",{payload:o}),b("我","测试"),r.value=""},b=(o,u)=>{i.value.push({sender:o,content:`${o}: ${u}`,time:new Date})},qe=()=>{const o=()=>Math.floor((1+Math.random())*65536).toString(16).substring(1);return`${o()}${o()}-${o()}-${o()}-${o()}-${o()}${o()}${o()}`},ue=()=>{const o=Date.now().toString(36),u=Math.random().toString(36).substr(2,6);return`SESS_${o}_${u}`.toUpperCase()};Ue(()=>{a.value=qe(),c.value=ue(),Ie(),_()});const Pe=o=>`${o.getHours().toString().padStart(2,"0")}:${o.getMinutes().toString().padStart(2,"0")}:${o.getSeconds().toString().padStart(2,"0")}`;let B=d({});const Ie=()=>new Promise(async(o,u)=>{let w="";const R=await Ve.get("https://watang.yanqu.cn/addons/fzdc/text/text");console.log(R),R&&R.data.data&&R.data.data.Response&&R.data.data.Response.Token&&(w=R.data.data.Response.Token),B.value={type:5,token:w,access:"ws"},console.log("【init msg----------demoToken---->】",B.value),o(B.value)});return Me(()=>{he()}),(o,u)=>{const w=$e,R=Ke;return N(),x("div",Mt,[k("div",Ft,[u[1]||(u[1]=P(" 连接状态: ")),k("span",{class:Fe({connected:t.value,disconnected:!t.value})},I(t.value?"已连接":"未连接"),3)]),k("div",$t,[D(w,{type:"primary",onClick:A,disabled:t.value},{default:Y(()=>u[2]||(u[2]=[P("连接")])),_:1},8,["disabled"]),D(w,{type:"danger",onClick:he,disabled:!t.value},{default:Y(()=>u[3]||(u[3]=[P("断开连接")])),_:1},8,["disabled"])]),k("div",Ht,[k("div",Kt,[(N(!0),x(fe,null,pe(i.value,(C,z)=>(N(),x("div",{key:z,class:"message-item"},[k("span",Wt,I(Pe(C.time)),1),k("span",zt,I(C.content),1)]))),128))]),k("div",Yt,[(N(!0),x(fe,null,pe(v.value,(C,z)=>(N(),x("div",{key:z,class:"message-item"},[k("span",Jt,I(C.str),1)]))),128))]),k("div",Xt,[D(R,{modelValue:r.value,"onUpdate:modelValue":u[0]||(u[0]=C=>r.value=C),placeholder:"请输入消息",onKeyup:He(le,["enter"])},null,8,["modelValue"]),D(w,{type:"primary",onClick:le,disabled:!t.value},{default:Y(()=>u[4]||(u[4]=[P("发送")])),_:1},8,["disabled"])])])])}}},ns=De(jt,[["__scopeId","data-v-21be20a9"]]);export{ns as default};
