<?php
namespace app\model;

use think\Model;

class Student extends Model
{
    protected $table = 'ba_student';
    protected $pk = 'id';
    protected $autoWriteTimestamp = false;

    /**
     * 性别获取器
     * @param mixed $value
     * @return string
     */
    public function getSexTextAttr($value, $data)
    {
        $status = [
            1 => '男',
            2 => '女',
            3 => '其他'
        ];
        return $status[$data['sex']] ?? '未知';
    }

    /**
     * 是否研究生获取器
     * @param mixed $value
     * @return string
     */
    public function getIsPostgraduateTextAttr($value, $data)
    {
        return $data['is_postgraduate'] ? '是' : '否';
    }

    /**
     * 是否跨专业获取器
     * @param mixed $value
     * @return string
     */
    public function getIsCrossMajorTextAttr($value, $data)
    {
        return $data['is_multi_disciplinary'] ? '是' : '否';
    }
    /**
     * 是否全日制获取器
     * @param mixed $value
     * @return string
     */
    public function getEducationalStyleTextAttr($value, $data)
    {
        return $data['educational_style']=== 1 ? '非全日制' : '全日制';
    }

    /**
     * 标签关联
     * @return \think\model\relation\HasMany
     */
    public function tags()
    {
        return $this->hasMany(StudentTag::class, 'student_id', 'id');
    }

    /**
     * 详细信息关联
     * @return \think\model\relation\HasOne
     */
    public function detail()
    {
        return $this->hasOne(StudentDetail::class, 'student_id', 'id');
    }



    /**
     * 获取学生详情
     * @param Request $request
     * @return array
     */
    public function getDetail($id = '')
    {
        // 获取学生基本信息
        $student = Student::where('id', $id)->where('is_delete', 0)->find();
        if (!$student) {
            return ['code'=> 1,'msg'=>'学生不存在'];
        }

        // 获取学生详细信息
        $detail = StudentDetail::where('student_id', $id)->find();

        // 获取学生标签
        $tagRelations = StudentTag::where('student_id', $id)->select();
        $tagIds = array_column($tagRelations->toArray(), 'tag_id');
        $tags = [];
        if (!empty($tagIds)) {
            $tagList = Tag::whereIn('id', $tagIds)->select();
            foreach ($tagList as $tag) {
                $tags[] = [
                    'value' => $tag['id'],
                    'label' => $tag['name'],
                    'class' => 'tag-' . $tag['color']
                ];
            }
        }

        // 获取本科院校信息
        $undergraduateSchoolName = $student['undergraduate_school_name'];
        if (!empty($student['undergraduate_school_id'])) {
            $school = School::where('id', $student['undergraduate_school_id'])->where('is_delete', 0)->find();
            if ($school) {
                $undergraduateSchoolName = $school['name'];
            }
        }

        // 获取本科专业信息
        $undergraduateMajorName = $student['undergraduate_major_name'];
        if (!empty($student['undergraduate_major_id'])) {
            $major = SchoolMajor::where('id', $student['undergraduate_major_id'])->where('is_delete', 0)->find();
            if ($major) {
                $undergraduateMajorName = $major['major_name'];
            }
        }

        // 获取目标专业信息
        $targetMajorName = $student['target_major_name'];
        if (!empty($student['target_major_id'])) {
            $targetMajor = Major::where('id', $student['target_major_id'])->where('is_delete', 0)->find();
            if ($targetMajor) {
                $targetMajorName = $targetMajor['name'];
            }
        }

        // 处理目标专业多选数据
        $targetMajorIds = $student['target_major_id'] ? explode(',', $student['target_major_id']) : [];
        $targetMajorNames = $targetMajorName ? (is_array($targetMajorName) ? $targetMajorName : explode(',', $targetMajorName)) : [];

        // 构建返回数据
        $result = [
            'id' => $student['id'],
            'name' => $student['name'],
            'sex' => $student['sex'],
            'sexText' => $student['sex_text'],
            'phone' => $student['phone'],
            'teacherPhone' => $student['teacher_phone'] ?? '',
            'undergraduateSchool' => $student['undergraduate_school_id'],
            'undergraduateSchoolName' => $undergraduateSchoolName,
            'undergraduateMajor' => $student['undergraduate_major_id'],
            'undergraduateMajorName' => $undergraduateMajorName,
            'targetMajor' => $targetMajorIds, // 返回数组
            'targetMajorName' => $targetMajorNames, // 返回数组
            'majorCode' => $student['major_code'],
            'isPostgraduate' => (bool)$student['is_postgraduate'],
            'isPostgraduateText' => $student['is_postgraduate_text'],
            'examYear' => $student['exam_year'],
           
            'isMultiDisciplinary' => $student['is_multi_disciplinary']==1,
            'isMultiDisciplinaryText' => $student['is_multi_disciplinary']==1 ? '是' : '否',

            'isCrossMajor' => $student['is_multi_disciplinary']==1,
            'isCrossMajorText' => $student['is_multi_disciplinary']==1 ? '是' : '否',

            'educationalStyle' => $student['educational_style'],
            'educationalStyleText' => $student['educational_style_text'],
            'tags' => $tags
        ];

        // 如果有详细信息，添加到结果中
        if ($detail) {
            // 处理JSON数据
            $undergraduateTranscript = is_string($detail['undergraduate_transcript']) ? json_decode($detail['undergraduate_transcript'], true) : $detail['undergraduate_transcript'];
            $estimatedScores = is_string($detail['estimated_scores']) ? json_decode($detail['estimated_scores'], true) : $detail['estimated_scores'];
            // 处理省份选择 - 确保是字符串再进行分割
            $targetProvinces = [];
            if (!empty($detail['target_provinces'])) {
                if (is_string($detail['target_provinces'])) {
                    $targetProvinces = explode(',', $detail['target_provinces']);
                } elseif (is_array($detail['target_provinces'])) {
                    $targetProvinces = $detail['target_provinces'];
                }
            }

            // 获取梦校信息
            $targetSchoolName = $detail['target_school_name'];
            if (!empty($detail['target_school_id'])) {
                $targetSchool = School::where('id', $detail['target_school_id'])->where('is_delete', 0)->find();
                if ($targetSchool) {
                    $targetSchoolName = $targetSchool['name'];
                }
            }

            $result = array_merge($result, [
                'undergraduateTranscript' => $undergraduateTranscript,
                'englishScore' => $detail['english_score'] ?? '',
                'cet4' => $detail['cet4'] ?? '',
                'cet6' => $detail['cet6'] ?? '',
                'specialEnglishLevel' => $detail['special_english_level'] ?? '',
                'tofelScore' => $detail['tofel_score'] ?? '',
                'ieltsScore' => $detail['ielts_score'] ?? '',
                'englishAbility' => $detail['english_ability'] ?? '',
                'targetRegion' => $detail['target_region'] ? explode(',', $detail['target_region'])  : [],
                'targetProvinces' => $targetProvinces,
                'targetSchool' => $detail['target_school_id'],
                'targetSchoolName' => $targetSchoolName,
                'schoolLevel' => $detail['school_level'] ?? '',
                'referenceBooks' => $detail['reference_books'] ?? '',
                'politics' => $detail['politics'] ?? '',
                'englishType' => $detail['english_type'] ?? '',
                'mathType' => $detail['math_type'] ?? '',
                'englishS'=>$detail['english_s'] ?? '',
                'mathScore'=>$detail['math_score'] ?? '',
                'professionalScore'=>$detail['professional_score'] ?? '',
                'estimatedScores' => $estimatedScores,
                'totalScore' => $detail['total_score'] ?? '',
                'personalNeeds' => $detail['personal_needs'] ?? '',
                'weakModules' => $detail['weak_modules'] ?? ''
            ]);
        }

        return ['code'=> 0,'data'=>$result];
    }

}
