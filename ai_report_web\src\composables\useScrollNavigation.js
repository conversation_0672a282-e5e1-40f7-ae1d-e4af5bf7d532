import { ref, nextTick, watch } from 'vue';

/**
 * 滚动导航composable
 * 用于处理表单滚动时左侧导航的高亮状态同步
 */
export function useScrollNavigation(options = {}) {
  const {
    // 节流时间，默认16ms（60fps）
    throttleDelay = 16,
    // 点击保护时间，默认300ms
    clickProtectionDelay = 300,
    // 滚动阈值，默认60px
    scrollThreshold = 60,
    // section选择器前缀，默认'section-'
    sectionPrefix = 'section-',
    // 是否启用调试日志
    debug = false
  } = options;

  // 状态管理
  const activeSection = ref(0);
  const lastClickTime = ref(0);
  const formContent = ref(null);
  const isInitialized = ref(false);

  // 节流函数
  const throttle = (func, limit) => {
    let lastFunc;
    let lastRan;
    return function(...args) {
      if (!lastRan) {
        func.apply(this, args);
        lastRan = Date.now();
      } else {
        clearTimeout(lastFunc);
        lastFunc = setTimeout(() => {
          if ((Date.now() - lastRan) >= limit) {
            func.apply(this, args);
            lastRan = Date.now();
          }
        }, limit - (Date.now() - lastRan));
      }
    };
  };

  // 日志输出函数
  const log = (...args) => {
    if (debug) {
      console.log('[ScrollNavigation]', ...args);
    }
  };

  // 滚动检测逻辑
  const handleScroll = () => {
    if (!formContent.value) return;

    // 获取所有带有id的部分
    const sections = [];
    let sectionCount = 0;
    
    // 动态查找section元素，直到找不到为止
    while (true) {
      const section = document.getElementById(`${sectionPrefix}${sectionCount}`);
      if (section) {
        sections.push({ index: sectionCount, element: section });
        sectionCount++;
      } else {
        break;
      }
    }

    // 如果没有找到任何部分，则直接返回
    if (sections.length === 0) {
      log("未找到任何section元素");
      return;
    }

    // 检查是否刚刚点击了导航项
    const justClicked = Date.now() - lastClickTime.value < clickProtectionDelay;
    
    if (justClicked) {
      log("刚刚点击了导航项，保持activeSection不变:", activeSection.value);
      return;
    }

    const scrollTop = formContent.value.scrollTop;
    const containerTop = formContent.value.getBoundingClientRect().top;
    
    // 优化的逻辑：更敏感的section检测
    let newActiveSection = 0;
    let minDistance = Infinity;
    
    sections.forEach((section) => {
      const sectionRect = section.element.getBoundingClientRect();
      const sectionTop = sectionRect.top;
      
      // 计算section顶部到容器顶部的距离
      const distanceToTop = Math.abs(sectionTop - containerTop);
      
      // 如果section在视口内且距离顶部最近，则选择它
      if (sectionTop <= containerTop + scrollThreshold && distanceToTop < minDistance) {
        minDistance = distanceToTop;
        newActiveSection = section.index;
      }
    });
    
    // 备用逻辑：如果没有找到合适的section，使用传统的offsetTop方法
    if (minDistance === Infinity) {
      for (let i = sections.length - 1; i >= 0; i--) {
        const section = sections[i];
        if (scrollTop >= section.element.offsetTop - scrollThreshold) {
          newActiveSection = section.index;
          break;
        }
      }
    }

    // 只有当活动部分发生变化时才更新
    if (activeSection.value !== newActiveSection) {
      log("更新activeSection为:", newActiveSection, "从:", activeSection.value);
      activeSection.value = newActiveSection;
    }
  };

  // 创建节流版本的滚动处理函数
  const throttledHandleScroll = throttle(handleScroll, throttleDelay);

  // 滚动到指定部分
  const scrollToSection = (index) => {
    // 记录点击时间
    lastClickTime.value = Date.now();
    log("记录点击时间:", lastClickTime.value);

    // 先设置活动部分
    activeSection.value = index;
    log("设置activeSection为:", index);

    // 延迟一下，确保DOM已经渲染完成
    setTimeout(() => {
      // 使用ID选择器更精确地定位到对应的部分
      const sectionId = `${sectionPrefix}${index}`;
      const section = document.getElementById(sectionId);

      if (section && formContent.value) {
        // 滚动到对应的部分，并添加一些偏移量以获得更好的视觉效果
        formContent.value.scrollTop = section.offsetTop - 20;
        log("滚动到section:", sectionId, "位置:", section.offsetTop - 20);
      } else if (formContent.value) {
        // 如果找不到对应的部分，则使用类选择器作为备选方案
        const sections = document.querySelectorAll(".form-section");
        if (sections[index]) {
          formContent.value.scrollTop = sections[index].offsetTop - 20;
          log("滚动到sections[index]:", index, "位置:", sections[index].offsetTop - 20);
        } else {
          log("未找到对应的section");
        }
      }

      // 再次确认activeSection的值
      activeSection.value = index;
      log("再次确认activeSection为:", index);
    }, 50);
  };

  // 初始化滚动监听
  const initScrollListener = (contentRef) => {
    formContent.value = contentRef;
    
    if (formContent.value && !isInitialized.value) {
      // 添加滚动事件监听器
      formContent.value.addEventListener("scroll", throttledHandleScroll);
      isInitialized.value = true;

      // 重置活动部分为第一个
      activeSection.value = 0;
      log("初始化滚动监听，重置activeSection为0");

      // 延迟一下，确保DOM已经渲染完成
      setTimeout(() => {
        if (formContent.value) {
          // 初始滚动到顶部
          formContent.value.scrollTop = 0;

          // 再次确认activeSection为0
          activeSection.value = 0;
          log("延迟后再次确认activeSection为0");

          // 检查section元素是否存在
          const sections = [];
          let sectionCount = 0;
          while (true) {
            const section = document.getElementById(`${sectionPrefix}${sectionCount}`);
            if (section) {
              sections.push({ index: sectionCount, element: section });
              log(`找到${sectionPrefix}${sectionCount}，位置:`, section.offsetTop);
              sectionCount++;
            } else {
              break;
            }
          }
        }
      }, 300);
    }
  };

  // 销毁滚动监听
  const destroyScrollListener = () => {
    if (formContent.value && isInitialized.value) {
      // 移除滚动事件监听器
      formContent.value.removeEventListener("scroll", throttledHandleScroll);
      isInitialized.value = false;
      log("销毁滚动监听");
    }
  };

  // 监听对话框状态的便捷方法
  const watchDialogVisible = (dialogVisible) => {
    watch(dialogVisible, (val) => {
      if (val) {
        nextTick(() => {
          const contentElement = formContent.value || document.querySelector('.dialog-content');
          if (contentElement) {
            initScrollListener(contentElement);
          }
        });
      } else {
        destroyScrollListener();
      }
    });
  };

  // 返回所有需要的方法和状态
  return {
    // 状态
    activeSection,
    formContent,
    
    // 方法
    scrollToSection,
    initScrollListener,
    destroyScrollListener,
    watchDialogVisible,
    
    // 便捷方法
    setFormContentRef: (ref) => { formContent.value = ref; },
    resetActiveSection: () => { activeSection.value = 0; }
  };
} 