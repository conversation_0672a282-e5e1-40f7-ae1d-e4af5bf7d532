<template>
    <div class="student-list-container">
        <!-- 顶部统计卡片区域 -->
        <div class="stats-cards">
            <div class="stat-card left-card">
                <div class="stat-content">
                    <span class="stat-label">生成报告数：</span>
                    <span class="stat-value">891人次</span>
                </div>
            </div>
            <div class="stat-card right-card">
                <div class="stat-content">
                    <span class="stat-label">容量：</span>
                    <span class="stat-value">1000人次</span>
                </div>
                <div class="warning-text">容量即将消耗完！</div>
            </div>
        </div>

        <!-- 学员搜索区域 -->
        <div class="search-section">
            <div class="section-header">
                <i class="section-icon"></i>
                <span class="section-title">学员搜索</span>
            </div>

            <div class="search-form">
                <div class="form-item">
                    <span class="item-label">姓名</span>
                    <el-input v-model="searchForm.name" placeholder="张三" clearable></el-input>
                </div>
                <div class="form-item">
                    <span class="item-label">本科院校</span>
                    <el-select v-model="searchForm.undergraduate" placeholder="中国科学技术大学" clearable>
                        <el-option label="中国科学技术大学" value="中国科学技术大学"></el-option>
                    </el-select>
                </div>
                <div class="form-item">
                    <span class="item-label">本科专业</span>
                    <el-select v-model="searchForm.major" placeholder="通信工程" clearable>
                        <el-option label="通信工程" value="通信工程"></el-option>
                    </el-select>
                </div>
                <div class="form-item">
                    <span class="item-label">年级</span>
                    <el-select v-model="searchForm.grade" placeholder="2023" clearable>
                        <el-option label="2023" value="2023"></el-option>
                    </el-select>
                </div>
                <el-button type="primary" class="search-button" @click="handleSearch">查询</el-button>
                <span class="grid-display" @click="toggleDisplay">
                    <i class="el-icon-menu"></i>
                </span>
            </div>
        </div>

        <!-- 学员列表区域 -->
        <div class="student-list-section">
            <div class="section-header">
                <i class="section-icon"></i>
                <span class="section-title">学员列表</span>
            </div>

            <el-table :data="tableData" border style="width: 100%" v-loading="loading" class="custom-table">
                <el-table-column prop="index" label="序号" width="60" align="center"></el-table-column>
                <el-table-column prop="name" label="姓名" width="100" align="center"></el-table-column>
                <el-table-column prop="year" label="年份" width="80" align="center"></el-table-column>
                <el-table-column prop="undergraduate" label="本科院校" align="center"></el-table-column>
                <el-table-column prop="major" label="本科专业" align="center"></el-table-column>
                <el-table-column label="操作" width="200" align="center">
                    <template #default="scope">
                        <el-button class="action-button up-button" size="small">上传</el-button>
                        <el-button class="action-button edit-button" size="small">修改</el-button>
                        <el-button class="action-button delete-button" size="small">二维码</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'

// 搜索表单
const searchForm = reactive({
    name: '',
    undergraduate: '',
    major: '',
    grade: ''
})

// 表格数据
const tableData = ref([])
const loading = ref(false)
const total = ref(0)
const pageSize = ref(10)
const currentPage = ref(1)

// 切换显示模式
const displayMode = ref('table')
const toggleDisplay = () => {
    displayMode.value = displayMode.value === 'table' ? 'grid' : 'table'
}

// 获取表格数据
const getList = () => {
    loading.value = true

    // 模拟请求数据
    setTimeout(() => {
        const mockData = []
        for (let i = 1; i <= 12; i++) {
            mockData.push({
                index: i,
                name: `张三星`,
                year: '2023',
                undergraduate: '中国科学技术大学',
                major: '通信与信息技术'
            })
        }

        tableData.value = mockData
        total.value = 50
        loading.value = false
    }, 500)
}

// 搜索
const handleSearch = () => {
    currentPage.value = 1
    getList()
}

// 重置搜索
const resetSearch = () => {
    Object.keys(searchForm).forEach(key => {
        searchForm[key] = ''
    })
    handleSearch()
}

// 页面加载时获取数据
onMounted(() => {
    getList()
})
</script>

<style scoped>
.student-list-container {
    padding: 20px;
}

/* 顶部统计卡片样式 */
.stats-cards {
    display: flex;
    margin-bottom: 20px;
    position: relative;
}

.stat-card {
    flex: 1;
    height: 70px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    padding: 0 20px;
    color: #fff;
    position: relative;
}

.left-card {
    background: linear-gradient(to right, #e0c7ff, #f9f3ff);
    margin-right: 10px;
    background-image: url('@/assets/images/g_list_left_bg.png');
    background-size: cover;
    background-position: right;
    color: #9a5fdf;
}

.right-card {
    background: linear-gradient(to right, #ffd6a5, #fff9f0);
    margin-left: 10px;
    background-image: url('@/assets/images/g_list_right_bg.png');
    background-size: cover;
    background-position: right;
    color: #ffa03e;
    justify-content: space-between;
}

.stat-content {
    display: flex;
    align-items: center;
}

.stat-label {
    font-size: 16px;
    margin-right: 5px;
}

.stat-value {
    font-size: 16px;
    font-weight: bold;
}

.warning-text {
    color: #ff4d4f;
    font-size: 14px;
    margin-right: 20px;
}

/* 搜索区域样式 */
.search-section,
.student-list-section {
    background: #fff;
    border-radius: 8px;
    margin-bottom: 20px;
    padding: 15px 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.section-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.section-icon {
    width: 10px;
    height: 10px;
    background-color: #18b3b3;
    border-radius: 50%;
    margin-right: 8px;
}

.section-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
}

.search-form {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.form-item {
    display: flex;
    align-items: center;
    margin-right: 15px;
    margin-bottom: 10px;
}

.item-label {
    margin-right: 8px;
    white-space: nowrap;
}

.search-button {
    background-color: #18b3b3;
    border-color: #18b3b3;
    margin-left: 10px;
}

.grid-display {
    margin-left: auto;
    cursor: pointer;
    font-size: 20px;
}

/* 表格样式 */
.custom-table {
    margin-top: 15px;
}

.action-button {
    padding: 4px 8px;
    margin: 0 3px;
}

.up-button {
    color: #18b3b3;
    border-color: #18b3b3;
}

.edit-button {
    color: #ffa03e;
    border-color: #ffa03e;
}

.delete-button {
    color: #18b3b3;
    border-color: #18b3b3;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .stats-cards {
        flex-direction: column;
    }

    .stat-card {
        margin: 0 0 10px 0;
    }

    .search-form {
        flex-direction: column;
        align-items: flex-start;
    }

    .form-item {
        width: 100%;
        margin-right: 0;
    }
}
</style>