import{_ as ue}from"./_plugin-vue_export-helper-BCEt7Ct6.js";/* empty css                   *//* empty css                     *//* empty css                 *//* empty css                  */import{r as _,a as G,x as ve,n as fe,c as $,o as u,e as i,w as r,a7 as _e,D as me,k as d,G as P,b as c,R as B,F as H,s as J,q as K,t as x,M as pe,_ as V,j as ge,l as S,P as F,E as he,g as ke,h as Ce,S as O}from"./index-Cu_gl4pu.js";import{g as Te,d as Q,a as ye,e as $e}from"./tag-IZtnXBn1.js";const be={class:"tags-container"},Le={class:"tag-levels-container"},we={class:"level-tabs"},Ee={class:"level-content"},Ie={class:"level-grid"},Be={class:"level-column"},xe={class:"level-items"},Ve=["onClick"],Fe={key:0,class:"item-actions"},De={class:"column-footer"},Se={class:"level-column"},Me={class:"level-items"},Ne=["onClick"],Re={key:0,class:"item-actions"},Ae={class:"column-footer"},qe={class:"level-column"},Ue={class:"level-items"},je=["onClick"],ze={key:0,class:"item-actions"},Ge={class:"column-footer"},Pe={class:"dialog-footer"},He={__name:"tags",setup(Je){const N=_(null),L=_(1),R=["第一","第二","第三"],a=G({first:-1,second:-1,third:-1}),v=G({first:0,second:0,third:0}),A=(l,e,t)=>{l===1?(a.first=e,v.first=t,ae(t),a.third=-1,v.third=0):l===2?(a.second=e,v.second=t,oe(t)):l===3&&(a.third=e,v.third=t)},m=_(0),q=l=>{if(m.value===l)m.value=0;else{let e=!1,t="";if(l===1?(e=a.first>=0,t="第一层级"):l===2?(e=a.second>=0,t="第二层级"):l===3&&(e=a.third>=0,t="第三层级"),!e){d.warning(`请先选择一个${t}标签再进行编辑`);return}m.value=l}},b=_(!1),M=_("add"),T=_(1),I=_(!1),W=_(0),g=G({name:"",level:1,parent_id:0}),X={name:[{required:!0,message:"请输入标签名称",trigger:"blur"}]},w=_([]),h=_([]),p=_([]),U=_(!1),j=l=>{l===L.value?Y(l):L.value=l},Y=l=>{if(M.value="add",T.value=l,b.value=!0,g.name="",g.level=l,l===1)g.parent_id=0;else if(l===2){if(v.first<=0){d.warning("请先选择一个一级标签"),b.value=!1;return}g.parent_id=v.first}else if(l===3){if(v.second<=0){d.warning("请先选择一个二级标签"),b.value=!1;return}g.parent_id=v.second}},z=(l,e)=>{M.value="edit",T.value=l,W.value=e.id,g.name=e.name,b.value=!0},Z=(l,e)=>{O.confirm(`确定要删除标签 "${w.value[l].name}" 吗?`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Q(e).then(t=>{if(t.code===0){w.value.splice(l,1),a.first===l?(a.first=-1,v.first=0,h.value=[],p.value=[]):a.first>l&&a.first--,y.value=y.value.filter(n=>n.parent_id!==e);const o=y.value.filter(n=>n.parent_id===e).map(n=>n.id);k.value=k.value.filter(n=>!o.includes(n.parent_id)),d.success("删除成功")}else d.error(t.msg||"删除失败")}).catch(t=>{console.error(t),d.error("删除失败，请稍后重试")})}).catch(()=>{})},ee=(l,e)=>{O.confirm(`确定要删除标签 "${h.value[l].name}" 吗?`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Q(e).then(t=>{if(t.code===0){h.value.splice(l,1);const o=y.value.findIndex(n=>n.id===e);o!==-1&&y.value.splice(o,1),a.second===l?(a.second=-1,v.second=0,p.value=[]):a.second>l&&a.second--,k.value=k.value.filter(n=>n.parent_id!==e),d.success("删除成功")}else d.error(t.msg||"删除失败")}).catch(t=>{console.error(t),d.error("删除失败，请稍后重试")})}).catch(()=>{})},le=(l,e)=>{O.confirm(`确定要删除标签 "${p.value[l].name}" 吗?`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Q(e).then(t=>{if(t.code===0){p.value.splice(l,1);const o=k.value.findIndex(n=>n.id===e);o!==-1&&k.value.splice(o,1),a.third===l?(a.third=-1,v.third=0):a.third>l&&a.third--,d.success("删除成功")}else d.error(t.msg||"删除失败")}).catch(t=>{console.error(t),d.error("删除失败，请稍后重试")})}).catch(()=>{})},te=async()=>{if(N.value)try{await N.value.validate(),I.value=!0;const l={name:g.name,level:T.value,parent_id:g.parent_id};M.value==="add"?ye(l).then(e=>{if(e.code===0){const t=e.data;T.value===1?w.value.push(t):T.value===2?(y.value.push(t),t.parent_id===v.first&&h.value.push(t)):(k.value.push(t),t.parent_id===v.second&&p.value.push(t)),d.success(`添加${R[T.value-1]}层级标签成功`)}else d.error(e.msg||"添加失败");I.value=!1,b.value=!1}).catch(e=>{console.error(e),d.error("添加失败，请稍后重试"),I.value=!1}):(l.id=W.value,$e(l).then(e=>{if(e.code===0){const t=e.data;if(T.value===1){const o=w.value.findIndex(n=>n.id===t.id);o!==-1&&(w.value[o]=t)}else if(T.value===2){const o=y.value.findIndex(C=>C.id===t.id);o!==-1&&(y.value[o]=t);const n=h.value.findIndex(C=>C.id===t.id);n!==-1&&(h.value[n]=t)}else{const o=k.value.findIndex(C=>C.id===t.id);o!==-1&&(k.value[o]=t);const n=p.value.findIndex(C=>C.id===t.id);n!==-1&&(p.value[n]=t)}d.success(`编辑${R[T.value-1]}层级标签成功`)}else d.error(e.msg||"编辑失败");I.value=!1,b.value=!1}).catch(e=>{console.error(e),d.error("编辑失败，请稍后重试"),I.value=!1}))}catch(l){console.error("表单验证失败",l),I.value=!1}},se=()=>{U.value=!0,Te().then(l=>{l.code===0?(w.value=l.data.first||[],y.value=l.data.second||[],k.value=l.data.third||[],h.value=[],p.value=[]):d.error(l.msg||"获取标签失败"),U.value=!1}).catch(l=>{console.error(l),d.error("获取标签失败，请稍后重试"),U.value=!1})},y=_([]),k=_([]),ae=l=>{l&&(h.value=y.value.filter(e=>e.parent_id===l),p.value=[],a.second=-1,v.second=0,console.log("加载二级标签",l,h.value))},oe=l=>{l&&(p.value=k.value.filter(e=>e.parent_id===l),a.third=-1,v.third=0,console.log("加载三级标签",l,p.value))};return ve(L,l=>{l>1&&w.value.length===0?(d.warning("请先添加第一层级标签"),L.value=1):l>2&&!h.value.length&&(d.warning("请先添加第二层级标签"),L.value=2)}),fe(()=>{se()}),(l,e)=>{const t=P("Edit"),o=pe,n=P("Delete"),C=P("Check"),D=ge,ne=_e,ie=Ce,ce=ke,de=he,re=me;return u(),$("div",be,[i(ne,{class:"tags-card",style:{"border-radius":"14px"}},{default:r(()=>[c("div",Le,[c("div",we,[c("div",{class:B(["tab-item",{active:L.value===1}]),onClick:e[0]||(e[0]=s=>j(1))},e[9]||(e[9]=[c("span",null,"+添加第一层级",-1)]),2),c("div",{class:B(["tab-item",{active:L.value===2}]),onClick:e[1]||(e[1]=s=>j(2))},e[10]||(e[10]=[c("span",null,"+添加第二层级",-1)]),2),c("div",{class:B(["tab-item",{active:L.value===3}]),onClick:e[2]||(e[2]=s=>j(3))},e[11]||(e[11]=[c("span",null,"+添加第三层级",-1)]),2)]),c("div",Ee,[c("div",Ie,[c("div",Be,[c("div",xe,[(u(!0),$(H,null,J(w.value,(s,f)=>(u(),$("div",{class:B(["level-item",{active:a.first===f}]),key:"first-"+s.id,onClick:E=>A(1,f,s.id)},[c("span",null,x(s.name),1),m.value===1&&a.first===f?(u(),$("div",Fe,[i(o,{class:"edit-icon",onClick:V(E=>z(1,s),["stop"])},{default:r(()=>[i(t)]),_:2},1032,["onClick"]),i(o,{class:"delete-icon",onClick:V(E=>Z(f,s.id),["stop"])},{default:r(()=>[i(n)]),_:2},1032,["onClick"])])):K("",!0)],10,Ve))),128))]),c("div",De,[i(D,{class:"edit-btn",onClick:e[3]||(e[3]=s=>q(1))},{default:r(()=>[S(x(m.value===1?"完成":"编辑")+" ",1),i(o,{class:"el-icon--right"},{default:r(()=>[m.value!==1?(u(),F(t,{key:0})):(u(),F(C,{key:1}))]),_:1})]),_:1})])]),c("div",Se,[c("div",Me,[(u(!0),$(H,null,J(h.value,(s,f)=>(u(),$("div",{class:B(["level-item",{active:a.second===f}]),key:"second-"+s.id,onClick:E=>A(2,f,s.id)},[c("span",null,x(s.name),1),m.value===2&&a.second===f?(u(),$("div",Re,[i(o,{class:"edit-icon",onClick:V(E=>z(2,s),["stop"])},{default:r(()=>[i(t)]),_:2},1032,["onClick"]),i(o,{class:"delete-icon",onClick:V(E=>ee(f,s.id),["stop"])},{default:r(()=>[i(n)]),_:2},1032,["onClick"])])):K("",!0)],10,Ne))),128))]),c("div",Ae,[i(D,{class:"edit-btn",onClick:e[4]||(e[4]=s=>q(2))},{default:r(()=>[S(x(m.value===2?"完成":"编辑")+" ",1),i(o,{class:"el-icon--right"},{default:r(()=>[m.value!==2?(u(),F(t,{key:0})):(u(),F(C,{key:1}))]),_:1})]),_:1})])]),c("div",qe,[c("div",Ue,[(u(!0),$(H,null,J(p.value,(s,f)=>(u(),$("div",{class:B(["level-item",{active:a.third===f}]),key:"third-"+s.id,onClick:E=>A(3,f,s.id)},[c("span",null,x(s.name),1),m.value===3&&a.third===f?(u(),$("div",ze,[i(o,{class:"edit-icon",onClick:V(E=>z(3,s),["stop"])},{default:r(()=>[i(t)]),_:2},1032,["onClick"]),i(o,{class:"delete-icon",onClick:V(E=>le(f,s.id),["stop"])},{default:r(()=>[i(n)]),_:2},1032,["onClick"])])):K("",!0)],10,je))),128))]),c("div",Ge,[i(D,{class:"edit-btn",onClick:e[5]||(e[5]=s=>q(3))},{default:r(()=>[S(x(m.value===3?"完成":"编辑")+" ",1),i(o,{class:"el-icon--right"},{default:r(()=>[m.value!==3?(u(),F(t,{key:0})):(u(),F(C,{key:1}))]),_:1})]),_:1})])])])])])]),_:1}),i(re,{modelValue:b.value,"onUpdate:modelValue":e[8]||(e[8]=s=>b.value=s),title:`${M.value==="add"?"添加":"编辑"}${R[T.value-1]}层级`,width:"500px"},{footer:r(()=>[c("span",Pe,[i(D,{onClick:e[7]||(e[7]=s=>b.value=!1)},{default:r(()=>e[12]||(e[12]=[S("取消")])),_:1}),i(D,{type:"primary",style:{"background-color":"#1bb394"},onClick:te,loading:I.value},{default:r(()=>e[13]||(e[13]=[S("确定")])),_:1},8,["loading"])])]),default:r(()=>[i(de,{model:g,"label-width":"100px",rules:X,ref_key:"tagFormRef",ref:N},{default:r(()=>[i(ce,{label:"标签名称",prop:"name"},{default:r(()=>[i(ie,{modelValue:g.name,"onUpdate:modelValue":e[6]||(e[6]=s=>g.name=s),placeholder:"请输入标签名称"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},el=ue(He,[["__scopeId","data-v-1ea86277"]]);export{el as default};
