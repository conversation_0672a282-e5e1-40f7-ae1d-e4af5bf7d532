// 学校和专业相关类型定义

export interface School {
  id: number;
  name: string;
  code?: string;
  province?: string;
  city?: string;
  level?: string;
  type?: string;
  [key: string]: any;
}

export interface Major {
  id: number;
  name: string;
  code?: string;
  school_id?: number;
  school_name?: string;
  category?: string;
  subcategory?: string;
  [key: string]: any;
}

export interface SchoolInfo {
  id: number;
  school_name: string;
  province: string;
  area: string;
  college: string;
  major_name: string;
  first_level_code: string;
  xueke_name: string;
  second_level_name: string;
  school_type: string;
  study_type: string;
  warning_level: string;
  must_reach_score: number;
  score_diff: number;
  ranking: string;
  admission: string;
  course_suggestion: string;
  detail_url: string;
  [key: string]: any;
}
