<template>
  <div class="student-list-container">
    <!-- 顶部统计卡片区域 -->
    <div class="stats-cards">
      <div class="stat-card left-card">
        <div class="stat-content">
          <span class="stat-label">生成报告数：</span>
          <span class="stat-value">{{ reportCount }}人次</span>
        </div>
      </div>
      <div class="stat-card right-card">
        <div class="stat-content">
          <span class="stat-label">容量：</span>
          <span class="stat-value">1000人次</span>
        </div>
      </div>
      <div class="tip-card"></div>
    </div>

    <!-- 学员搜索区域 -->
    <div class="search-section">
      <div class="section-header">
        <img src="@/assets/images/tag.png" alt="标签图标" />
        <span class="section-title">学员搜索</span>
      </div>

      <div class="search-form">
        <div class="form-item">
          <span class="item-label">姓名</span>
          <el-autocomplete
            v-model="searchForm.name"
            :fetch-suggestions="queryStudentNames"
            placeholder="张三"
            clearable
            :trigger-on-focus="false"
            @select="handleNameSelect"
            class="centered-placeholder"
          ></el-autocomplete>
        </div>
        <div class="form-item">
          <span class="item-label">本科院校</span>
          <el-select
            v-model="searchForm.school_name"
            placeholder="请输入本科院校"
            clearable
            filterable
            remote
            :remote-method="querySchools"
            :loading="schoolLoading"
          >
            <el-option
              v-for="item in schoolOptions"
              :key="item.value"
              :label="item.label"
              :value="item.label"
            ></el-option>
          </el-select>
        </div>
        <div class="form-item">
          <span class="item-label">本科专业</span>

          <el-select
            v-model="searchForm.major_name"
            placeholder="输入关键字搜索专业"
            filterable
            remote
            reserve-keyword
            :remote-method="searchRemoteMajor"
            :loading="majorSearchLoading"
            class="full-width"
            :disabled="!searchForm.school_name"
            @focus="handleMajorFocus"
            popper-class="major-select-dropdown"
          >
            <el-option
              v-for="item in majorOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
            <div
              v-if="majorHasMore && !majorSearchLoading"
              class="load-more-option"
              @click="loadMoreMajors"
            >
              <el-button
                style="color: #1bb394; padding-left: 20px"
                type="text"
                size="small"
                >点击加载更多...</el-button
              >
            </div>
            <div
              v-if="majorSearchLoading && majorCurrentPage > 1"
              class="loading-option"
            >
              <el-icon>
                <Loading />
              </el-icon>
              加载中...
            </div>
          </el-select>

          <!-- <el-select
            v-model="searchForm.major_name"
            placeholder="通信工程"
            clearable
            filterable
            remote
            :remote-method="queryMajors"
            :loading="majorLoading"
          >
            <el-option
              v-for="item in majorOptions"
              :key="item.value"
              :label="item.label"
              :value="item.label"
            ></el-option>
          </el-select> -->
        </div>
        <div class="form-item grade_class">
          <span class="item-label">年级</span>
          <el-select v-model="searchForm.class" placeholder="2023" clearable>
            <el-option
              v-for="item in years"
              :key="item.value"
              :label="item.label"
              :value="item.label"
            ></el-option>
          </el-select>
        </div>
        <div class="form-item" style="margin-left: 100px">
          <el-button type="primary" class="search-button" @click="handleSearch"
            >查询</el-button
          >
          <el-button plain class="reset-button" @click="resetSearch"
            >重置</el-button
          >
        </div>

        <span class="grid-display" @click="toggleDisplay">
          <i class="el-icon-menu"></i>
        </span>
      </div>
    </div>

    <!-- 学员列表区域 -->
    <div class="student-list-section">
      <div class="section-header">
        <img src="@/assets/images/tag.png" alt="标签图标" />
        <span class="section-title">学员列表</span>
      </div>

      <el-table
        :data="tableData"
        style="width: 90%"
        v-loading="loading"
        class="custom-table"
      >
        <el-table-column
          prop="id"
          label="序号"
          width="160"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="name"
          label="姓名"
          width="180"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="year"
          label="年份"
          width="180"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="undergraduate"
          label="本科院校"
          width="240"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="major"
          label="本科专业"
          align="center"
          width="240"
        ></el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <span
              v-if="scope.row.pdf_url"
              @click="handlePreview(scope.row.pdf_url)"
              class="action-button up-button"
              size="small"
              >下载</span
            >
            <!-- <span class="action-button up-button" size="small">上传</span> -->
            <span
              class="action-button edit-button"
              @click="toReport(scope.row.id, scope.row.student_id)"
              size="small"
              >修改</span
            >
            <span  class="action-button delete-button"  @click="generateQRCode(scope.row)" size="small">二维码</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout=" total, prev, pager, next, jumper"
          :total="total"
          :page-size="pageSize"
          :current-page="currentPage"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </div>
    <el-dialog
    v-model="dialogVisible"
    title="择校报告二维码"
    width="500"
    :before-close="handleClose"
  >
    <div id="canvas">
      <img :src="url" alt="">
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
       
      </div>
    </template>
  </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { searchSchool, searchMajor, searchTargetMajor } from "@/api/school";
import { getStudentList } from "@/api/student";
import { reportList, getExamYears, getReportNum,generateCode } from "@/api/report"; // 导入 getReportNum
import { ElMessage } from "element-plus";
import { useRouter } from "vue-router";
const router = useRouter();
// 搜索表单
const searchForm = reactive({
  name: "",
  school_name: "",
  major_name: "",
  class: "",
  page: 1,
  limit: 7,
});

// 表格数据
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const pageSize = ref(10);
const currentPage = ref(1);
const reportCount = ref(0); // 新增响应式变量存储报告数
const url = ref('');
//获取生成报告数
const fetchReportCount = async () => {
  try {
    const res = await getReportNum();
    if (res.code === 0 && res.data) {
      reportCount.value = res.data.count;
    }
  } catch (error) {
    console.error("获取报告数量失败:", error);
    ElMessage.error("获取报告数量失败");
  }
};

const qrRef = ref(null);

const dialogVisible = ref(false); 
const generateQRCode = async (row) => { 
  dialogVisible.value = true;
  if(row.idcode != '' && row.idcode != null) {
    url.value = 'https://oa.yanqux.com/qrcode/build?text=https://report.yanqukaoyan.com/sreport/'+row.idcode
  } else  {
    const { code,data } = await generateCode({report_id: row.id});
    console.log("生成二维码成功:", data);
    if(code == 0) {
      url.value = 'https://oa.yanqux.com/qrcode/build?text=https://report.yanqukaoyan.com/sreport/'+data
    } else {
      ElMessage.error(data.msg);
    }
  }
 };

const handleClose = () => {
  dialogVisible.value = false;
}
// 学生姓名搜索相关
const queryStudentNames = (queryString, callback) => {
  if (queryString.length < 1) {
    callback([]);
    return;
  }
  getStudentList({ name: queryString, page: 1, limit: 50 }).then((res) => {
    const results = res.data
      .map((item) => ({
        value: item.name,
      }))
      .filter((item) => item.value.includes(queryString));
    callback(results);
  });

  // 从后端获取学生姓名数据
  // 这里应该调用学生搜索API，但为了简化，我们使用模拟数据
  // // 实际项目中应该替换为 getStudentList API 调用
  // setTimeout(() => {
  //   const results = [
  //     { value: "张三" },
  //     { value: "张三丰" },
  //     { value: "张三星" },
  //     { value: "李四" },
  //     { value: "王五" },
  //   ].filter((item) => item.value.includes(queryString));

  // }, 200);
};

const handleNameSelect = (item) => {
  searchForm.name = item.value;
};

// 学校搜索相关
const schoolOptions = ref([]);
const schoolLoading = ref(false);
const schoolSearchCache = ref({}); // 添加缓存

const querySchools = async (query) => {
  if (query.length < 1) {
    schoolOptions.value = [];
    return;
  }

  // 检查缓存中是否已有该查询结果
  if (schoolSearchCache.value[query]) {
    schoolOptions.value = schoolSearchCache.value[query];
    return;
  }

  schoolLoading.value = true;
  try {
    // 调用API获取学校数据
    const res = await searchSchool(query);
    console.log("搜索学校结果:", res);

    if (res && res.code === 0 && res.data) {
      // 转换为下拉菜单需要的格式
      const options = res.data.map((item) => ({
        value: item.id.toString(),
        label: item.name,
      }));

      schoolOptions.value = options;
      // 添加到缓存
      schoolSearchCache.value[query] = options;
    } else {
      // 模拟数据，实际项目中应该使用API返回的数据
      const mockData = [
        { value: "1", label: "中国科学技术大学" },
        { value: "2", label: "北京大学" },
        { value: "3", label: "清华大学" },
        { value: "4", label: "复旦大学" },
        { value: "5", label: "南京大学" },
      ].filter((item) => item.label.includes(query));

      schoolOptions.value = mockData;
      // 添加到缓存
      schoolSearchCache.value[query] = mockData;
    }
  } catch (error) {
    console.error("获取学校数据失败:", error);
    // 使用模拟数据作为备选
    const mockData = [
      { value: "1", label: "中国科学技术大学" },
      { value: "2", label: "北京大学" },
      { value: "3", label: "清华大学" },
    ].filter((item) => item.label.includes(query));

    schoolOptions.value = mockData;
    // 添加到缓存
    schoolSearchCache.value[query] = mockData;
  } finally {
    schoolLoading.value = false;
  }
};

// 专业搜索相关
const majorOptions = ref([]);
const majorLoading = ref(false);
const majorSearchCache = ref({}); // 添加缓存
const majorCurrentPage = ref(1);
const majorHasMore = ref(false);
const majorIsInitialized = ref(false);
const majorSearchLoading = ref(false);
const majorSearchTimeout = ref(null);
const lastMajorQuery = ref("");

const handleMajorFocus = () => {
  // 检查是否已选择本科院校
  if (!searchForm.school_name) {
    ElMessage.warning("请先选择本科院校");
    return;
  }

  // 如果还未初始化或者选项为空，则加载第一页数据
  if (!majorIsInitialized.value || majorOptions.value.length === 0) {
    majorCurrentPage.value = 1;
    searchRemoteMajor(""); // 传入空字符串，加载该院校所有专业
  }
};

const loadMoreMajors = () => {
  if (majorHasMore.value && !majorSearchLoading.value) {
    majorCurrentPage.value += 1;
    searchRemoteMajor(lastMajorQuery.value);
  }
};
// 搜索本科专业
const searchRemoteMajor = (query) => {
  if (!searchForm.school_name) {
    ElMessage.warning("请先选择本科院校");
    return;
  }

  // 如果是新的搜索查询，重置分页状态
  if (query !== lastMajorQuery.value) {
    majorCurrentPage.value = 1;
    majorOptions.value = [];
    majorHasMore.value = false;
    lastMajorQuery.value = query;
  }

  majorSearchLoading.value = true;

  // 生成缓存键，包含学校ID、查询词和页码
  const cacheKey = `${searchForm.school_name}_${query || ""}_${
    majorCurrentPage.value
  }`;

  // 检查缓存中是否已有该查询结果
  if (majorSearchCache[cacheKey]) {
    const cachedResult = majorSearchCache[cacheKey];
    if (majorCurrentPage.value === 1) {
      majorOptions.value = cachedResult.data;
    } else {
      majorOptions.value = [...majorOptions.value, ...cachedResult.data];
    }
    majorHasMore.value = cachedResult.hasMore;
    majorSearchLoading.value = false;
    return;
  }

  // 延迟300ms，避免频繁请求
  clearTimeout(majorSearchTimeout.value);

  const searchSchoolId = schoolOptions.value.find(
    (item) => item.label === searchForm.school_name
  ).value;
  majorSearchTimeout.value = setTimeout(() => {
    searchMajor(searchSchoolId, query || "", majorCurrentPage.value)
      .then((res) => {
        if (res.code === 0 && res.data) {
          const options = res.data.map((item) => ({
            value: item.id.toString(),
            label: item.major_name,
          }));

          // 处理分页数据
          if (majorCurrentPage.value === 1) {
            majorOptions.value = options;
          } else {
            majorOptions.value = [...majorOptions.value, ...options];
          }

          // 更新分页状态
          majorHasMore.value = res.pagination ? res.pagination.has_more : false;

          // 添加到缓存
          majorSearchCache[cacheKey] = {
            data: options,
            hasMore: majorHasMore.value,
          };
        } else {
          if (majorCurrentPage.value === 1) {
            majorOptions.value = [];
          }
          majorHasMore.value = false;
        }
      })
      .catch(() => {
        if (majorCurrentPage.value === 1) {
          majorOptions.value = [];
        }
        majorHasMore.value = false;
      })
      .finally(() => {
        majorSearchLoading.value = false;
        majorIsInitialized.value = true;
      });
  }, 300);
};

const queryMajors = async (query) => {
  if (query.length < 1) {
    majorOptions.value = [];
    return;
  }

  // 检查是否选择了学校
  if (!searchForm.undergraduate) {
    // 如果没有选择学校，使用通用专业搜索
    const cacheKey = `general_${query}`;

    // 检查缓存中是否已有该查询结果
    if (majorSearchCache.value[cacheKey]) {
      majorOptions.value = majorSearchCache.value[cacheKey];
      return;
    }

    majorLoading.value = true;
    try {
      // 调用API获取专业数据 - 使用searchTargetMajor而不是searchMajor
      const res = await searchTargetMajor(query);
      console.log("搜索专业结果:", res);

      if (res && res.code === 0 && res.data) {
        // 转换为下拉菜单需要的格式
        const options = res.data.map((item) => ({
          value: item.code || item.id.toString(),
          label: item.name,
        }));

        majorOptions.value = options;
        // 添加到缓存
        majorSearchCache.value[cacheKey] = options;
      } else {
        // 模拟数据
        const mockData = [
          { value: "1", label: "通信工程" },
          { value: "2", label: "计算机科学与技术" },
          { value: "3", label: "软件工程" },
          { value: "4", label: "电子信息工程" },
          { value: "5", label: "人工智能" },
        ].filter((item) => item.label.includes(query));

        majorOptions.value = mockData;
        // 添加到缓存
        majorSearchCache.value[cacheKey] = mockData;
      }
    } catch (error) {
      console.error("获取专业数据失败:", error);
      // 使用模拟数据作为备选
      const mockData = [
        { value: "1", label: "通信工程" },
        { value: "2", label: "计算机科学与技术" },
        { value: "3", label: "软件工程" },
      ].filter((item) => item.label.includes(query));

      majorOptions.value = mockData;
      // 添加到缓存
      majorSearchCache.value[cacheKey] = mockData;
    } finally {
      majorLoading.value = false;
    }
  } else {
    // 如果选择了学校，使用学校专业搜索
    const schoolId = searchForm.undergraduate;
    const cacheKey = `${schoolId}_${query}`;

    // 检查缓存中是否已有该查询结果
    if (majorSearchCache.value[cacheKey]) {
      majorOptions.value = majorSearchCache.value[cacheKey];
      return;
    }

    majorLoading.value = true;
    try {
      // 调用API获取专业数据 - 使用正确的参数
      const res = await searchMajor(schoolId, query);
      console.log("搜索学校专业结果:", res);

      if (res && res.code === 0 && res.data) {
        // 转换为下拉菜单需要的格式
        const options = res.data.map((item) => ({
          value: item.id.toString(),
          label: item.major_name || item.name,
        }));

        majorOptions.value = options;
        // 添加到缓存
        majorSearchCache.value[cacheKey] = options;
      } else {
        // 模拟数据
        const mockData = [
          { value: "1", label: "通信工程" },
          { value: "2", label: "计算机科学与技术" },
          { value: "3", label: "软件工程" },
          { value: "4", label: "电子信息工程" },
          { value: "5", label: "人工智能" },
        ].filter((item) => item.label.includes(query));

        majorOptions.value = mockData;
        // 添加到缓存
        majorSearchCache.value[cacheKey] = mockData;
      }
    } catch (error) {
      console.error("获取专业数据失败:", error);
      // 使用模拟数据作为备选
      const mockData = [
        { value: "1", label: "通信工程" },
        { value: "2", label: "计算机科学与技术" },
        { value: "3", label: "软件工程" },
      ].filter((item) => item.label.includes(query));

      majorOptions.value = mockData;
      // 添加到缓存
      majorSearchCache.value[cacheKey] = mockData;
    } finally {
      majorLoading.value = false;
    }
  }
};

// 切换显示模式
const displayMode = ref("table");
const toggleDisplay = () => {
  displayMode.value = displayMode.value === "table" ? "grid" : "table";
};

// 获取表格数据
const getList = async () => {
  loading.value = true;
  await reportList(searchForm).then((res) => {
    tableData.value = res.data.data;
    total.value = res.data.total;
    loading.value = false;
  });
};

// 搜索
const handleSearch = () => {
  currentPage.value = 1;
  getList();
};

// 重置搜索
const resetSearch = () => {
  // 清空搜索表单
  searchForm.page = 1;
  searchForm.name = "";
  searchForm.school_name = "";
  searchForm.major_name = "";
  searchForm.class = "";
  // 清空下拉选项
  schoolOptions.value = [];
  majorOptions.value = [];

  // 重新搜索
  handleSearch();
};
const toReport = (id, student_id) => {
  console.log(id);
  // 或者使用命名路由
  router.push({
    name: "editReport",
    params: { id: id, student_id: student_id },
  });
};

// 页面加载时获取数据
onMounted(() => {
  getList();
  getYears();
  fetchReportCount(); // 调用获取报告数的方法
});
const years = ref([]);
const getYears = () => {
  getExamYears().then((res) => {
    console.log(res);
    years.value = res.data;
  });
};
// 页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  searchForm.page = page;
  getList();
};
</script>

<style scoped>
.student-list-container {
  background-color: #fff;
  padding: 20px 50px;
  border-radius: 16px 16px 0px 0px;
  height: calc(100vh - 74px);
}

:deep(.el-table th) {
  background-color: #f6f7fb;
  font-weight: bold;
  font-size: 13px;
  color: #313131;
  height: 60px;
  border: none;
}

:deep(.el-table td) {
  height: 52px;
  background: #ffffff;
  border: none;
  font-weight: 400;
  font-size: 13px;
  color: #313131;
  border-bottom: 1px solid #ececec;
}

/* 顶部统计卡片样式 */
.stats-cards {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  position: relative;
}

.stat-card {
  width: 558px;
  height: 120px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
  color: #fff;
  position: relative;
}

.left-card {
  background: linear-gradient(to right, #e0c7ff, #f9f3ff);
  margin-right: 10px;
  background-image: url("@/assets/images/g_list_left_bg.png");
  background-size: contain;
  background-repeat: no-repeat;
  color: #9a5fdf;
}

.right-card {
  background: linear-gradient(to right, #ffd6a5, #fff9f0);
  margin-left: 10px;
  background-image: url("@/assets/images/g_list_right_bg.png");
  background-size: contain;
  background-repeat: no-repeat;
  color: #ffa03e;
  margin-left: 54px;
}

.tip-card {
  width: 190px;
  height: 30px;
  background: linear-gradient(to right, #ffd6a5, #fff9f0);
  margin-left: 10px;
  background-image: url("@/assets/images/list-tip.png");
  background-size: cover;
  background-position: right;
  color: #ffa03e;
  justify-content: space-between;
}

.stat-content {
  display: flex;
  align-items: center;
  font-weight: bold;
  font-size: 22px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

/* :deep(.el-input__inner) {
  text-align: center !important;
} */
.stat-label {
  margin-right: 5px;
}

.warning-text {
  flex: 1;
  color: #ff4d4f;
  font-size: 14px;
  margin-right: 20px;
}

/* 搜索区域样式 */
.search-section,
.student-list-section {
  background: #fff;
  border-radius: 8px;
  padding-top: 0;
  margin-bottom: 15px;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;

  img {
    width: 26px;
    height: 22px;
    margin-right: 6px;
  }
}

.section-icon {
  width: 10px;
  height: 10px;
  background-color: #18b3b3;
  border-radius: 50%;
  margin-right: 8px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.search-form {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 30px;
  margin-bottom: 10px;
}

:deep(.el-select__wrapper) {
  border-radius: 8px;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #1bb394 inset !important;
}

:deep(.el-select__wrapper.is-focused) {
  box-shadow: 0 0 0 1px #1bb394 inset !important;
}

.grade_class {
  width: 200px;
}

.item-label {
  margin-right: 8px;
  white-space: nowrap;
}

.search-button {
  background: #1bb394;
  border-radius: 10px;
  width: 94px;
  height: 36px;
}

.reset-button {
  border-radius: 10px;
  width: 94px;
  height: 36px;
}

.grid-display {
  margin-left: auto;
  cursor: pointer;
  font-size: 20px;
}

/* 表格样式 */
.custom-table {
  margin-top: 15px;
}

.action-button {
  width: 24px;
  height: 16px;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 12px;
  color: #1bb394;
  text-align: center;
  font-style: normal;
  line-height: 16px;
  text-transform: none;
  padding: 0 10px;
  cursor: pointer;
}

.up-button {
  color: #18b3b3;
  border: none;
}

.edit-button {
  color: #18b3b3;
  border: none;
}

.delete-button {
  color: #18b3b3;
  border: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-cards {
    flex-direction: column;
  }

  .stat-card {
    margin: 0 0 10px 0;
  }

  .search-form {
    flex-direction: column;
    align-items: flex-start;
  }

  .form-item {
    width: 100%;
    margin-right: 0;
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

:deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
  background-color: #1bb394;
}
#canvas {
  height:400px;
  text-align: center;
  margin-top:40px;
}
</style>
