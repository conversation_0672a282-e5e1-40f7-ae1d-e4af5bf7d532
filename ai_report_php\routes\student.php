<?php
use Webman\Route;

// 学生管理相关接口
Route::group("/api", function () {
    // 学生管理
    Route::get('/student/list', [app\controller\StudentController::class, 'getList']);
    Route::get('/student/detail', [app\controller\StudentController::class, 'getDetail']);
    Route::post('/student/add', [app\controller\StudentController::class, 'add']);
    Route::post('/student/edit', [app\controller\StudentController::class, 'edit']);
    Route::get('/student/delete', [app\controller\StudentController::class, 'delete']);
    Route::post('/student/update-teacher', [app\controller\StudentController::class, 'updateTeacher']);
    Route::post('/student/toggle-ai-overlay', [app\controller\StudentController::class, 'toggleAIOverlay']);

    // 学科分类相关接口
    Route::get('/student/discipline-categories', [app\controller\StudentController::class, 'getDisciplineCategories']);
    Route::get('/student/first-level-disciplines', [app\controller\StudentController::class, 'getFirstLevelDisciplines']);
    Route::get('/student/second-level-disciplines', [app\controller\StudentController::class, 'getSecondLevelDisciplines']);

    // 教师管理
    Route::get('/user/teacher-list', [app\controller\UserController::class, 'getTeacherList']);
    Route::get('/user/search-teacher', [app\controller\UserController::class, 'searchTeacher']);
})->middleware([app\middleware\JwtMiddleware::class]); 