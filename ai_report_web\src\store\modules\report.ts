import { defineStore } from "pinia"
import type { AiRecommendationResult } from "@/types/index"

export const useUserReportStore = defineStore("userReport", {
  state: () => ({
    reportData: null as AiRecommendationResult | null
  }),
  
  getters: {
    hasReportData: (state) => state.reportData !== null,
    getReportData: (state) => state.reportData
  },
  
  actions: {
    setReportData(data: AiRecommendationResult) {
      this.reportData = data
    },
    
    clearReportData() {
      this.reportData = null
    },
    
    updateReportData(updates: Partial<AiRecommendationResult>) {
      if (this.reportData) {
        this.reportData = { ...this.reportData, ...updates }
      }
    }
  }
})