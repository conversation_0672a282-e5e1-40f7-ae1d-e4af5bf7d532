<?php

namespace app\controller;
use app\common\utils\CosUploader;
use Exception;
use support\Log;
use support\Request;

class ConfigController
{
    protected $noNeedAuth = ['uploadFile'];
    public function getCosKey( Request $request){
        $filename = $request->get('filename');
        try{
         $data =  CosUploader::getKeyAndCredentials($filename);
        }catch (Exception $e){
            Log::error($e->getMessage());
            return json(['msg' => '获取临时密钥失败','code'=>1]);
        }
         return json(['msg' => '获取临时密钥成功','code'=>0, 'data'=>$data]);
    }

    public function uploadFile(Request $request){
        $file = $request->file('file');
        var_dump($file);
    }
}