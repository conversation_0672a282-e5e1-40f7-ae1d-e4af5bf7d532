import axios, { AxiosInstance,AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import { ElMessage } from 'element-plus'
import { ApiResponse } from '@/types'

// 创建axios实例
const service: AxiosInstance = axios.create({
  baseURL: '/api', // API基础URL
  timeout: 180000 // 请求超时时间
})

// 请求拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig): InternalAxiosRequestConfig => {
    // 在请求头中添加token
    const token = localStorage.getItem('token')
    console.log('当前token:', token)
    if (token && config.headers) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    console.log('发送请求:', config.method, config.url, config.params || config.data)
    return config
  },
  (error) => {
    console.error('请求错误', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>): any => {
    const res = response.data as ApiResponse<any>
    console.log('收到响应:', response.config.url, res)

    // 如果返回的状态码不是0，则判断为错误
    if (res.code !== 0) {
      console.error('API错误响应:', response.config.url, res)
      ElMessage({
        message: res.msg || '请求失败',
        type: 'error',
        duration: 5 * 1000
      })

      // 401: 未登录或token过期
      if (res.code === 401) {
        console.warn('登录已过期，准备跳转到登录页')
        // 清除本地token和用户信息
        localStorage.removeItem('token')
        localStorage.removeItem('userInfo')

        // 跳转到登录页
        window.location.href = '/login'
      }

      return Promise.reject(new Error(res.msg || '请求失败'))
    } else {
      return res
    }
  },
  (error) => {
    console.error('响应错误', error)

    // 处理网络错误
    let message = '网络错误，请稍后重试'
    if (error.response) {
      switch (error.response.status) {
        case 401:
          message = '未登录或登录已过期，请重新登录'
          // 清除本地token和用户信息
          localStorage.removeItem('token')
          localStorage.removeItem('userInfo')
          // 跳转到登录页
          window.location.href = '/login'
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求的资源不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = `请求失败: ${error.response.status}`
      }
    } else if (error.request) {
      message = '服务器未响应'
    }

    ElMessage({
      message,
      type: 'error',
      duration: 5 * 1000
    })

    return Promise.reject(error)
  }
)

export default service
