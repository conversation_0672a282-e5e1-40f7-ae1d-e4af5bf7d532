import{_ as ke}from"./_plugin-vue_export-helper-BCEt7Ct6.js";import{s as Ie,a as <PERSON>,b as Se}from"./school-BRpLdP4V.js";/* empty css                   *//* empty css                     *//* empty css                        */import{b as ha,g as Ve,a as Sa,f as ja,u as _a}from"./student-Db39rDv4.js";/* empty css                 *//* empty css                  */import{S as ba}from"./TeacherSelector.vue_vue_type_style_index_0_scoped_0ca4774d_lang-CADRJ9Cy.js";import{m as Ma,r as s,x as be,n as De,P as H,o as S,w as m,D as $e,a4 as Ca,k as p,b as i,p as Pe,e as c,M as ze,N as ya,a5 as Na,h as Oe,v as Re,a1 as Be,a3 as Fe,j as xe,_ as Ta,l as z,a2 as Ue,a as Ae,c as $,a6 as wa,Z as Ia,f as La,G as Va,F as q,s as ee,$ as Aa,q as je,t as P,R as Ee,E as Ea,y as _e}from"./index-Cu_gl4pu.js";import{g as ka}from"./tag-IZtnXBn1.js";import"./province-Do8ZIeJL.js";const Da={class:"teacher-selector"},$a={class:"search-container"},Pa={class:"pagination-container"},za={class:"dialog-footer"},Oa=Ma({__name:"TeacherSelector",props:{visible:{type:Boolean,default:!1}},emits:["update:visible","select"],setup(Me,{emit:ae}){const se=Me,ie=ae,y=s(se.visible);be(()=>se.visible,v=>{y.value=v}),be(y,v=>{ie("update:visible",v),v&&(_(),O())});const K=s(""),N=s(1),j=s(20),te=s(0),Q=s([]),F=s(!1),T=s(""),G=s(null),O=async()=>{F.value=!0;try{const v=await Ca({page:N.value,limit:j.value,keyword:K.value});v.code===0?(Q.value=v.data,te.value=v.data.length||0):p.error(v.msg||"获取老师列表失败")}catch(v){console.error("获取老师列表失败",v),p.error("获取老师列表失败")}finally{F.value=!1}},_=()=>{K.value="",N.value=1,T.value="",G.value=null},f=()=>{N.value=1,O()},Y=v=>{N.value=v,O()},x=v=>{j.value=v,N.value=1,O()},Z=({row:v})=>T.value===v.id?"selected-row":"",C=v=>{T.value=v.id,G.value=v},ce=()=>{G.value&&(ie("select",G.value),y.value=!1)},oe=()=>{_()};return De(()=>{y.value&&O()}),(v,h)=>{const de=ze,le=Oe,V=Fe,A=xe,ue=Be,re=Ue,u=$e,W=Re;return S(),H(u,{modelValue:y.value,"onUpdate:modelValue":h[2]||(h[2]=E=>y.value=E),title:"选择服务老师",width:"800px","close-on-click-modal":!1,onClosed:oe},{footer:m(()=>[i("span",za,[c(A,{onClick:h[1]||(h[1]=E=>y.value=!1)},{default:m(()=>h[4]||(h[4]=[z("取消")])),_:1}),c(A,{type:"primary",onClick:ce,disabled:!T.value},{default:m(()=>h[5]||(h[5]=[z("确定")])),_:1},8,["disabled"])])]),default:m(()=>[i("div",Da,[i("div",$a,[c(le,{modelValue:K.value,"onUpdate:modelValue":h[0]||(h[0]=E=>K.value=E),placeholder:"搜索老师昵称或手机号",clearable:"",onInput:f},{prefix:m(()=>[c(de,null,{default:m(()=>[c(ya(Na))]),_:1})]),_:1},8,["modelValue"])]),Pe((S(),H(ue,{data:Q.value,border:"",style:{width:"100%"},"highlight-current-row":"",onRowClick:C,"row-class-name":Z},{default:m(()=>[c(V,{type:"index",width:"50",align:"center"}),c(V,{prop:"nickname",label:"老师昵称",align:"center"}),c(V,{prop:"username",label:"用户名",align:"center"}),c(V,{prop:"mobile",label:"手机号码",align:"center"}),c(V,{label:"操作",width:"120",align:"center"},{default:m(E=>[c(A,{type:"primary",size:"small",onClick:Ta(ne=>C(E.row),["stop"])},{default:m(()=>h[3]||(h[3]=[z(" 选择 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[W,F.value]]),i("div",Pa,[c(re,{background:"",layout:"total, sizes, prev, pager, next, jumper",total:te.value,"page-size":j.value,"page-sizes":[10,20,50,100],"current-page":N.value,onCurrentChange:Y,onSizeChange:x},null,8,["total","page-size","current-page"])])])]),_:1},8,["modelValue"])}}}),Ra=ke(Oa,[["__scopeId","data-v-0ca4774d"]]),Ba={class:"student-list-container"},Fa={class:"filter-section"},xa={class:"filter-form-container"},Ua={class:"filter-form"},Ja={class:"form-row"},Ha={class:"form-item"},Ka={class:"form-item"},Ga={class:"form-item"},Qa={class:"form-row"},Ya={class:"form-item"},Za={class:"form-item"},Wa={key:1,class:"loading-option"},Xa={class:"form-item"},qa={class:"form-item"},et={class:"filter-buttons"},at={class:"student-list-section"},tt={class:"list-header"},ot={class:"tag-container"},lt=["onClick"],rt=["onClick"],nt={class:"pagination-container"},st={class:"dialog-container"},it={class:"dialog-sidebar"},ct=["onClick"],dt={class:"sidebar-item-number"},ut={class:"sidebar-item-text"},gt={class:"dialog-footer"},mt={__name:"list",setup(Me){La();const ae=s(null),se=[{value:"985",label:"985",class:"tag-orange"},{value:"211",label:"211",class:"tag-purple"},{value:"double",label:"双一流",class:"tag-green"},{value:"important",label:"重点",class:"tag-blue"}],ie=s([]),y=s(""),K=s([]),N=s(!1),j=s([]),te=s(null),Q={},F=s(!1),T=s([]),G=s(null),O={},_=s(!1),f=s([]),Y=s(null),x={};s(!1),s([]),s(null);const Z=s(!1),C=s([]),ce=s(null),oe={},v=s(!1),h=s([]),de=s(null),le={},V=s(!1),A=s([]),ue=s(null),re={},u=Ae({name:"",phone:"",teacher:"",school:"",undergraduate:"",undergraduateMajor:"",targetSchool:"",targetMajor:"",tagId:[]}),W=s([]),E=s(!1),ne=s(0),Ce=s(8),ge=s(1),U=s(!1),b=s("add"),R=s(!1),ye=s(!1),Je=s([]),He=a=>{if(!a||!a.id){p.warning("请选择有效的服务老师");return}const e=Je.value.map(t=>t.id);_a({studentIds:e,teacherId:a.id}).then(t=>{t.code===0?(p.success("更换服务老师成功"),X()):p.error(t.msg||"更换服务老师失败")}).catch(t=>{console.error("更换服务老师失败",t),p.error("更换服务老师失败，请稍后重试")})},w=s(null),ve=["个人基础信息","本科成绩情况","英语基础","目标院校倾向","考研成绩预估"],B=s(0);s([]);const fe=s(0),Ke=(a,e)=>{let t,o;return function(...n){o?(clearTimeout(t),t=setTimeout(()=>{Date.now()-o>=e&&(a.apply(this,n),o=Date.now())},e-(Date.now()-o))):(a.apply(this,n),o=Date.now())}},Ge=a=>{fe.value=Date.now(),console.log("记录点击时间:",fe.value),B.value=a,console.log("设置activeSection为:",a),setTimeout(()=>{const e=`section-${a}`,t=document.getElementById(e);if(t)w.value.scrollTop=t.offsetTop-20,console.log("滚动到section:",e,"位置:",t.offsetTop-20);else{const o=document.querySelectorAll(".form-section");o[a]?(w.value.scrollTop=o[a].offsetTop-20,console.log("滚动到sections[index]:",a,"位置:",o[a].offsetTop-20)):console.log("未找到对应的section")}B.value=a,console.log("再次确认activeSection为:",a)},50)},Ne=Ke(()=>{if(!w.value)return;const a=[];for(let g=0;g<ve.length;g++){const I=document.getElementById(`section-${g}`);I&&a.push({index:g,element:I})}if(a.length===0){console.log("未找到任何section元素");return}if(Date.now()-fe.value<300){console.log("刚刚点击了导航项，保持activeSection不变:",B.value);return}const t=w.value.scrollTop,o=w.value.getBoundingClientRect().top;let n=0,d=1/0;if(a.forEach(g=>{const L=g.element.getBoundingClientRect().top,M=Math.abs(L-o);L<=o+60&&M<d&&(d=M,n=g.index)}),d===1/0)for(let g=a.length-1;g>=0;g--){const I=a[g];if(t>=I.element.offsetTop-60){n=I.index;break}}B.value!==n&&(console.log("更新activeSection为:",n,"从:",B.value),B.value=n)},16);be(U,a=>{a?_e(()=>{w.value&&(w.value.addEventListener("scroll",Ne),B.value=0,console.log("对话框打开，重置activeSection为0"),setTimeout(()=>{w.value.scrollTop=0,B.value=0,console.log("延迟后再次确认activeSection为0");for(let e=0;e<ve.length;e++){const t=document.getElementById(`section-${e}`);t?console.log(`找到section-${e}，位置:`,t.offsetTop):console.log(`未找到section-${e}`)}},300))}):w.value&&w.value.removeEventListener("scroll",Ne)});const l=Ae({id:"",name:"",sex:"",phone:"",teacherPhone:"",undergraduateSchool:"",undergraduateMajor:"",disciplineCategory:null,firstLevelDiscipline:null,targetMajor:"",majorCode:"",isPostgraduate:!1,examYear:"",isMultiDisciplinary:"",undergraduateTranscript:[{id:1,title:"高数(上)期末考试",score:""},{id:2,title:"高数(下)期末考试",score:""},{id:3,title:"积分论期末考试",score:""},{id:4,title:"线性代数期末考试",score:""}],englishScore:"",cet4:"",cet6:"",tofelScore:"",ieltsScore:"",englishAbility:"",targetRegion:"",targetProvinces:[],targetSchool:"",schoolLevel:"",politics:"",englishType:"",mathType:"",englishS:0,mathScore:0,professionalScore:0,totalScore:"",personalNeeds:"",tags:[]}),Qe={name:[{required:!0,message:"请输入学员姓名",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号码",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],undergraduateSchool:[{required:!0,message:"请选择本科院校",trigger:"change"}],undergraduateMajor:[{required:!0,message:"请选择本科专业",trigger:"change"}],targetMajor:[{required:!0,message:"请选择目标专业",trigger:"change"}]},X=()=>{E.value=!0;const a={page:ge.value,limit:Ce.value,name:u.name,phone:u.phone,teacherId:u.teacher,school:u.school,undergraduate:u.undergraduate,undergraduateMajor:u.undergraduateMajor,targetSchool:u.targetSchool,targetMajor:u.targetMajor};if(u.tagId&&u.tagId.length>0){const e=u.tagId.map(t=>Array.isArray(t)?t[t.length-1]:t);a.tagId=e}console.log("发送获取学生列表请求，参数:",a),ha(a).then(e=>{console.log("获取学生列表响应:",e),e.code===0?(e.data&&Array.isArray(e.data)&&e.data.forEach(t=>{t.tags&&Array.isArray(t.tags)&&t.tags.forEach(o=>{!o.label&&o.name&&(o.label=o.name)})}),W.value=e.data,ne.value=e.total):(p.error(e.msg||"获取学生列表失败"),W.value=[],ne.value=0)}).catch(e=>{console.error("获取学生列表失败",e),p.error("获取学生列表失败，请稍后重试"),W.value=[],ne.value=0}).finally(()=>{E.value=!1})},Te=["tag-orange","tag-purple","tag-green","tag-blue","tag-pink"],pe=()=>{const a=Math.floor(Math.random()*Te.length);return Te[a]},Ye=a=>a==="1"||a===1?"男":a==="2"||a===2?"女":a==="3"||a===3?"其他":"",Ze=a=>pe(),We=()=>{ka().then(a=>{if(a.code===0){const e=a.data.first||[],t=a.data.second||[],o=a.data.third||[];t.forEach(n=>{n.class=pe()}),o.forEach(n=>{n.class=pe()}),ie.value=[...e,...t,...o],K.value=e.map(n=>{const d=t.filter(g=>g.parent_id===n.id).map(g=>{const I=o.filter(L=>L.parent_id===g.id).map(L=>({id:L.id,name:L.name,color:L.color||n.color,class:L.class}));return{id:g.id,name:g.name,color:g.color||n.color,class:g.class,children:I.length>0?I:void 0}});return{id:n.id,name:n.name,color:n.color,children:d.length>0?d:void 0}})}else p.error(a.msg||"获取标签失败")}).catch(a=>{console.error("获取标签失败",a),p.error("获取标签失败，请稍后重试")})};De(()=>{X(),We()});const Xe=async a=>{b.value="edit",R.value=!0;try{const e=await Ve(a.id);if(e.code===0){if(Object.keys(l).forEach(t=>{t in e.data&&(t==="tags"?(l[t]=e.data[t].map(o=>{const n=o.value||o;return parseInt(n,10)}).filter(o=>!isNaN(o)&&o>0),console.log("编辑时处理后的标签ID:",l[t])):l[t]=e.data[t]===null?"":e.data[t])}),l.undergraduateSchool&&(l.undergraduateSchool&&e.data.undergraduateSchoolName&&(j.value=[{value:l.undergraduateSchool,label:e.data.undergraduateSchoolName}]),l.undergraduateMajor&&e.data.undergraduateMajorName?(f.value=[{value:l.undergraduateMajor,label:e.data.undergraduateMajorName}],l.undergraduateMajorName=e.data.undergraduateMajorName,console.log("预加载本科专业:",{id:l.undergraduateMajor,name:e.data.undergraduateMajorName})):me("")),console.log("原始majorCode:",e.data.majorCode),e.data.majorCode&&(l.majorCode=e.data.majorCode),l.targetMajor&&e.data.targetMajorName){const t=typeof l.targetMajor=="string"?l.targetMajor.split(",").filter(d=>d.trim()):Array.isArray(l.targetMajor)?l.targetMajor:[l.targetMajor],o=Array.isArray(e.data.targetMajorName)?e.data.targetMajorName:typeof e.data.targetMajorName=="string"?e.data.targetMajorName.split(",").filter(d=>d.trim()):[e.data.targetMajorName],n=l.majorCode?l.majorCode.split(",").filter(d=>d.trim()):[];l.targetMajorName=o,l.targetMajor=t,C.value=t.map((d,g)=>({value:d,label:o[g]||`专业ID: ${d}`,code:n[g]||"",id:d,major_code:n[g]||""})),console.log("预加载目标专业(详情):",{ids:t,names:o,codes:n,majorCode:l.majorCode})}l.englishS=e.data.englishS,l.mathScore=e.data.mathScore,l.professionalScore=e.data.professionalScore,l.targetSchool&&e.data.targetSchoolName&&(l.targetSchoolName=e.data.targetSchoolName,setTimeout(()=>{const t=document.querySelector("student-basic");if(t&&t.__vueParentComponent){const o=t.__vueParentComponent.ctx;o&&o.dreamSchoolOptions&&(o.dreamSchoolOptions=[{value:l.targetSchool,label:e.data.targetSchoolName}])}},100)),l.targetProvinces&&(console.log("编辑时的省份数据:",l.targetProvinces),_e(()=>{console.log("编辑时设置省份选择:",l.targetProvinces)})),l.region=l.targetRegion||[],U.value=!0}else p.error(e.msg||"获取学生详情失败")}catch(e){console.error("获取学生详情失败",e),p.error("获取学生详情失败，请稍后重试")}finally{R.value=!1}},qe=async a=>{b.value="detail",R.value=!0;try{const e=await Ve(a.id);if(e.code===0){if(Object.keys(l).forEach(t=>{t in e.data&&(t==="tags"?(l[t]=e.data[t].map(o=>{const n=o.value||o;return parseInt(n,10)}).filter(o=>!isNaN(o)&&o>0),console.log("详情时处理后的标签ID:",l[t])):l[t]=e.data[t]===null?"":e.data[t])}),l.undergraduateSchool&&(l.undergraduateSchool&&e.data.undergraduateSchoolName&&(j.value=[{value:l.undergraduateSchool,label:e.data.undergraduateSchoolName}]),l.undergraduateMajor&&e.data.undergraduateMajorName?(f.value=[{value:l.undergraduateMajor,label:e.data.undergraduateMajorName}],l.undergraduateMajorName=e.data.undergraduateMajorName,console.log("预加载本科专业(详情):",{id:l.undergraduateMajor,name:e.data.undergraduateMajorName})):me("")),console.log("编辑模式原始majorCode:",e.data.majorCode),e.data.majorCode&&(l.majorCode=e.data.majorCode),l.targetMajor&&e.data.targetMajorName){const t=typeof l.targetMajor=="string"?l.targetMajor.split(",").filter(d=>d.trim()):Array.isArray(l.targetMajor)?l.targetMajor:[l.targetMajor],o=Array.isArray(e.data.targetMajorName)?e.data.targetMajorName:typeof e.data.targetMajorName=="string"?e.data.targetMajorName.split(",").filter(d=>d.trim()):[e.data.targetMajorName],n=l.majorCode?l.majorCode.split(",").filter(d=>d.trim()):[];l.targetMajorName=o,l.targetMajor=t,C.value=t.map((d,g)=>({value:d,label:o[g]||`专业ID: ${d}`,code:n[g]||"",id:d,major_code:n[g]||""})),console.log("预加载目标专业(编辑):",{ids:t,names:o,codes:n,majorCode:l.majorCode})}l.targetSchool&&e.data.targetSchoolName&&(l.targetSchoolName=e.data.targetSchoolName,setTimeout(()=>{const t=document.querySelector("student-basic");if(t&&t.__vueParentComponent){const o=t.__vueParentComponent.ctx;o&&o.dreamSchoolOptions&&(o.dreamSchoolOptions=[{value:l.targetSchool,label:e.data.targetSchoolName}])}},100)),l.targetProvinces&&(console.log("详情视图的省份数据:",l.targetProvinces),_e(()=>{console.log("详情视图设置省份选择:",l.targetProvinces)})),U.value=!0}else p.error(e.msg||"获取学生详情失败")}catch(e){console.error("获取学生详情失败",e),p.error("获取学生详情失败，请稍后重试")}finally{R.value=!1}},ea=()=>{b.value="add",Object.keys(l).forEach(a=>{a==="tags"||a==="targetProvinces"?l[a]=[]:a==="mathScores"?l[a]=[{id:1,title:"高数(上)期末考试",score:""},{id:2,title:"高数(下)期末考试",score:""},{id:3,title:"积分论期末考试",score:""},{id:4,title:"线性代数期末考试",score:""}]:a==="specializedCourses"?l[a]=[{id:1,title:"专业课一",name:"",score:""},{id:2,title:"专业课二",name:"",score:""},{id:3,title:"专业课三",name:"",score:""}]:a==="estimatedScores"?l[a]=[{id:1,title:"专业课一",name:"",score:""},{id:2,title:"专业课二",name:"",score:""},{id:3,title:"专业课三",name:"",score:""}]:l[a]=""}),j.value=[],f.value=[],C.value=[],U.value=!0},aa=async()=>{if(ae.value)try{await ae.value.validate(),R.value=!0;const a={...l,mathScoresJson:"",specializedCoursesJson:"",estimatedScoresJson:""};if((!a.mathScores||!Array.isArray(a.mathScores))&&(a.mathScores=[]),(!a.specializedCourses||!Array.isArray(a.specializedCourses))&&(a.specializedCourses=[]),(!a.estimatedScores||!Array.isArray(a.estimatedScores))&&(a.estimatedScores=[]),(!a.targetProvinces||!Array.isArray(a.targetProvinces))&&(a.targetProvinces=[]),(a.targetMajor==="0"||a.targetMajor===0)&&a.majorCode&&(console.log("提交前：目标专业ID为0，使用专业代码替代:",a.majorCode),a.targetMajor=a.majorCode),!a.tags||!Array.isArray(a.tags)?a.tags=[]:(a.tags=a.tags.map(t=>parseInt(t,10)).filter(t=>!isNaN(t)&&t>0),console.log("处理后的标签ID:",a.tags)),a.mathScoresJson=JSON.stringify(a.mathScores),a.specializedCoursesJson=JSON.stringify(a.specializedCourses),a.undergraduateSchool){const t=j.value.find(o=>o.value===a.undergraduateSchool);a.undergraduateSchoolName=t?t.label:""}if(a.undergraduateMajor){const t=f.value.find(o=>o.value===a.undergraduateMajor);a.undergraduateMajorName=t?t.label:""}if(a.targetMajor){const t=C.value.find(o=>o.value===a.targetMajor);a.targetMajorName=t?t.label:""}if(a.targetSchool){const t=document.querySelector("student-basic");if(t&&t.__vueParentComponent){const o=t.__vueParentComponent.ctx;if(o&&o.dreamSchoolOptions){const n=o.dreamSchoolOptions.find(d=>d.value===a.targetSchool);a.targetSchoolName=n?n.label:""}}}let e;if(b.value==="add")e=Sa(a);else if(b.value==="edit")e=ja(a);else{R.value=!1;return}e.then(t=>{t.code===0?(p.success(b.value==="add"?"添加成功":"更新成功"),U.value=!1,X()):p.error(t.msg||(b.value==="add"?"添加失败":"更新失败"))}).catch(t=>{console.error(b.value==="add"?"添加失败":"更新失败",t),p.error(b.value==="add"?"添加失败，请稍后重试":"更新失败，请稍后重试")}).finally(()=>{R.value=!1})}catch(a){console.error("表单验证失败",a),R.value=!1}},ta=a=>{if(!a){j.value=[];return}if(N.value=!0,console.log("搜索本科院校:",a),Q[a]){j.value=Q[a],N.value=!1;return}clearTimeout(te.value),te.value=setTimeout(()=>{Se(a).then(e=>{if(console.log("搜索本科院校结果:",e),e.code===0&&e.data){const t=e.data.map(o=>({value:o.id.toString(),label:o.name}));j.value=t,Q[a]=t}else j.value=[]}).catch(e=>{console.error("获取学校列表失败",e),j.value=[]}).finally(()=>{N.value=!1})},300)},oa=a=>{if(!a){T.value=[];return}if(F.value=!0,console.log("列表筛选搜索本科院校:",a),O[a]){T.value=O[a],F.value=!1;return}clearTimeout(G.value),G.value=setTimeout(()=>{Se(a).then(e=>{if(console.log("列表筛选搜索本科院校结果:",e),e.code===0&&e.data){const t=e.data.map(o=>({value:o.id.toString(),label:o.name}));T.value=t,O[a]=t}else T.value=[]}).catch(e=>{console.error("获取学校列表失败",e),T.value=[]}).finally(()=>{F.value=!1})},300)},k=s(1),J=s(!1),we=s(!1),la=()=>{if(!u.undergraduate){p.warning("请先选择本科院校");return}(!we.value||f.value.length===0)&&(k.value=1,he(""))},he=a=>{if(!u.undergraduate){p.warning("请先选择本科院校");return}a!==y.value&&(k.value=1,f.value=[],J.value=!1,y.value=a),_.value=!0;const e=`${u.undergraduate}_${a||""}_${k.value}`;if(x[e]){const t=x[e];k.value===1?f.value=t.data:f.value=[...f.value,...t.data],J.value=t.hasMore,_.value=!1;return}clearTimeout(Y.value),Y.value=setTimeout(()=>{Le(u.undergraduate,a||"",k.value).then(t=>{if(t.code===0&&t.data){const o=t.data.map(n=>({value:n.id.toString(),label:n.major_name}));k.value===1?f.value=o:f.value=[...f.value,...o],J.value=t.pagination?t.pagination.has_more:!1,x[e]={data:o,hasMore:J.value}}else k.value===1&&(f.value=[]),J.value=!1}).catch(()=>{k.value===1&&(f.value=[]),J.value=!1}).finally(()=>{_.value=!1,we.value=!0})},300)},me=a=>{if(!l.undergraduate){p.warning("请先选择本科院校");return}_.value=!0,console.log("搜索本科专业:",l.undergraduateSchool,a);const e=`${l.undergraduateSchool}_${a}`;if(x[e]){f.value=x[e],_.value=!1;return}clearTimeout(Y.value),Y.value=setTimeout(()=>{Le(l.undergraduateSchool,a||"").then(t=>{if(console.log("搜索本科专业结果:",t),t.code===0&&t.data){const o=t.data.map(n=>({value:n.id.toString(),label:n.major_name||n.name}));f.value=o,x[e]=o}else f.value=[]}).catch(t=>{console.error("获取专业列表失败",t),f.value=[]}).finally(()=>{_.value=!1})},300)},ra=a=>{if(!a){C.value=[];return}if(Z.value=!0,console.log("搜索目标专业:",a),oe[a]){C.value=oe[a],Z.value=!1;return}clearTimeout(ce.value),ce.value=setTimeout(()=>{Ie(a).then(e=>{if(console.log("搜索目标专业结果:",e),e.code===0&&e.data){const t=e.data.map(o=>{var d;const n=((d=o.id)==null?void 0:d.toString())||o.code;return{value:n,label:o.name,code:o.code,id:n}});C.value=t,oe[a]=t,console.log("处理后的目标专业选项:",t)}else C.value=[]}).catch(e=>{console.error("获取目标专业列表失败",e),C.value=[]}).finally(()=>{Z.value=!1})},300)},na=a=>{if(!a){h.value=[];return}if(v.value=!0,console.log("列表筛选搜索目标院校:",a),le[a]){h.value=le[a],v.value=!1;return}clearTimeout(de.value),de.value=setTimeout(()=>{Se(a).then(e=>{if(console.log("列表筛选搜索目标院校结果:",e),e.code===0&&e.data){const t=e.data.map(o=>({value:o.id.toString(),label:o.name}));h.value=t,le[a]=t}else h.value=[]}).catch(e=>{console.error("获取目标院校列表失败",e),h.value=[]}).finally(()=>{v.value=!1})},300)},sa=a=>{if(!a){A.value=[];return}if(V.value=!0,console.log("列表筛选搜索目标专业:",a),re[a]){A.value=re[a],V.value=!1;return}clearTimeout(ue.value),ue.value=setTimeout(()=>{Ie(a).then(e=>{if(console.log("列表筛选搜索目标专业结果:",e),e.code===0&&e.data){const t=e.data.map(o=>{var d;const n=((d=o.id)==null?void 0:d.toString())||o.code;return{value:n,label:o.name,code:o.code,id:n}});A.value=t,re[a]=t}else A.value=[]}).catch(e=>{console.error("获取目标专业列表失败",e),A.value=[]}).finally(()=>{V.value=!1})},300)},ia=()=>{J.value&&!_.value&&(k.value+=1,he(y.value))},ca=()=>{l.undergraduateMajor="",f.value=[],l.undergraduateSchool&&me("")},da=()=>{Object.keys(u).forEach(a=>{a==="tagId"?u[a]=[]:u[a]=""}),ge.value=1,X()},ua=a=>{ge.value=a,X()};return(a,e)=>{const t=Oe,o=wa,n=Aa,d=Ia,g=xe,I=Va("Loading"),L=ze,M=Fe,ga=Be,ma=Ue,va=Ea,fa=$e,pa=Re;return S(),$("div",Ba,[i("div",Fa,[e[21]||(e[21]=i("div",{class:"filter-header"},[i("div",{class:"filter-icon"}),i("div",{class:"filter-title"},"筛选区")],-1)),i("div",xa,[i("div",Ua,[i("div",Ja,[i("div",Ha,[e[10]||(e[10]=i("div",{class:"item-label"},"学员姓名",-1)),c(t,{modelValue:u.name,"onUpdate:modelValue":e[0]||(e[0]=r=>u.name=r),placeholder:"请输入学员姓名"},null,8,["modelValue"])]),i("div",Ka,[e[11]||(e[11]=i("div",{class:"item-label"},"联系方式",-1)),c(t,{modelValue:u.phone,"onUpdate:modelValue":e[1]||(e[1]=r=>u.phone=r),placeholder:"请输入联系方式"},null,8,["modelValue"])]),i("div",Ga,[e[12]||(e[12]=i("div",{class:"item-label"},"标签筛选",-1)),c(o,{modelValue:u.tagId,"onUpdate:modelValue":e[2]||(e[2]=r=>u.tagId=r),options:K.value,placeholder:"请选择标签",clearable:"",filterable:"",multiple:"","collapse-tags":"",props:{checkStrictly:!0,value:"id",label:"name",children:"children",expandTrigger:"hover"}},null,8,["modelValue","options"])])]),i("div",Qa,[i("div",Ya,[e[13]||(e[13]=i("div",{class:"item-label"},"本科院校",-1)),c(d,{modelValue:u.undergraduate,"onUpdate:modelValue":e[3]||(e[3]=r=>u.undergraduate=r),placeholder:"输入关键字搜索院校",filterable:"",remote:"","reserve-keyword":"","remote-method":oa,loading:F.value,clearable:""},{default:m(()=>[(S(!0),$(q,null,ee(T.value,r=>(S(),H(n,{key:r.value,label:r.label,value:r.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),i("div",Za,[e[16]||(e[16]=i("div",{class:"item-label"},"本科专业",-1)),c(d,{modelValue:u.undergraduateMajor,"onUpdate:modelValue":e[4]||(e[4]=r=>u.undergraduateMajor=r),placeholder:"输入关键字搜索专业",filterable:"",remote:"","reserve-keyword":"","remote-method":he,loading:_.value,class:"full-width",disabled:!u.undergraduate,onFocus:la,"popper-class":"major-select-dropdown"},{default:m(()=>[(S(!0),$(q,null,ee(f.value,r=>(S(),H(n,{key:r.value,label:r.label,value:r.value},null,8,["label","value"]))),128)),J.value&&!_.value?(S(),$("div",{key:0,class:"load-more-option",onClick:ia},[c(g,{style:{color:"#1bb394","padding-left":"20px"},type:"text",size:"small"},{default:m(()=>e[14]||(e[14]=[z("点击加载更多...")])),_:1})])):je("",!0),_.value&&k.value>1?(S(),$("div",Wa,[c(L,null,{default:m(()=>[c(I)]),_:1}),e[15]||(e[15]=z(" 加载中... "))])):je("",!0)]),_:1},8,["modelValue","loading","disabled"])]),i("div",Xa,[e[17]||(e[17]=i("div",{class:"item-label"},"目标院校",-1)),c(d,{modelValue:u.targetSchool,"onUpdate:modelValue":e[5]||(e[5]=r=>u.targetSchool=r),placeholder:"输入关键字搜索院校",filterable:"",remote:"","reserve-keyword":"","remote-method":na,loading:v.value,clearable:""},{default:m(()=>[(S(!0),$(q,null,ee(h.value,r=>(S(),H(n,{key:r.value,label:r.label,value:r.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),i("div",qa,[e[18]||(e[18]=i("div",{class:"item-label"},"目标专业",-1)),c(d,{modelValue:u.targetMajor,"onUpdate:modelValue":e[6]||(e[6]=r=>u.targetMajor=r),placeholder:"请输入关键字搜索专业",filterable:"",remote:"","reserve-keyword":"","remote-method":sa,loading:V.value,clearable:"",style:{width:"100%"}},{default:m(()=>[(S(!0),$(q,null,ee(A.value,r=>(S(),H(n,{key:r.value,label:r.label,value:r.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])])])]),i("div",et,[c(g,{type:"primary",class:"filter-button",onClick:X},{default:m(()=>e[19]||(e[19]=[z("查询")])),_:1}),c(g,{class:"filter-button",onClick:da},{default:m(()=>e[20]||(e[20]=[z("重置")])),_:1})])])]),i("div",at,[i("div",tt,[e[23]||(e[23]=i("div",{class:"list-header-content"},[i("div",{class:"list-icon"}),i("div",{class:"list-title"},"学生列表")],-1)),c(g,{type:"primary",class:"add-student-button",onClick:ea},{default:m(()=>e[22]||(e[22]=[z("添加学员")])),_:1})]),Pe((S(),H(ga,{data:W.value,style:{width:"100%"},class:"custom-table"},{default:m(()=>[c(M,{label:"序号",type:"index",width:"60",align:"center"}),c(M,{prop:"name",label:"姓名",width:"120",align:"center"}),c(M,{label:"性别",width:"60",align:"center"},{default:m(r=>[i("span",null,P(Ye(r.row.sex)),1)]),_:1}),c(M,{prop:"phone",label:"联系方式",width:"130",align:"center"}),c(M,{label:"服务老师",width:"120",align:"center"},{default:m(r=>[i("span",null,P(r.row.teacherName||"未分配"),1)]),_:1}),c(M,{label:"本科院校",align:"center"},{default:m(r=>[i("span",null,P(r.row.undergraduateSchoolName||"未填写"),1)]),_:1}),c(M,{label:"本科专业",align:"center"},{default:m(r=>[i("span",null,P(r.row.undergraduateMajorName||"未填写"),1)]),_:1}),c(M,{label:"目标院校",align:"center"},{default:m(r=>[i("span",null,P(r.row.targetSchoolName||"未填写"),1)]),_:1}),c(M,{label:"目标专业",align:"center"},{default:m(r=>[i("span",null,P(r.row.targetMajorName||"未填写"),1)]),_:1}),c(M,{label:"标签",align:"center"},{default:m(r=>[i("div",ot,[(S(!0),$(q,null,ee(r.row.tags,D=>(S(),$("span",{key:D.value,class:Ee([Ze(D),"tag"])},P(D.label),3))),128))])]),_:1}),c(M,{label:"操作",width:"160",align:"center"},{default:m(r=>[i("span",{onClick:D=>Xe(r.row),class:"edit-button"},"编辑",8,lt),i("span",{onClick:D=>qe(r.row),class:"evaluate-button"},"详情",8,rt)]),_:1})]),_:1},8,["data"])),[[pa,E.value]]),i("div",nt,[c(ma,{background:"",layout:"total,  prev, pager, next, jumper",total:ne.value,"page-size":Ce.value,"current-page":ge.value,onCurrentChange:ua},null,8,["total","page-size","current-page"])])]),c(fa,{modelValue:U.value,"onUpdate:modelValue":e[8]||(e[8]=r=>U.value=r),title:b.value==="add"?"添加学员":b.value==="edit"?"编辑学员":"学员详情",width:"90%",top:"5vh","close-on-click-modal":!1,"destroy-on-close":!0,class:"student-dialog"},{footer:m(()=>[i("span",gt,[c(g,{onClick:e[7]||(e[7]=r=>U.value=!1)},{default:m(()=>[z(P(b.value==="detail"?"关闭":"取消"),1)]),_:1}),b.value!=="detail"?(S(),H(g,{key:0,type:"primary",onClick:aa,loading:R.value,class:"action-button"},{default:m(()=>e[24]||(e[24]=[z("确定")])),_:1},8,["loading"])):je("",!0)])]),default:m(()=>[i("div",st,[i("div",it,[(S(),$(q,null,ee(ve,(r,D)=>i("div",{key:D,class:Ee(["sidebar-item",{active:B.value==D}]),onClick:ft=>Ge(D)},[i("div",dt,P(D+1),1),i("div",ut,P(r),1)],10,ct)),64))]),i("div",{class:"dialog-content",ref_key:"formContent",ref:w},[c(va,{model:l,"label-width":"100px",rules:Qe,ref_key:"studentFormRef",ref:ae,class:"student-form"},{default:m(()=>[c(ba,{"student-form":l,"dialog-type":b.value,"school-options":j.value,"major-options":f.value,"target-major-options":C.value,"tag-options":se,"school-search-loading":N.value,"major-search-loading":_.value,"target-major-search-loading":Z.value,onSchoolChange:ca,onSearchSchool:ta,onSearchMajor:me,onSearchTargetMajor:ra},null,8,["student-form","dialog-type","school-options","major-options","target-major-options","school-search-loading","major-search-loading","target-major-search-loading"])]),_:1},8,["model"])],512)])]),_:1},8,["modelValue","title"]),c(Ra,{visible:ye.value,"onUpdate:visible":e[9]||(e[9]=r=>ye.value=r),onSelect:He},null,8,["visible"])])}}},It=ke(mt,[["__scopeId","data-v-052c6233"]]);export{It as default};
