import { defineStore } from 'pinia'
import { login as apiLogin, logout as apiLogout, getUserInfo as apiGetUserInfo, verifyToken as apiVerifyToken } from '@/api/user'
import { UserInfo, LoginParams, ApiResponse, LoginResult } from '@/types'
import router from '@/router'

interface UserState {
  token: string;
  userInfo: UserInfo;
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    token: localStorage.getItem('token') || '',
    userInfo: JSON.parse(localStorage.getItem('userInfo') || '{}')
  }),
  
  getters: {
    isLoggedIn: (state): boolean => !!state.token,
    nickname: (state): string => state.userInfo?.nickname || state.userInfo?.username || ''
  },
  
  actions: {
    // 登录
    async login(loginForm: LoginParams): Promise<ApiResponse<LoginResult>> {
      try {
        const res = await apiLogin(loginForm)
        
        const { token, user } = res.data
        
        // 保存到状态
        this.token = token
        this.userInfo = user
        
        // 保存到本地存储
        localStorage.setItem('token', token)
        localStorage.setItem('userInfo', JSON.stringify(user))
        
        return Promise.resolve(res)
      } catch (error) {
        return Promise.reject(error)
      }
    },
    
    // 获取用户信息
    async getUserInfo(): Promise<ApiResponse<UserInfo>|any> {
      if (this.token) {
        try {
          const res = await apiGetUserInfo()
          this.userInfo = res.data as UserInfo
          localStorage.setItem('userInfo', JSON.stringify(res.data))
          return Promise.resolve(res)
        } catch (error) {
          return Promise.reject(error)
        }
      }
      return Promise.reject(new Error('未登录'))
    },
    
    // 刷新用户信息（更新状态管理中的用户数据）
    async refreshUserInfo(): Promise<UserInfo> {
      try {
        const res = await this.getUserInfo()
        return Promise.resolve(res.data)
      } catch (error) {
        console.error('刷新用户信息失败', error)
        return Promise.reject(error)
      }
    },
    
    // 验证token有效性
    async verifyToken(): Promise<boolean> {
      if (!this.token) {
        return false
      }
      
      try {
        const res = await apiVerifyToken()
        this.getUserInfo()
        return res.code === 0
      } catch (error) {
        console.error('token验证失败:', error)
        // token无效，清除本地存储
        this.token = ''
        this.userInfo = {} as UserInfo
        localStorage.removeItem('token')
        localStorage.removeItem('userInfo')
        return false
      }
    },
    
    // 退出登录
    async logout(): Promise<void> {
      if (this.token) {
        try {
          const res = await apiLogout()
          if (res.code === 0) {
            this.token = ''
            this.userInfo = {} as UserInfo
            localStorage.removeItem('token')
            localStorage.removeItem('userInfo')
            router.push("/login")
          }
        } catch (error) {
          console.error('退出登录失败', error)
        }
      }
      
      // 清除状态和本地存储
      this.token = ''
      this.userInfo = {} as UserInfo
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
    }
  }
})
