<template>
    <div class="component-demo">
        <div class="demo-section">
            <h2>组件使用示例</h2>

            <!-- 通用标签组件示例 -->
            <div class="demo-item">
                <h3>通用标签组件 (TagComponent)</h3>
                <div class="tag-examples">
                    <TagComponent number="01" text="薄弱模块分析" />
                    <TagComponent number="02" text="初试各科学习规划" />
                    <TagComponent number="03" text="综合建议" size="small" theme="success" />
                    <TagComponent number="04" text="院校推荐" size="large" theme="primary" />
                </div>
            </div>

            <!-- 薄弱模块分析组件 -->
            <div class="demo-item">
                <h3>薄弱模块分析组件</h3>
                <WeakModuleAnalysis />
            </div>

            <!-- 学习规划组件 -->
            <div class="demo-item">
                <h3>初试各科学习规划组件</h3>
                <StudyPlanAnalysis />
            </div>

            <!-- 带自定义数据的使用示例 -->
            <div class="demo-item">
                <h3>使用自定义数据</h3>
                <WeakModuleAnalysis :module-data="customModuleData" />
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import WeakModuleAnalysis from './WeakModuleAnalysis.vue'
import StudyPlanAnalysis from './StudyPlanAnalysis.vue'
import TagComponent from './TagComponent.vue'

// 自定义数据示例
const customModuleData = ref([
    {
        subject: '计算机专业课',
        problemAnalysis: '数据结构和算法基础相对薄弱，对于复杂的算法设计和分析能力有待提高。',
        potentialImpact: '在专业课考试中可能难以应对算法设计题和复杂程序分析题。',
        solutions: [
            '系统学习《数据结构（严蔚敏）》，每天练习2小时编程题',
            '参考《王道计算机考研复习指导》，完成每章节的习题练习',
            '在LeetCode上每天刷5道中等难度算法题，提高编程思维'
        ]
    }
])
</script>

<style lang="less" scoped>
.component-demo {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.demo-section {
    h2 {
        color: #333;
        border-bottom: 2px solid #1bb394;
        padding-bottom: 10px;
        margin-bottom: 30px;
    }
}

.demo-item {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #fff;

    h3 {
        color: #1bb394;
        margin-top: 0;
        margin-bottom: 20px;
        font-size: 18px;
    }
}

.tag-examples {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .component-demo {
        padding: 15px;
    }

    .tag-examples {
        grid-template-columns: 1fr;
    }
}
</style>