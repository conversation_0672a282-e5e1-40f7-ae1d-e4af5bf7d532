<?php
namespace app\model;

use think\Model;

class StudentDetail extends Model
{
    protected $table = 'ba_student_detail';
    protected $pk = 'id';
    protected $autoWriteTimestamp = false;

    /**
     * 本科成绩获取器
     * 将JSON字符串转换为数组
     * 数据格式: [{"id": 1, "title": "课程名称", "score": "90"}, ...]
     *
     * @param mixed $value 数据库中存储的JSON字符串
     * @return array 转换后的数组
     */
    public function getUndergraduateTranscriptAttr($value)
    {
        return $value ? json_decode($value, true) : [];
    }

    /**
     * 本科成绩修改器
     * 将数组转换为JSON字符串存储到数据库
     *
     * @param mixed $value 要存储的数组或JSON字符串
     * @return string 转换后的JSON字符串
     */
    public function setUndergraduateTranscriptAttr($value)
    {
        return is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
    }

    /**
     * 省份选择获取器
     * 将逗号分隔的字符串转换为数组
     * 数据格式: "北京市,上海市,广东省,..."
     *
     * @param mixed $value 数据库中存储的逗号分隔字符串
     * @return array 转换后的数组
     */
    public function getTargetProvincesAttr($value)
    {
        return $value ? explode(',', $value) : [];
    }

    /**
     * 省份选择修改器
     * 将数组转换为逗号分隔的字符串存储到数据库
     *
     * @param mixed $value 要存储的数组或字符串
     * @return string 转换后的逗号分隔字符串
     */
    public function setTargetProvincesAttr($value)
    {
        return is_array($value) ? implode(',', $value) : $value;
    }

    /**
     * 预估成绩获取器
     * 将JSON字符串转换为数组
     * 数据格式: [{"id": 1, "title": "专业课一", "name": "数据结构", "score": "85"}, ...]
     *
     * @param mixed $value 数据库中存储的JSON字符串
     * @return array 转换后的数组
     */
    public function getEstimatedScoresAttr($value)
    {
        return $value ? json_decode($value, true) : [];
    }

    /**
     * 预估成绩修改器
     * 将数组转换为JSON字符串存储到数据库
     *
     * @param mixed $value 要存储的数组或JSON字符串
     * @return string 转换后的JSON字符串
     */
    public function setEstimatedScoresAttr($value)
    {
        return is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
    }

    /**
     * 学生关联
     * @return \think\model\relation\BelongsTo
     */
    public function student()
    {
        return $this->belongsTo(Student::class, 'student_id', 'id');
    }
}
