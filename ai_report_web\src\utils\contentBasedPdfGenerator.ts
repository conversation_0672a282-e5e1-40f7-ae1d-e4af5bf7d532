import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'

interface ContentPdfOptions {
  filename?: string
  quality?: number
  scale?: number
  useCORS?: boolean
  allowTaint?: boolean
  backgroundColor?: string
}

/**
 * 基于内容分段的PDF生成器
 */
export class ContentBasedPdfGenerator {
  private static readonly A4_WIDTH = 210 // A4宽度(mm)
  private static readonly A4_HEIGHT = 297 // A4高度(mm)
  private static readonly HEADER_HEIGHT = 15 // 页头高度(mm)
  private static readonly FOOTER_HEIGHT = 15 // 页脚高度(mm)

  /**
   * 生成PDF - 基于内容自然分段
   */
  static async generatePdf(element: HTMLElement, options: ContentPdfOptions = {}): Promise<void> {
    const {
      filename = `report-${Date.now()}.pdf`,
      quality = 0.95,
      scale = 1.5,
      useCORS = true,
      allowTaint = true,
      backgroundColor = '#ffffff'
    } = options

    try {
      console.log('开始基于内容的PDF生成...')
      
      // 等待所有内容加载完成
      await this.waitForChartsAndImages(element)

      // 获取所有主要内容区域
      const contentSections = this.getContentSections(element)
      console.log(`找到 ${contentSections.length} 个内容区域`)

      // 创建PDF文档
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      })

      const pageWidth = this.A4_WIDTH
      const pageHeight = this.A4_HEIGHT
      const contentWidth = pageWidth - 20
      const maxContentHeight = pageHeight - this.HEADER_HEIGHT - this.FOOTER_HEIGHT - 10

      let currentPageHeight = 0
      let pageNumber = 1
      let totalPages = 1
      let isFirstPage = true

      // 预计算总页数
      let estimatedHeight = 0
      for (const section of contentSections) {
        const tempCanvas = await this.renderSection(section.element, {
          scale: scale * 0.5, // 使用较小的scale进行预计算
          quality: 0.5,
          useCORS,
          allowTaint,
          backgroundColor,
          contentWidth
        })
        estimatedHeight += (tempCanvas.height * contentWidth) / tempCanvas.width
      }
      totalPages = Math.max(1, Math.ceil(estimatedHeight / maxContentHeight))
      console.log(`预计总页数: ${totalPages}`)

      // 处理每个内容区域
      for (let i = 0; i < contentSections.length; i++) {
        const section = contentSections[i]
        console.log(`处理第 ${i + 1} 个内容区域:`, section.type)

        // 渲染当前区域
        const sectionCanvas = await this.renderSection(section.element, {
          scale,
          quality,
          useCORS,
          allowTaint,
          backgroundColor,
          contentWidth
        })

        const sectionHeight = (sectionCanvas.height * contentWidth) / sectionCanvas.width

        // 检查是否需要新页面
        if (!isFirstPage && (currentPageHeight + sectionHeight > maxContentHeight)) {
          // 添加页脚到当前页
          this.addFooter(pdf, pageWidth, pageHeight, pageNumber, totalPages)

          // 创建新页面
          pdf.addPage()
          pageNumber++
          currentPageHeight = 0
        }

        // 如果是新页面，添加页头
        if (currentPageHeight === 0) {
          this.addHeader(pdf, pageWidth, pageNumber)
          currentPageHeight = this.HEADER_HEIGHT + 5
          isFirstPage = false
        }

        // 添加内容到PDF
        const imgData = sectionCanvas.toDataURL('image/jpeg', quality)
        const actualHeight = Math.min(sectionHeight, maxContentHeight - currentPageHeight)

        pdf.addImage(imgData, 'JPEG', 10, currentPageHeight, contentWidth, actualHeight)
        currentPageHeight += actualHeight + 5 // 添加一些间距

        console.log(`第 ${i + 1} 个区域已添加到PDF，当前页高度: ${currentPageHeight}`)
      }

      // 更新实际总页数
      totalPages = pageNumber

      // 添加最后一页的页脚
      this.addFooter(pdf, pageWidth, pageHeight, pageNumber, totalPages)

      // 保存PDF
      pdf.save(filename)
      console.log('PDF生成完成')

    } catch (error) {
      console.error('PDF生成失败:', error)
      throw error
    }
  }

  /**
   * 获取内容区域
   */
  private static getContentSections(element: HTMLElement): Array<{element: HTMLElement, type: string}> {
    const sections: Array<{element: HTMLElement, type: string}> = []

    // 查找报告头部
    const reportHeader = element.querySelector('.report-header') as HTMLElement
    if (reportHeader) {
      sections.push({ element: reportHeader, type: 'header' })
    }

    // 查找所有报告内容页面
    const reportContents = element.querySelectorAll('.report-content')
    reportContents.forEach((content, index) => {
      sections.push({ 
        element: content as HTMLElement, 
        type: `content-${index + 1}` 
      })
    })

    // 如果没有找到分段内容，使用整个元素
    if (sections.length === 0) {
      sections.push({ element, type: 'full-content' })
    }

    return sections
  }

  /**
   * 渲染单个内容区域
   */
  private static async renderSection(
    element: HTMLElement, 
    options: {
      scale: number
      quality: number
      useCORS: boolean
      allowTaint: boolean
      backgroundColor: string
      contentWidth: number
    }
  ): Promise<HTMLCanvasElement> {
    const { scale, useCORS, allowTaint, backgroundColor, contentWidth } = options

    // 获取元素尺寸
    const elementRect = element.getBoundingClientRect()
    const elementWidth = element.scrollWidth || elementRect.width
    const elementHeight = element.scrollHeight || elementRect.height

    console.log(`渲染区域尺寸: ${elementWidth} x ${elementHeight}`)

    // 使用html2canvas渲染
    const canvas = await html2canvas(element, {
      scale,
      useCORS,
      allowTaint,
      backgroundColor,
      width: elementWidth,
      height: elementHeight,
      scrollX: 0,
      scrollY: 0,
      windowWidth: elementWidth,
      windowHeight: elementHeight,
      logging: false
    })

    return canvas
  }

  /**
   * 等待图表和图片加载完成
   */
  private static async waitForChartsAndImages(element: HTMLElement): Promise<void> {
    console.log('等待图表和图片加载...')
    
    // 等待ECharts图表渲染完成
    const charts = element.querySelectorAll('.echarts-box')
    for (const chart of charts) {
      await new Promise(resolve => {
        const checkChart = () => {
          const canvas = chart.querySelector('canvas')
          if (canvas && canvas.width > 0 && canvas.height > 0) {
            resolve(void 0)
          } else {
            setTimeout(checkChart, 100)
          }
        }
        checkChart()
      })
    }

    // 等待图片加载完成
    const images = element.querySelectorAll('img')
    const imagePromises = Array.from(images).map(img => {
      return new Promise(resolve => {
        if (img.complete) {
          resolve(void 0)
        } else {
          img.onload = () => resolve(void 0)
          img.onerror = () => resolve(void 0)
        }
      })
    })

    await Promise.all(imagePromises)

    // 强制重绘ECharts图表
    const echartsInstances = (window as any).echarts?.getInstanceByDom
    if (echartsInstances) {
      charts.forEach(chart => {
        const instance = (window as any).echarts.getInstanceByDom(chart)
        if (instance) {
          instance.resize()
        }
      })
    }

    // 额外等待确保所有内容都已渲染
    await new Promise(resolve => setTimeout(resolve, 1000))
    console.log('图表和图片加载完成')
  }

  /**
   * 添加页头
   */
  private static addHeader(pdf: jsPDF, pageWidth: number, pageNumber: number): void {
    // 添加绿色背景条
    pdf.setFillColor(27, 179, 148) // #1bb394
    pdf.rect(0, 0, pageWidth, this.HEADER_HEIGHT, 'F')

    // 添加标题文字
    pdf.setTextColor(255, 255, 255)
    pdf.setFontSize(10)
    pdf.text('AI择校报告系统', 10, 8)

    // 右侧添加日期
    const currentDate = new Date().toLocaleDateString('zh-CN')
    const dateWidth = pdf.getTextWidth(currentDate)
    pdf.text(currentDate, pageWidth - dateWidth - 10, 8)
  }

  /**
   * 添加页脚
   */
  private static addFooter(pdf: jsPDF, pageWidth: number, pageHeight: number, pageNumber: number, totalPages: number): void {
    const footerY = pageHeight - 8

    // 添加分隔线
    pdf.setDrawColor(200, 200, 200)
    pdf.setLineWidth(0.3)
    pdf.line(10, footerY - 3, pageWidth - 10, footerY - 3)

    // 添加页码
    pdf.setTextColor(102, 102, 102)
    pdf.setFontSize(9)
    const pageText = `${pageNumber} / ${totalPages}`
    const textWidth = pdf.getTextWidth(pageText)
    pdf.text(pageText, (pageWidth - textWidth) / 2, footerY)

    // 左侧添加生成时间
    const generateTime = `生成时间: ${new Date().toLocaleString('zh-CN')}`
    pdf.setFontSize(8)
    pdf.text(generateTime, 10, footerY)
  }
}

/**
 * 快捷方法：生成PDF
 */
export async function generateContentBasedPdf(elementId: string, options: ContentPdfOptions = {}): Promise<void> {
  const element = document.getElementById(elementId)
  if (!element) {
    throw new Error(`未找到ID为 ${elementId} 的元素`)
  }
  
  return ContentBasedPdfGenerator.generatePdf(element, options)
}

export default ContentBasedPdfGenerator
