<?php
use Webman\Route;

// 用户认证相关接口
// 登录登出 - 无需鉴权
Route::post('/api/login', [app\controller\UserController::class, 'login']);
Route::post('/api/logout', [app\controller\UserController::class, 'logout']);

// 需要鉴权的用户接口
Route::group("/api", function () {
    // 用户信息
    Route::get('/user/info', [app\controller\UserController::class, 'getUserInfo']);
    Route::get('/user/verify-token', [app\controller\UserController::class, 'verifyToken']);
})->middleware([app\middleware\JwtMiddleware::class]); 