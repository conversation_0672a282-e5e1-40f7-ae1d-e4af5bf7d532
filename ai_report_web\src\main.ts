import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import router from './router'
import pinia from './store'
import './style.css'
import App from './App.vue'
import { useUserStore } from '@/store/modules/user'

const app = createApp(App)

// 注册ElementPlus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(ElementPlus, {
  locale: zhCn, // 设置默认语言为中文
})
app.use(router)
app.use(pinia)

// 应用初始化时验证token
const initApp = async () => {
  const userStore = useUserStore()
  // 如果有token，验证其有效性
  if (userStore.token) {
    const isValid = await userStore.verifyToken()
    if (!isValid) {
      console.log('应用启动时发现token无效，已清除')
    }
  }
  
  app.mount('#app')
}

initApp()
