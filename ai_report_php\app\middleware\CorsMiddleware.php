<?php
// 假设文件路径为 app/middleware/CorsMiddleware.php (请根据您的实际路径调整命名空间)
namespace app\middleware;

use Webman\MiddlewareInterface;
use Webman\Http\Response;
use Webman\Http\Request;

class CorsMiddleware implements MiddlewareInterface
{
    public function process(Request $request, callable $handler): Response
    {
        // --- 步骤 1: 首先处理 OPTIONS 预检请求 ---
        if ($request->method() === 'OPTIONS') {
            // 对于 OPTIONS 请求，直接创建一个空响应 (通常是 204 No Content 或 200 OK)
            // 不要调用 $handler($request)
            $response = response('', 204); // 创建一个状态码为 204 的空响应

            // 为预检响应设置 CORS 头部
            $response->withHeaders([
                'Access-Control-Allow-Origin'  => $request->header('Origin', '*'), // 允许任何源，或指定特定源
                'Access-Control-Allow-Methods' => 'GET, POST, PUT, DELETE, PATCH, OPTIONS', // 明确列出允许的方法，SSE 至少需要 GET, OPTIONS
                'Access-Control-Allow-Headers' => $request->header('Access-Control-Request-Headers', 'Content-Type, Accept, Origin, X-Requested-With'), // 反射客户端请求的头部，或列出常用头部
                'Access-Control-Max-Age'       => '86400', // 预检请求结果的缓存时间 (秒)
            ]);
            // 因为不需要凭证，所以不需要 Access-Control-Allow-Credentials 头部

            return $response; // 对于 OPTIONS 请求，在此处直接返回响应
        }

        // --- 步骤 2: 对于非 OPTIONS 的实际请求 (例如 SSE 的 GET 请求)，执行后续处理器 ---
        $response = $handler($request);

        // --- 步骤 3: 为实际请求的响应添加 CORS 头部 (主要是 Origin) ---
        // 对于实际的 GET 请求，浏览器主要关心 Access-Control-Allow-Origin。
        $response->withHeader('Access-Control-Allow-Origin', $request->header('Origin', '*'));
        // 因为不需要凭证，所以不需要 Access-Control-Allow-Credentials 头部

        // 如果前端需要读取某些特定的响应头，可以在这里暴露它们
        // $response->withHeader('Access-Control-Expose-Headers', 'Content-Length, X-My-Custom-Header');

        return $response;
    }
}