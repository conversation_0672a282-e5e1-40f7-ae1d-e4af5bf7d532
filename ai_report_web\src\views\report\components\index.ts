// 导出所有组件
export { default as WeakModuleAnalysis } from './WeakModuleAnalysis.vue'
export { default as StudyPlanAnalysis } from './StudyPlanAnalysis.vue'
export { default as TagComponent } from './TagComponent.vue'
export { default as ComponentDemo } from './ComponentDemo.vue'

// 导出组件类型定义
export interface WeakModule {
  subject: string;
  problemAnalysis: string;
  potentialImpact: string;
  solutions: string[];
}

export interface StudySubject {
  name: string;
  content: string[];
  methods: string[];
  materials: string;
  keyPoints?: string[];
}

export interface StudyStage {
  stageName: string;
  duration: string;
  goal: string;
  subjects: StudySubject[];
}

export interface ComprehensiveAdvice {
  title: string;
  content: string;
}

export interface TagProps {
  number: string | number;
  text: string;
  theme?: 'default' | 'primary' | 'success' | 'warning' | 'danger';
  size?: 'small' | 'medium' | 'large';
  backgroundImage?: string;
} 