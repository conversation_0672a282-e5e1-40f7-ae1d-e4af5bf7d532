<template>
  <div class="top-green-section">
    <div class="left-top-container">
      <img
        class="right-color-block"
        src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/pdf-top-green.png"
        alt="顶部文字"
      />

      <img
        class="text-img"
        src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/top-text.png"
        alt="顶部文字"
      />
    </div>

    <div class="content-logo">
      <img
        src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/pdf_logo.png"
        alt="Logo"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
// 页头组件，用于显示报告页面的顶部绿色区域
// 包含左侧的绿色背景图片、文字图片和右侧的Logo
</script>

<style scoped>
.top-green-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.left-top-container {
  display: flex;
  align-items: center;
}

.right-color-block {
  width: 82px;
  height: 34px;
}

.text-img {
  margin-left: 10px;
  width: 206px;
  height: 34px;
}

.content-logo img {
  height: 50px;
}
</style>
