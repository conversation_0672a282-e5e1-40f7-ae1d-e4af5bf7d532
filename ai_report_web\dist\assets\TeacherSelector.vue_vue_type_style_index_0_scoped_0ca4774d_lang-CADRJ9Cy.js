import{_ as Se}from"./_plugin-vue_export-helper-BCEt7Ct6.js";/* empty css                   *//* empty css                     *//* empty css                  *//* empty css                 */import{c as Me,d as G,e as Z}from"./student-Db39rDv4.js";/* empty css                        */import{r as i,a as H,A as O,x as _,n as J,y as Ve,Y as xe,G as De,c as f,o as m,b as s,e as r,h as Ce,P as h,w as u,$ as _e,Z as Ne,F as j,s as S,a6 as Ue,l as A,j as ke,_ as Te,t as Le,M as Oe,N as Ae,d as Ee,E as Ie,g as Be,D as we,k as c}from"./index-Cu_gl4pu.js";import{c as Pe,b as Ye}from"./school-BRpLdP4V.js";import{g as $e}from"./tag-IZtnXBn1.js";import{p as ze}from"./province-Do8ZIeJL.js";const Re={class:"generate-container"},Ge={class:"report-container"},Ze={class:"steps"},He={class:"step-section form-section",id:"section-0"},Je={class:"step-content"},Ke={class:"form-grid"},Qe={class:"form-item"},We={class:"form-item"},Xe={class:"form-item"},qe={class:"form-item"},et={class:"form-item"},tt={class:"form-item"},lt={class:"form-item"},ot={class:"form-item"},st={class:"form-item"},at={class:"form-item"},rt={class:"form-item"},dt={class:"form-item"},nt={class:"form-item"},it={class:"step-section form-section",id:"section-1"},ut={class:"step-content"},mt={class:"step-container"},ct={class:"create-btn"},gt={class:"score-grid"},pt={class:"score-row"},vt=["onDragstart","onDrop"],ft={class:"score-label"},Ft={class:"score-input-container"},ht={class:"step-section form-section",id:"section-2"},bt={class:"step-content"},yt={class:"english-grid"},jt={class:"english-row"},St={class:"english-item"},Mt={class:"english-item"},Vt={class:"english-item"},xt={class:"english-item"},Dt={class:"english-row"},Ct={class:"english-item"},_t={class:"english-item"},Nt={class:"step-section form-section",id:"section-3"},Ut={class:"step-content"},kt={class:"school-grid"},Tt={class:"form-item"},Lt={class:"form-item"},Ot={class:"form-item"},At={class:"form-item"},Et={class:"form-item wide-item"},It={class:"step-section form-section",id:"section-4"},Bt={class:"step-content"},wt={class:"score-table"},Pt={class:"table-row-score"},Yt={class:"td-cell"},$t={class:"td-cell"},zt={class:"td-cell"},Rt={class:"td-cell"},Gt={class:"td-cell"},Zt={class:"personal-demands"},Ht={class:"expertise-advice form-section",id:"section-5"},Jt={class:"dialog-footer"},Kt={__name:"StudentBasic",props:{studentForm:{type:Object,required:!0},dialogType:{type:String,default:"add"},schoolOptions:{type:Array,default:()=>[]},majorOptions:{type:Array,default:()=>[]},targetMajorOptions:{type:Array,default:()=>[]},tagOptions:{type:Array,default:()=>[]},schoolSearchLoading:{type:Boolean,default:!1},majorSearchLoading:{type:Boolean,default:!1},targetMajorSearchLoading:{type:Boolean,default:!1}},emits:["school-change","search-school","search-major","search-target-major"],setup(K,{emit:Q}){const t=K,N=Q;i(!1),i(!1),i([]),i(null);const E=i([]),T=i(!1),b=i([]),U=i(!1),I=i(null),L={},B=i([]),w=i([]);i(!1),i([]),i(null),i(!1),i([]),i(null);const P=i([]),k=i([]),D=i([]),y=H({studentName:"",gender:"",college:"",major:"",targetMajor:"",majorCode:"",contact:"",year:"",isMultiDisciplinary:"",gaokaoScore:"",mathScore1:"",mathScore2:"",statScore:"",linearScore:"",majorScore1:"",majorScore2:"",majorScore3:"",gaokaoEnglish:"",cet4:"",cet6:"",toefl:"",ielts:"",englishLevel:"",englishAbility:"",region:"",intendedSchools:[],targetSchool:"",schoolLevel:"",majorAdjustment:"",politics:"",english:"",statistician:"",professional:"",totalScore:"",personalDemands:"",expertiseAdvice:""}),W=()=>{$e().then(l=>{if(l.code===0){const e=l.data.first||[],a=l.data.second||[],d=l.data.third||[];w.value=[...e,...a,...d],B.value=e.map(n=>{const F=a.filter(g=>g.parent_id===n.id).map(g=>{const p=d.filter(x=>x.parent_id===g.id).map(x=>({id:x.id,name:x.name,color:x.color||n.color}));return{id:g.id,name:g.name,color:g.color||n.color,children:p.length>0?p:void 0}});return{id:n.id,name:n.name,color:n.color,children:F.length>0?F:void 0}})}else c.error(l.msg||"获取标签失败")}).catch(l=>{console.error("获取标签失败",l),c.error("获取标签失败，请稍后重试")})};O(()=>!t.studentForm.tags||!Array.isArray(t.studentForm.tags)||t.studentForm.tags.length===0?[]:t.studentForm.tags.map(l=>{const e=w.value.find(d=>d.id===l);return e?{value:e.id,label:e.name,color:e.color}:t.tagOptions.find(d=>d.value===l)||{value:l,label:l,class:"tag-default"}}));const Y=O({get:()=>t.studentForm.sex,set:l=>{t.studentForm.sex=l!=null?String(l):l}}),$=O({get:()=>t.studentForm.isMultiDisciplinary+"",set:l=>{t.studentForm.isMultiDisciplinary=l!=null?String(l):l}});_(()=>t.studentForm,l=>{console.log("newval",l),y.studentName=l.name===null?"":l.name||"",y.college=l.undergraduateSchool===null?"":l.undergraduateSchool||"",y.major=l.undergraduateMajor===null?"":l.undergraduateMajor||"",y.targetMajor=l.targetMajor===null?"":l.targetMajor||"",y.majorCode=l.majorCode===null?"":l.majorCode||"",y.contact=l.phone===null?"":l.phone||"",y.year=l.examYear===null?"":l.examYear||"",y.isMultiDisciplinary=l.isMultiDisciplinary===null?"":l.isMultiDisciplinary||"",l.name===null&&(t.studentForm.name=""),l.sex===null?t.studentForm.sex="":typeof l.sex!="string"&&(t.studentForm.sex=String(l.sex)),l.undergraduateSchool===null&&(t.studentForm.undergraduateSchool=""),l.undergraduateMajor===null&&(t.studentForm.undergraduateMajor=""),l.targetMajor===null&&(t.studentForm.targetMajor=""),l.majorCode===null&&(t.studentForm.majorCode=""),l.phone===null&&(t.studentForm.phone=""),l.examYear===null&&(t.studentForm.examYear=""),l.isMultiDisciplinary===null&&(t.studentForm.isMultiDisciplinary=""),(l.englishAbility===null||l.englishAbility===void 0)&&(t.studentForm.englishAbility=""),(l.referenceBooks===null||l.referenceBooks===void 0)&&(t.studentForm.referenceBooks=""),l.targetProvinces===null||l.targetProvinces===void 0?t.studentForm.targetProvinces=[]:typeof l.targetProvinces=="string"?t.studentForm.targetProvinces=l.targetProvinces.split(",").filter(a=>a.trim()!==""):Array.isArray(l.targetProvinces)&&(t.studentForm.targetProvinces=l.targetProvinces);const e=l.targetRegion||[];console.log("当前地区倾向:",e),t.dialogType==="edit"&&t.studentForm.undergraduateSchool&&t.studentForm.undergraduateSchoolName&&(t.schoolOptions.find(d=>d.value===t.studentForm.undergraduateSchool)||t.schoolOptions.push({value:t.studentForm.undergraduateSchool,label:t.studentForm.undergraduateSchoolName})),t.dialogType==="edit"&&t.studentForm.undergraduateMajor&&t.studentForm.undergraduateMajorName&&(t.majorOptions.find(d=>d.value===t.studentForm.undergraduateMajor)||t.majorOptions.push({value:t.studentForm.undergraduateMajor,label:t.studentForm.undergraduateMajorName})),t.dialogType==="edit"&&t.studentForm.targetMajor&&t.studentForm.targetMajorName&&(t.targetMajorOptions.find(d=>d.value===t.studentForm.targetMajor)||t.targetMajorOptions.push({value:t.studentForm.targetMajor,label:t.studentForm.targetMajorName,code:t.studentForm.majorCode||""})),t.dialogType==="edit"&&t.studentForm.targetSchool&&t.studentForm.targetSchoolName&&(b.value.find(d=>d.value===t.studentForm.targetSchool)||b.value.push({value:t.studentForm.targetSchool,label:t.studentForm.targetSchoolName}))},{deep:!0,immediate:!0}),_(()=>y.studentName,l=>{t.studentForm.name=l}),_([()=>t.studentForm.politics,()=>t.studentForm.englishS,()=>t.studentForm.englishType,()=>t.studentForm.mathType],([l,e,a,d])=>{const n=l?parseFloat(l):0,F=e?parseFloat(e):0,g=a?parseFloat(a):0,p=d?parseFloat(d):0;t.studentForm.totalScore=n+F+g+p}),i([]);const C=i(!1),v=H({title:"",score:""}),V=i(null),X=(l,e)=>{V.value=e,l.dataTransfer.effectAllowed="move",l.target.style.opacity="0.5"},q=l=>{l.preventDefault(),l.dataTransfer.dropEffect="move";const e=l.currentTarget;e&&V.value&&e!==V.value&&e.classList.add("drag-over")},ee=(l,e)=>{if(l.preventDefault(),document.querySelectorAll(".score-item").forEach(p=>{p.classList.remove("drag-over")}),!V.value||V.value.id===e.id)return;const d=t.studentForm.undergraduateTranscript.findIndex(p=>p.id===V.value.id),n=t.studentForm.undergraduateTranscript.findIndex(p=>p.id===e.id);if(d<0||n<0)return;const F=[...t.studentForm.undergraduateTranscript],[g]=F.splice(d,1);F.splice(n,0,g),t.studentForm.undergraduateTranscript=F,V.value=null,document.querySelectorAll(".score-item").forEach(p=>{p.style.opacity="1"}),c.success("排序已更新")},te=l=>{l.target.style.opacity="1",document.querySelectorAll(".score-item").forEach(a=>{a.classList.remove("drag-over")})},le=()=>{C.value=!0,v.title="",v.score=""},oe=l=>{if(t.studentForm.undergraduateTranscript.length<=1){c.warning("至少保留一项成绩");return}const e=t.studentForm.undergraduateTranscript.findIndex(a=>a.id===l);e!==-1&&(t.studentForm.undergraduateTranscript.splice(e,1),c.success("删除成功"))},se=()=>{if(!v.title||!v.score){c.warning("科目名称和成绩不能为空");return}t.studentForm.undergraduateTranscript||(t.studentForm.undergraduateTranscript=[]);const e=Math.max(...t.studentForm.undergraduateTranscript.map(a=>a.id)||[],0)+1;t.studentForm.undergraduateTranscript.push({id:e,title:v.title,score:v.score}),C.value=!1,c.success("添加成功"),v.title="",v.score=""},ae=l=>{const e=String(l);return e==="1"?"男":e==="2"?"女":e==="3"?"其他":""},re=l=>{N("search-school",l)},de=l=>{if(!t.studentForm.undergraduateSchool){c.warning("请先选择本科院校");return}N("search-major",l)},ne=l=>{N("search-target-major",l)},ie=l=>{if(!l){b.value=[];return}if(U.value=!0,console.log("搜索梦校:",l),L[l]){b.value=L[l],U.value=!1;return}clearTimeout(I.value),I.value=setTimeout(()=>{Ye(l).then(e=>{if(console.log("搜索梦校结果:",e),e.code===0&&e.data){const a=e.data.map(d=>({value:d.id.toString(),label:d.name}));if(b.value=a,L[l]=a,t.studentForm.targetSchool){const d=a.find(n=>n.value===t.studentForm.targetSchool);d&&(t.studentForm.targetSchoolName=d.label)}}else b.value=[]}).catch(e=>{console.error("获取梦校列表失败",e),b.value=[]}).finally(()=>{U.value=!1})},300)},ue=l=>{const e=t.schoolOptions.find(a=>a.value===l);e&&(t.studentForm.undergraduateSchoolName=e.label),t.studentForm.undergraduateMajor="",t.studentForm.undergraduateMajorName="",N("school-change")},me=l=>{const e=t.majorOptions.find(a=>a.value===l);e?t.studentForm.undergraduateMajorName=e.label:t.studentForm.undergraduateMajorName=""},ce=async l=>{if(console.log("学科门类变更:",l),t.studentForm.firstLevelDiscipline=null,t.studentForm.targetMajor=null,t.studentForm.targetMajorName="",t.studentForm.majorCode="",k.value=[],D.value=[],l)try{const e=await G(l);e.code===0?k.value=e.data:c.error(e.msg||"获取一级学科失败")}catch(e){console.error("获取一级学科失败",e),c.error("获取一级学科失败，请稍后重试")}},ge=async l=>{if(console.log("一级学科变更:",l),t.studentForm.targetMajor=null,t.studentForm.targetMajorName="",t.studentForm.majorCode="",D.value=[],l)try{const e=await Z(l);e.code===0?D.value=e.data:c.error(e.msg||"获取目标专业失败")}catch(e){console.error("获取目标专业失败",e),c.error("获取目标专业失败，请稍后重试")}},pe=l=>{const e=D.value.filter(a=>l.includes(a.id));Array.isArray(e)&&e.length>0?(console.log("选中的目标专业:",e),t.studentForm.targetMajorName=e.map(a=>a.label),t.studentForm.majorCode=e.map(a=>a.major_code).join(","),t.studentForm.targetMajor=e.map(a=>a.id),console.log("设置后的targetMajor:",t.studentForm.targetMajor)):(t.studentForm.majorCode="",t.studentForm.targetMajor="",t.studentForm.targetMajorName="")},ve=l=>{const e=b.value.find(a=>a.value===l);e&&(t.studentForm.targetSchoolName=e.label)},fe=l=>{if(console.log("标签选择变更:",l),!Array.isArray(l)){t.studentForm.tags=[];return}t.studentForm.tags=l.map(e=>{const a=typeof e=="object"?e.id:e;return parseInt(a,10)}).filter(e=>!isNaN(e)&&e>0),console.log("处理后的标签ID:",t.studentForm.tags)};i(!1),i(!1),i(!1);const Fe=async()=>{T.value=!0;try{const l=await Pe();l.code===0?E.value=l.data:c.error(l.msg||"获取考研年份列表失败")}catch(l){console.error("获取考研年份列表失败",l),c.error("获取考研年份列表失败，请稍后重试")}finally{T.value=!1}},he=async()=>{try{const l=await Me();l.code===0?P.value=l.data:c.error(l.msg||"获取学科门类失败")}catch(l){console.error("获取学科门类失败",l),c.error("获取学科门类失败，请稍后重试")}},be=async()=>{if(t.studentForm.disciplineCategory)try{const l=await G(t.studentForm.disciplineCategory);l.code===0&&(k.value=l.data)}catch(l){console.error("加载一级学科失败",l)}if(t.studentForm.firstLevelDiscipline)try{const l=await Z(t.studentForm.firstLevelDiscipline);l.code===0&&(D.value=l.data)}catch(l){console.error("加载二级学科失败",l)}};J(()=>{if(Fe(),W(),he(),be(),console.log("目标专业ID:",t.studentForm.targetMajor),console.log("目标专业名称:",t.studentForm.targetMajorName),console.log("可选目标专业列表:",t.targetMajorOptions),t.studentForm.targetMajor&&!t.studentForm.targetMajorName){const l=t.targetMajorOptions.find(e=>e.value===t.studentForm.targetMajor);l?t.studentForm.targetMajorName=l.label:(typeof t.studentForm.targetMajor=="string"||typeof t.studentForm.targetMajor=="number")&&(t.studentForm.targetMajorName=`专业ID: ${t.studentForm.targetMajor}`)}t.studentForm.isMultiDisciplinary&&(t.studentForm.isMultiDisciplinary=String(t.studentForm.isMultiDisciplinary))});const z=()=>{};return J(()=>{Ve(()=>{window.addEventListener("resize",z)})}),xe(()=>{window.removeEventListener("resize",z)}),i(null),i(null),_(()=>t.studentForm.sex,l=>{console.log("性别值变化:",l)},{immediate:!0}),_(()=>t.studentForm.targetMajor,(l,e)=>{if(console.log("目标专业ID变化",e,"->",l),!(l===0||l==="0")&&l&&(!t.studentForm.targetMajorName||t.studentForm.targetMajorName===`专业ID: ${e}`)){const a=t.targetMajorOptions.find(d=>d.value===l);a?(t.studentForm.targetMajorName=a.label,console.log("从选项列表中找到并更新目标专业名称:",a.label)):(typeof l=="string"||typeof l=="number")&&l!==0&&l!=="0"&&(t.studentForm.targetMajorName=`专业ID: ${l}`,console.log("未找到目标专业名称，临时设置为ID"),t.targetMajorOptions.length===0&&(console.log("尝试搜索目标专业"),ne("")))}},{immediate:!0}),(l,e)=>{const a=Ce,d=_e,n=Ne,F=Ue,g=ke,p=De("Close"),x=Oe,R=Be,ye=Ie,je=we;return m(),f("div",Re,[s("div",Ge,[s("div",Ze,[s("div",He,[e[50]||(e[50]=s("div",{class:"step-header"},[s("div",{class:"step-title"},"第一部分：个人基础信息")],-1)),s("div",Je,[e[49]||(e[49]=s("div",{class:"step-num-tag"},[s("span",null,"01"),s("div",{class:"tag-text"},"个人基础信息")],-1)),s("div",Ke,[s("div",Qe,[e[36]||(e[36]=s("div",{class:"item-label"},"学员姓名",-1)),r(a,{modelValue:t.studentForm.name,"onUpdate:modelValue":e[0]||(e[0]=o=>t.studentForm.name=o),placeholder:"请输入学员姓名",disabled:t.dialogType==="detail"},null,8,["modelValue","disabled"])]),s("div",We,[e[37]||(e[37]=s("div",{class:"item-label"},"性别",-1)),t.dialogType==="detail"?(m(),h(a,{key:0,value:ae(t.studentForm.sex),disabled:"",class:"full-width"},null,8,["value"])):(m(),h(n,{key:1,modelValue:Y.value,"onUpdate:modelValue":e[1]||(e[1]=o=>Y.value=o),placeholder:"请选择性别",class:"full-width"},{default:u(()=>[r(d,{label:"男",value:"1"}),r(d,{label:"女",value:"2"}),r(d,{label:"其他",value:"3"})]),_:1},8,["modelValue"]))]),s("div",Xe,[e[38]||(e[38]=s("div",{class:"item-label"},"本科院校",-1)),r(n,{modelValue:t.studentForm.undergraduateSchool,"onUpdate:modelValue":e[2]||(e[2]=o=>t.studentForm.undergraduateSchool=o),placeholder:"输入关键字搜索院校",filterable:"",remote:"","reserve-keyword":"","remote-method":re,loading:t.schoolSearchLoading,class:"full-width",onChange:ue},{default:u(()=>[(m(!0),f(j,null,S(t.schoolOptions,o=>(m(),h(d,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),s("div",qe,[e[39]||(e[39]=s("div",{class:"item-label"},"本科专业",-1)),r(n,{modelValue:t.studentForm.undergraduateMajor,"onUpdate:modelValue":e[3]||(e[3]=o=>t.studentForm.undergraduateMajor=o),placeholder:"输入关键字搜索专业",filterable:"",remote:"","reserve-keyword":"","remote-method":de,loading:t.majorSearchLoading,class:"full-width",disabled:!t.studentForm.undergraduateSchool,onChange:me},{default:u(()=>[(m(!0),f(j,null,S(t.majorOptions,o=>(m(),h(d,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading","disabled"])]),s("div",et,[e[40]||(e[40]=s("div",{class:"item-label"},"学科门类",-1)),r(n,{modelValue:t.studentForm.disciplineCategory,"onUpdate:modelValue":e[4]||(e[4]=o=>t.studentForm.disciplineCategory=o),placeholder:"请选择学科门类",class:"full-width",onChange:ce},{default:u(()=>[(m(!0),f(j,null,S(P.value,o=>(m(),h(d,{key:o.id,label:o.label,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),s("div",tt,[e[41]||(e[41]=s("div",{class:"item-label"},"一级学科",-1)),r(n,{modelValue:t.studentForm.firstLevelDiscipline,"onUpdate:modelValue":e[5]||(e[5]=o=>t.studentForm.firstLevelDiscipline=o),placeholder:"请选择一级学科",class:"full-width",disabled:!t.studentForm.disciplineCategory,onChange:ge},{default:u(()=>[(m(!0),f(j,null,S(k.value,o=>(m(),h(d,{key:o.id,label:o.label,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),s("div",lt,[e[42]||(e[42]=s("div",{class:"item-label"},"目标专业",-1)),r(n,{modelValue:t.studentForm.targetMajor,"onUpdate:modelValue":e[6]||(e[6]=o=>t.studentForm.targetMajor=o),placeholder:"请选择目标专业",class:"full-width",disabled:!t.studentForm.firstLevelDiscipline,onChange:pe,multiple:""},{default:u(()=>[(m(!0),f(j,null,S(D.value,o=>(m(),h(d,{key:o.id,label:o.label,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),s("div",ot,[e[43]||(e[43]=s("div",{class:"item-label"},"专业代码",-1)),r(a,{modelValue:t.studentForm.majorCode,"onUpdate:modelValue":e[7]||(e[7]=o=>t.studentForm.majorCode=o),placeholder:"请输入专业代码"},null,8,["modelValue"])]),s("div",st,[e[44]||(e[44]=s("div",{class:"item-label"},"联系方式",-1)),r(a,{modelValue:t.studentForm.phone,"onUpdate:modelValue":e[8]||(e[8]=o=>t.studentForm.phone=o),placeholder:"请输入联系方式"},null,8,["modelValue"])]),s("div",at,[e[45]||(e[45]=s("div",{class:"item-label"},"考研年份",-1)),r(n,{modelValue:t.studentForm.examYear,"onUpdate:modelValue":e[9]||(e[9]=o=>t.studentForm.examYear=o),placeholder:"请选择考研年份",class:"full-width",loading:T.value},{default:u(()=>[(m(!0),f(j,null,S(E.value,o=>(m(),h(d,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),s("div",rt,[e[46]||(e[46]=s("div",{class:"item-label"},"跨专业",-1)),r(n,{modelValue:$.value,"onUpdate:modelValue":e[10]||(e[10]=o=>$.value=o),placeholder:"请选择是否跨专业",class:"full-width"},{default:u(()=>[r(d,{label:"是",value:"1"}),r(d,{label:"否",value:"2"})]),_:1},8,["modelValue"])]),s("div",dt,[e[47]||(e[47]=s("div",{class:"item-label"},"培养方式",-1)),r(n,{modelValue:t.studentForm.educationalStyle,"onUpdate:modelValue":e[11]||(e[11]=o=>t.studentForm.educationalStyle=o),placeholder:"请选择培养方式",class:"full-width"},{default:u(()=>[r(d,{label:"全日制",value:0}),r(d,{label:"非全日制",value:1})]),_:1},8,["modelValue"])]),s("div",nt,[e[48]||(e[48]=s("div",{class:"item-label"},"标签",-1)),r(F,{modelValue:t.studentForm.tags,"onUpdate:modelValue":e[12]||(e[12]=o=>t.studentForm.tags=o),options:B.value,placeholder:"请选择标签",clearable:"",filterable:"",props:{checkStrictly:!0,value:"id",label:"name",children:"children",expandTrigger:"hover",multiple:!0,emitPath:!1},disabled:t.dialogType==="detail",class:"full-width",onChange:fe},null,8,["modelValue","options","disabled"])])])])]),s("div",it,[s("div",ut,[s("div",mt,[e[52]||(e[52]=s("div",{class:"step-num-tag"},[s("span",null,"02"),s("div",{class:"tag-text"},"本科成绩情况")],-1)),s("div",ct,[r(g,{type:"primary",class:"action-button",onClick:le},{default:u(()=>e[51]||(e[51]=[A("创建")])),_:1})])]),s("div",gt,[s("div",pt,[(m(!0),f(j,null,S(t.studentForm.undergraduateTranscript,o=>(m(),f("div",{class:"score-item",key:o.id,draggable:!0,onDragstart:M=>X(M,o),onDragover:e[13]||(e[13]=Te(M=>q(M),["prevent"])),onDrop:M=>ee(M,o),onDragend:te},[s("div",ft,Le(o.title),1),s("div",Ft,[r(a,{modelValue:o.score,"onUpdate:modelValue":M=>o.score=M,placeholder:"请输入成绩"},null,8,["modelValue","onUpdate:modelValue"])]),r(x,{onClick:M=>oe(o.id),class:"score-close",color:"#1BB394",size:"14"},{default:u(()=>[r(p)]),_:2},1032,["onClick"])],40,vt))),128))])])])]),s("div",ht,[s("div",bt,[e[59]||(e[59]=s("div",{class:"step-num-tag"},[s("span",null,"03"),s("div",{class:"tag-text"},"英语基础")],-1)),s("div",yt,[s("div",jt,[s("div",St,[e[53]||(e[53]=s("div",{class:"english-label"},"高考英语成绩",-1)),r(a,{modelValue:t.studentForm.englishScore,"onUpdate:modelValue":e[14]||(e[14]=o=>t.studentForm.englishScore=o),placeholder:"请输入高考英语成绩",class:"full-width"},null,8,["modelValue"])]),s("div",Mt,[e[54]||(e[54]=s("div",{class:"english-label"},"大学四级",-1)),r(a,{modelValue:t.studentForm.cet4,"onUpdate:modelValue":e[15]||(e[15]=o=>t.studentForm.cet4=o),placeholder:"请输入四级成绩",class:"full-width"},null,8,["modelValue"])]),s("div",Vt,[e[55]||(e[55]=s("div",{class:"english-label"},"大学六级",-1)),r(a,{modelValue:t.studentForm.cet6,"onUpdate:modelValue":e[16]||(e[16]=o=>t.studentForm.cet6=o),placeholder:"请输入六级成绩",class:"full-width"},null,8,["modelValue"])]),s("div",xt,[e[56]||(e[56]=s("div",{class:"english-label"},"托福",-1)),r(a,{modelValue:t.studentForm.tofelScore,"onUpdate:modelValue":e[17]||(e[17]=o=>t.studentForm.tofelScore=o),placeholder:"请输入托福成绩",class:"full-width"},null,8,["modelValue"])])]),s("div",Dt,[s("div",Ct,[e[57]||(e[57]=s("div",{class:"english-label"},"雅思",-1)),r(a,{modelValue:t.studentForm.ieltsScore,"onUpdate:modelValue":e[18]||(e[18]=o=>t.studentForm.ieltsScore=o),placeholder:"请输入雅思成绩",class:"full-width"},null,8,["modelValue"])]),s("div",_t,[e[58]||(e[58]=s("div",{class:"english-label"},"英语能力",-1)),r(n,{modelValue:t.studentForm.englishAbility,"onUpdate:modelValue":e[19]||(e[19]=o=>t.studentForm.englishAbility=o),placeholder:"请选择英语能力",class:"full-width","popper-class":"english-ability-dropdown"},{default:u(()=>[r(d,{label:"一般",value:"一般"}),r(d,{label:"良好",value:"良好"}),r(d,{label:"优秀",value:"优秀"})]),_:1},8,["modelValue"])])])])])]),s("div",Nt,[s("div",Ut,[e[65]||(e[65]=s("div",{class:"step-num-tag"},[s("span",null,"04"),s("div",{class:"tag-text"},"目标院校梯度")],-1)),s("div",kt,[s("div",Tt,[e[60]||(e[60]=s("div",{class:"item-label"},"地区倾向",-1)),r(n,{modelValue:t.studentForm.region,"onUpdate:modelValue":e[20]||(e[20]=o=>t.studentForm.region=o),placeholder:"请选择地区倾向",multiple:"",class:"full-width"},{default:u(()=>[r(d,{label:"A区",value:"A区"}),r(d,{label:"B区",value:"B区"})]),_:1},8,["modelValue"])]),s("div",Lt,[e[61]||(e[61]=s("div",{class:"item-label"},"省份选择",-1)),r(n,{modelValue:t.studentForm.targetProvinces,"onUpdate:modelValue":e[21]||(e[21]=o=>t.studentForm.targetProvinces=o),placeholder:"请选择省份",multiple:"",class:"full-width"},{default:u(()=>[(m(!0),f(j,null,S(Ae(ze),o=>(m(),h(d,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),s("div",Ot,[e[62]||(e[62]=s("div",{class:"item-label"},"梦校",-1)),r(n,{modelValue:t.studentForm.targetSchool,"onUpdate:modelValue":e[22]||(e[22]=o=>t.studentForm.targetSchool=o),placeholder:"输入关键字搜索院校",filterable:"",remote:"","reserve-keyword":"","remote-method":ie,loading:U.value,class:"full-width",onChange:ve},{default:u(()=>[(m(!0),f(j,null,S(b.value,o=>(m(),h(d,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),s("div",At,[e[63]||(e[63]=s("div",{class:"item-label"},"院校层次",-1)),r(n,{modelValue:t.studentForm.schoolLevel,"onUpdate:modelValue":e[23]||(e[23]=o=>t.studentForm.schoolLevel=o),placeholder:"请选择院校层次",class:"full-width",multiple:""},{default:u(()=>[r(d,{label:"985",value:"985"}),r(d,{label:"211",value:"211"}),r(d,{label:"双一流",value:"双一流"})]),_:1},8,["modelValue"])]),s("div",Et,[e[64]||(e[64]=s("div",{class:"item-label"},"专业课指定参考书",-1)),r(a,{modelValue:t.studentForm.referenceBooks,"onUpdate:modelValue":e[24]||(e[24]=o=>t.studentForm.referenceBooks=o),placeholder:"请输入专业课指定参考书"},null,8,["modelValue"])])])])]),s("div",It,[s("div",Bt,[e[69]||(e[69]=s("div",{class:"step-num-tag"},[s("span",null,"05"),s("div",{class:"tag-text"},"考研成绩预估")],-1)),s("div",wt,[e[66]||(e[66]=Ee('<div class="table-header" data-v-03ae811b><div class="th-cell" data-v-03ae811b>政治</div><div class="th-cell" data-v-03ae811b>英语</div><div class="th-cell" data-v-03ae811b>业务课一</div><div class="th-cell" data-v-03ae811b>业务课二</div><div class="th-cell" data-v-03ae811b>总分</div></div>',1)),s("div",Pt,[s("div",Yt,[r(a,{modelValue:t.studentForm.politics,"onUpdate:modelValue":e[25]||(e[25]=o=>t.studentForm.politics=o),class:"table-input"},null,8,["modelValue"])]),s("div",$t,[r(a,{modelValue:t.studentForm.englishS,"onUpdate:modelValue":e[26]||(e[26]=o=>t.studentForm.englishS=o),class:"table-input"},null,8,["modelValue"])]),s("div",zt,[r(a,{modelValue:t.studentForm.englishType,"onUpdate:modelValue":e[27]||(e[27]=o=>t.studentForm.englishType=o),class:"table-input"},null,8,["modelValue"])]),s("div",Rt,[r(a,{modelValue:t.studentForm.mathType,"onUpdate:modelValue":e[28]||(e[28]=o=>t.studentForm.mathType=o),class:"table-input"},null,8,["modelValue"])]),s("div",Gt,[r(a,{modelValue:t.studentForm.totalScore,"onUpdate:modelValue":e[29]||(e[29]=o=>t.studentForm.totalScore=o),class:"table-input",disabled:""},null,8,["modelValue"])])])]),s("div",Zt,[e[67]||(e[67]=s("div",{class:"demands-label"},"个性化需求",-1)),r(a,{modelValue:t.studentForm.personalNeeds,"onUpdate:modelValue":e[30]||(e[30]=o=>t.studentForm.personalNeeds=o),type:"textarea",rows:1,placeholder:"",class:"demands-input"},null,8,["modelValue"])]),s("div",Ht,[e[68]||(e[68]=s("div",{class:"advice-label"},"薄弱模块",-1)),r(a,{modelValue:t.studentForm.weakModules,"onUpdate:modelValue":e[31]||(e[31]=o=>t.studentForm.weakModules=o),type:"textarea",rows:1,placeholder:"",class:"advice-input"},null,8,["modelValue"])])])])])]),r(je,{title:"添加成绩项",modelValue:C.value,"onUpdate:modelValue":e[35]||(e[35]=o=>C.value=o),width:"30%","close-on-click-modal":!1},{footer:u(()=>[s("span",Jt,[r(g,{onClick:e[34]||(e[34]=o=>C.value=!1)},{default:u(()=>e[70]||(e[70]=[A("取消")])),_:1}),r(g,{type:"primary",style:{"background-color":"#1bb394"},onClick:se},{default:u(()=>e[71]||(e[71]=[A("确认")])),_:1})])]),default:u(()=>[r(ye,{model:v,"label-width":"80px"},{default:u(()=>[r(R,{label:"科目名称"},{default:u(()=>[r(a,{modelValue:v.title,"onUpdate:modelValue":e[32]||(e[32]=o=>v.title=o),placeholder:"请输入科目名称"},null,8,["modelValue"])]),_:1}),r(R,{label:"成绩"},{default:u(()=>[r(a,{modelValue:v.score,"onUpdate:modelValue":e[33]||(e[33]=o=>v.score=o),placeholder:"请输入成绩"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},dl=Se(Kt,[["__scopeId","data-v-03ae811b"]]);export{dl as S};
