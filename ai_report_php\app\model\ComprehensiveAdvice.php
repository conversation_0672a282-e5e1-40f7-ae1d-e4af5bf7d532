<?php

namespace app\model;

use think\Model;

/**
 * 综合建议模型
 */
class ComprehensiveAdvice extends Model
{
    protected $name = 'comprehensive_advice';
    
    // 设置字段信息
    protected $schema = [
        'id'             => 'int',
        'report_id'      => 'int',
        'advice_content' => 'text',
        'created_at'     => 'datetime',
        'updated_at'     => 'datetime',
    ];
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    /**
     * 根据报告ID删除综合建议数据
     *
     * @param int $reportId 报告ID
     * @return bool
     */
    public static function deleteByReportId($reportId)
    {
        return self::where('report_id', $reportId)->delete();
    }

    /**
     * 插入综合建议数据
     *
     * @param int $reportId 报告ID
     * @param string $adviceContent 综合建议内容
     * @return bool
     */
    public static function insertAdvice($reportId, $adviceContent)
    {
        if (empty($adviceContent)) {
            return true; // 如果没有内容，返回成功
        }

        $data = [
            'report_id' => $reportId,
            'advice_content' => str_replace('\n', '</br>', $adviceContent),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ];

        $model = new self();
        return $model->save($data);
    }

    /**
     * 根据报告ID获取综合建议数据
     *
     * @param int $reportId 报告ID
     * @return array|null
     */
    public static function getByReportId($reportId)
    {
        $result = self::where('report_id', $reportId)
            ->order('id', 'desc')
            ->find();
            
        return $result ? $result->toArray() : null;
    }
}
