<template>
  <div class="major-analysis-container" v-show="visible">
    <div class="step-section">
      <div class="step-header">第二部分：专业分析</div>
      <div class="step-content">
        <div ref="echartsARef" class="echarts-box"></div>
        <div ref="echartsBRef" class="echarts-box"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  onMounted,
  onBeforeUnmount,
  nextTick,
  watch,
  type Ref,
} from "vue";
import * as echarts from "echarts";
import type { ECharts, EChartsOption } from "echarts";

// 定义 props 接口
interface NationalLineData {
  subject_code: string;
  subject_name: string;
  first_level_discipline: string;
  years: string[];
  a_total: number[];
  a_single_100: number[];
  a_single_over100: number[];
  b_total: number[];
  b_single_100: number[];
  b_single_over100: number[];
}

interface ReportData {
  targetMajorName?: string;
  majorCode?: string;
  firstLevelSubject?: string;
}

// 定义 props
const props = defineProps<{
  nationalLineData?: NationalLineData;
  reportData?: ReportData;
  visible?: boolean;
}>();

// 图表相关
const echartsARef = ref<HTMLDivElement>();
const echartsBRef = ref<HTMLDivElement>();
let chartA: ECharts | null = null;
let chartB: ECharts | null = null;

// 生成图表配置
const chartOption = (
  title?: string | null,
  isARegion: boolean = true
): EChartsOption => {
  console.log(`生成${isARegion ? "A区" : "B区"}图表配置`);

  // 如果没有传入国家线数据，使用默认数据
  const nationalData = props.nationalLineData || {
    subject_code: "",
    subject_name: "",
    first_level_discipline: "",
    years: [],
    a_total: [],
    a_single_100: [],
    a_single_over100: [],
    b_total: [],
    b_single_100: [],
    b_single_over100: [],
  };

  console.log("国家线数据:", nationalData);

  // 根据区域选择对应的数据
  const years: string[] =
    nationalData.years.length > 0
      ? nationalData.years
      : ["2021", "2022", "2023", "2024", "2025"];

  const totalScores: number[] = isARegion
    ? nationalData.a_total.length > 0
      ? nationalData.a_total
      : [360, 370, 360, 370, 370]
    : nationalData.b_total.length > 0
    ? nationalData.b_total
    : [340, 350, 340, 350, 350];

  const single100Scores: number[] = isARegion
    ? nationalData.a_single_100.length > 0
      ? nationalData.a_single_100
      : [60, 65, 60, 65, 65]
    : nationalData.b_single_100.length > 0
    ? nationalData.b_single_100
    : [55, 60, 55, 60, 60];

  const singleOver100Scores: number[] = isARegion
    ? nationalData.a_single_over100.length > 0
      ? nationalData.a_single_over100
      : [90, 95, 90, 95, 95]
    : nationalData.b_single_over100.length > 0
    ? nationalData.b_single_over100
    : [85, 90, 85, 90, 90];

  console.log(`${isARegion ? "A区" : "B区"}图表数据:`, {
    years,
    totalScores,
    single100Scores,
    singleOver100Scores,
  });

  // 构建标题
  const regionText: string = isARegion ? "A区" : "B区";

  // 构建标题，添加一级学科列
  const chartTitle: string = `${regionText}  门类：${nationalData.subject_name}  一级学科：${nationalData.first_level_discipline}`;

  return {
    title: {
      text: title || chartTitle,
      left: "left",
      textStyle: {
        fontSize: 14,
        fontWeight: "bold",
        color: "#1bb394",
      },
      top: 10,
    },
    grid: {
      left: 20,
      right: 20,
      top: 70,
      bottom: 20,
      containLabel: true,
    },
    legend: {
      data: ["总分", "单科(满分=100)", "单科(满分>100)"],
      right: 10,
      top: 10,
      icon: "rect",
      itemWidth: 16,
      itemHeight: 8,
      textStyle: {
        fontSize: 12,
      },
    },
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(0,0,0,0.7)",
      borderRadius: 8,
      textStyle: { color: "#fff" },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: years,
      axisLine: { lineStyle: { color: "#1bb394" } },
      axisLabel: { color: "#666" },
    },
    yAxis: {
      type: "value",
      min: 0,
      max: 400,
      interval: 50,
      splitNumber: 8,
      splitLine: {
        show: true,
        lineStyle: {
          color: "#eee",
          type: "dashed",
        },
      },
      axisLine: { show: false },
      axisLabel: {
        color: "#666",
        fontSize: 12,
        margin: 20,
      },
    },
    series: [
      {
        name: "总分",
        type: "line",
        smooth: true,
        symbol: "circle",
        symbolSize: 6,
        areaStyle: {
          color: "rgba(255, 153, 0, 0.15)",
          origin: "start",
        },
        lineStyle: { color: "#ff9900", width: 2 },
        label: {
          show: true,
          position: "top",
          fontSize: 12,
          color: "#ff9900",
          fontWeight: "bold",
          offset: [0, -20],
          formatter: function (params: any) {
            return params.value;
          },
        },
        data: totalScores,
      },
      {
        name: "单科(满分=100)",
        type: "line",
        smooth: true,
        symbol: "circle",
        symbolSize: 6,
        areaStyle: {
          color: "rgba(27, 179, 148, 0.15)",
          origin: "start",
        },
        lineStyle: { color: "#1bb394", width: 2 },
        label: {
          show: true,
          position: "top",
          fontSize: 12,
          color: "#1bb394",
          fontWeight: "bold",
          offset: [0, -40],
          formatter: function (params: any) {
            return params.value;
          },
        },
        data: single100Scores,
      },
      {
        name: "单科(满分>100)",
        type: "line",
        smooth: true,
        symbol: "circle",
        symbolSize: 6,
        areaStyle: {
          color: "rgba(255, 99, 132, 0.10)",
          origin: "start",
        },
        lineStyle: { color: "#ff6384", width: 2 },
        label: {
          show: true,
          position: "top",
          fontSize: 12,
          color: "#ff6384",
          fontWeight: "bold",
          offset: [0, -5],
          formatter: function (params: any) {
            return params.value;
          },
        },
        data: singleOver100Scores,
      },
    ],
  };
};

// 初始化图表
const initCharts = (): void => {
  console.log("initCharts 被调用，visible:", props.visible);

  // 检查组件是否可见，如果不可见则不初始化图表
  if (props.visible === false) {
    console.log("组件不可见，跳过图表初始化");
    return;
  }

  // 使用 setTimeout 确保 DOM 已完全渲染并且有正确的尺寸
  setTimeout(() => {
    console.log("开始初始化图表...");
    console.log("echartsARef.value:", echartsARef.value);
    console.log("echartsBRef.value:", echartsBRef.value);
    console.log("当前 visible:", props.visible);

    if (echartsARef.value) {
      // 检查容器是否有尺寸
      const rect = echartsARef.value.getBoundingClientRect();
      console.log("A区图表容器尺寸:", rect);

      if (rect.width > 0 && rect.height > 0) {
        try {
          // 如果已经初始化过，先销毁
          if (chartA) {
            console.log("销毁已存在的A区图表");
            chartA.dispose();
          }
          console.log("开始初始化A区图表");
          chartA = echarts.init(echartsARef.value);
          const optionA = chartOption(null, true);
          console.log("A区图表配置:", optionA);
          chartA.setOption(optionA); // A区图表
          console.log("A区图表初始化成功");
        } catch (error) {
          console.error("A区图表初始化失败:", error);
        }
      } else {
        console.warn("A区图表容器尺寸为0，延迟初始化");
        // 如果容器尺寸为0，延迟重试
        setTimeout(() => initCharts(), 200);
        return;
      }
    } else {
      console.warn("A区图表容器不存在");
    }

    if (echartsBRef.value) {
      // 检查容器是否有尺寸
      const rect = echartsBRef.value.getBoundingClientRect();
      console.log("B区图表容器尺寸:", rect);

      if (rect.width > 0 && rect.height > 0) {
        try {
          // 如果已经初始化过，先销毁
          if (chartB) {
            console.log("销毁已存在的B区图表");
            chartB.dispose();
          }
          console.log("开始初始化B区图表");
          chartB = echarts.init(echartsBRef.value);
          const optionB = chartOption(null, false);
          console.log("B区图表配置:", optionB);
          chartB.setOption(optionB); // B区图表
          console.log("B区图表初始化成功");
        } catch (error) {
          console.error("B区图表初始化失败:", error);
        }
      } else {
        console.warn("B区图表容器尺寸为0，延迟初始化");
        // 如果容器尺寸为0，延迟重试
        setTimeout(() => initCharts(), 200);
        return;
      }
    } else {
      console.warn("B区图表容器不存在");
    }
  }, 200); // 增加延迟时间，确保DOM完全渲染
};

// 处理窗口大小变化
const handleResize = (): void => {
  // 如果组件不可见，不需要调整图表大小
  if (props.visible === false) return;

  // 使用 setTimeout 确保在 DOM 更新后调整大小
  setTimeout(() => {
    if (chartA) {
      chartA.resize();
    }
    if (chartB) {
      chartB.resize();
    }

    // 如果图表未初始化或者大小不正确，重新初始化
    if ((!chartA || !chartB) && props.visible !== false) {
      initCharts();
    }
  }, 0);
};

// 监听 visible 变化
watch(
  () => props.visible,
  (newVal: boolean | undefined) => {
    if (newVal !== false) {
      // 当组件变为可见时，确保图表正确初始化和渲染
      nextTick(() => {
        initCharts();
      });
    }
  }
);

// 监听国家线数据变化
watch(
  () => props.nationalLineData,
  (newData) => {
    if (newData && props.visible !== false) {
      console.log("国家线数据变化，重新初始化图表");
      nextTick(() => {
        initCharts();
      });
    }
  },
  { deep: true }
);

// 监听 reportData 变化
watch(
  () => props.reportData,
  (newData) => {
    if (newData && props.visible !== false) {
      console.log("报告数据变化，重新初始化图表");
      nextTick(() => {
        initCharts();
      });
    }
  },
  { deep: true }
);

onMounted(() => {
  nextTick(() => {
    initCharts();
    window.addEventListener("resize", handleResize);
  });
});

// 清理资源
onBeforeUnmount(() => {
  window.removeEventListener("resize", handleResize);

  // 销毁图表实例
  if (chartA) {
    chartA.dispose();
    chartA = null;
  }
  if (chartB) {
    chartB.dispose();
    chartB = null;
  }
});

// 暴露方法给父组件
defineExpose({
  initCharts,
  handleResize,
  // 新增：获取图表配置的方法，用于预览
  getChartOption: (isARegion: boolean = true) => {
    return chartOption(null, isARegion);
  },
  // 新增：获取国家线数据
  getNationalLineData: () => {
    return props.nationalLineData;
  },
  // 新增：获取报告数据
  getReportData: () => {
    return props.reportData;
  },
});
</script>

<style lang="less" scoped>
.major-analysis-container {
  width: 100%;
  padding: 0 160px;
}

/* 步骤部分样式 */
.step-section {
  margin-bottom: 30px;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.step-header {
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-image: url("@/assets/images/step-bg.png");
  background-repeat: no-repeat;
  padding-left: 92px;
  color: #fff;
  font-weight: bold;
}

.step-content {
  width: 100%;
  padding: 20px 0;
}

/* 图表样式 */
.echarts-box {
  width: 100%;
  height: 280px;
  margin: 0 auto 18px auto;
  border: 1px solid #1bb394;
  border-radius: 12px;
  background: #fff;
  box-sizing: border-box;
}
</style>
