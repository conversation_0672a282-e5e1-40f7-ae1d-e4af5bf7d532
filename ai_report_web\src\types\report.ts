// 报告相关类型定义

export interface Report {
  id: number;
  title: string;
  content?: string;
  student_name?: string;
  student_id?: number;
  create_time?: number;
  update_time?: number;
  status?: number;
  [key: string]: any;
}

export interface ReportListParams {
  page?: number;
  limit?: number;
  keyword?: string;
  status?: number;
  start_time?: string;
  end_time?: string;
}

export interface ReportListResult {
  total: number;
  list: Report[];
}

export interface GenerateReportParams {
  student_id: number;
  student_name: string;
  school_id?: number;
  school_name?: string;
  major_id?: number;
  major_name?: string;
  target_major_id?: number;
  target_major_name?: string;
  [key: string]: any;
}

export interface getMsgStrParams {
  base: object;
  tar: object;
  will?: object;
  english?: object;
  math?: object;
  remark?: string;
  [key: string]: any;
}
export interface getSchoolAndMajorParams {
  base: object;
  tar: object;
  will?: object;
  english?: object;
  math?: object;
  remark?: string;
  school?: string;
  [key: string]: any;
}

export interface SearchParams {
  name?: string;
  major_name?: string;
  school_name?: string;
  class?: string;
  page?: number;
  limit?: number;
  [key: string]: any;
}

export interface ReportNumResult {
  count?: number;
}

