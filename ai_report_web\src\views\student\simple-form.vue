<template>
  <div class="student-form-container">
    <el-button type="primary" @click="dialogVisible = true">打开表单</el-button>
    
    <el-dialog
      v-model="dialogVisible"
      title="学员信息"
      width="80%"
      top="5vh"
    >
      <div class="form-container">
        <el-tabs v-model="activeTab">
          <el-tab-pane 
            v-for="(section, index) in formSections" 
            :key="index"
            :label="section"
            :name="'tab-' + index"
          >
            <div class="section-content">
              <template v-if="index === 0">
                <!-- 个人基础信息 -->
                <el-form :model="studentForm" label-width="100px">
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="姓名">
                        <el-input v-model="studentForm.name" placeholder="请输入学员姓名"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="性别">
                        <el-select v-model="studentForm.sex" placeholder="请选择性别" style="width: 100%">
                          <el-option label="男" value="1"></el-option>
                          <el-option label="女" value="2"></el-option>
                          <el-option label="其他" value="3"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="联系方式">
                        <el-input v-model="studentForm.phone" placeholder="请输入手机号码"></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </template>
              
              <template v-else-if="index === 1">
                <!-- 本科成绩情况 -->
                <el-form :model="studentForm" label-width="100px">
                  <div class="subsection-title">数学基础</div>
                  <el-row :gutter="20">
                    <el-col :span="6" v-for="(item, idx) in studentForm.mathScores" :key="'math-' + idx">
                      <el-form-item :label="item.title">
                        <el-input v-model="item.score" placeholder="请输入成绩"></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </template>
              
              <template v-else>
                <!-- 其他部分 -->
                <el-form :model="studentForm" label-width="100px">
                  <el-form-item :label="section">
                    <el-input v-model="studentForm.name" :placeholder="'请输入' + section"></el-input>
                  </el-form-item>
                </el-form>
              </template>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';

// 对话框相关
const dialogVisible = ref(false);
const activeTab = ref('tab-0');

// 表单部分相关
const formSections = [
  "个人基础信息",
  "本科成绩情况",
  "英语基础",
  "目标院校倾向",
  "考研成绩预估",
  "个性化需求"
];

// 学员表单
const studentForm = reactive({
  // 基本信息
  name: "",
  sex: "1", // 1-男, 2-女, 3-其他
  phone: "",
  
  // 本科成绩情况
  mathScores: [
    { id: 1, title: "高数(上)期末考试", score: "" },
    { id: 2, title: "高数(下)期末考试", score: "" },
    { id: 3, title: "积分论期末考试", score: "" },
    { id: 4, title: "线性代数期末考试", score: "" },
  ],
});

// 提交表单
const submitForm = () => {
  console.log('表单提交', studentForm);
  dialogVisible.value = false;
};
</script>

<style scoped>
.student-form-container {
  padding: 20px;
}

.form-container {
  max-height: 70vh;
  overflow-y: auto;
}

.section-content {
  padding: 20px 0;
}

.subsection-title {
  font-size: 14px;
  font-weight: bold;
  color: #1bb394;
  margin: 10px 0;
  padding-bottom: 5px;
  border-bottom: 1px dashed #e0e0e0;
}
</style>
