<template>
    <div class="dashboard-container">
        <!-- 统计卡片 -->
        <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6">
                <el-card class="stat-card">
                    <div class="card-body">
                        <div class="card-icon" style="background-color: #409EFF">
                            <el-icon>
                                <User />
                            </el-icon>
                        </div>
                        <div class="card-content">
                            <div class="card-title">学员总数</div>
                            <div class="card-value">128</div>
                            <div class="card-desc">较上周 <span style="color: #67C23A">+12%</span></div>
                        </div>
                    </div>
                </el-card>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6">
                <el-card class="stat-card">
                    <div class="card-body">
                        <div class="card-icon" style="background-color: #67C23A">
                            <el-icon>
                                <Document />
                            </el-icon>
                        </div>
                        <div class="card-content">
                            <div class="card-title">报告总数</div>
                            <div class="card-value">87</div>
                            <div class="card-desc">较上周 <span style="color: #67C23A">+5%</span></div>
                        </div>
                    </div>
                </el-card>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6">
                <el-card class="stat-card">
                    <div class="card-body">
                        <div class="card-icon" style="background-color: #E6A23C">
                            <el-icon>
                                <School />
                            </el-icon>
                        </div>
                        <div class="card-content">
                            <div class="card-title">目标院校</div>
                            <div class="card-value">54</div>
                            <div class="card-desc">较上周 <span style="color: #67C23A">+8%</span></div>
                        </div>
                    </div>
                </el-card>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6">
                <el-card class="stat-card">
                    <div class="card-body">
                        <div class="card-icon" style="background-color: #16b788">
                            <el-icon>
                                <Money />
                            </el-icon>
                        </div>
                        <div class="card-content">
                            <div class="card-title">剩余额度</div>
                            <div class="card-value">200</div>
                            <div class="card-desc">较上周 <span style="color: #F56C6C">-10%</span></div>
                        </div>
                    </div>
                </el-card>
            </el-col>
        </el-row>

        <!-- 图表区域 -->
        <el-row :gutter="20" class="chart-row">
            <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                <el-card class="chart-card">
                    <template #header>
                        <div class="card-header">
                            <span>报告生成统计</span>
                            <el-radio-group v-model="visitTimeRange" size="small">
                                <el-radio-button label="week">本周</el-radio-button>
                                <el-radio-button label="month">本月</el-radio-button>
                                <el-radio-button label="year">本年</el-radio-button>
                            </el-radio-group>
                        </div>
                    </template>
                    <div class="chart-placeholder">
                        <!-- 实际项目中这里应该是真实的图表组件 -->
                        <el-empty description="报告生成数据图表" />
                    </div>
                </el-card>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                <el-card class="chart-card">
                    <template #header>
                        <div class="card-header">
                            <span>目标院校分布</span>
                            <el-radio-group v-model="salesTimeRange" size="small">
                                <el-radio-button label="week">本周</el-radio-button>
                                <el-radio-button label="month">本月</el-radio-button>
                                <el-radio-button label="year">本年</el-radio-button>
                            </el-radio-group>
                        </div>
                    </template>
                    <div class="chart-placeholder">
                        <!-- 实际项目中这里应该是真实的图表组件 -->
                        <el-empty description="目标院校分布图表" />
                    </div>
                </el-card>
            </el-col>
        </el-row>

        <!-- 最近活动和待办事项 -->
        <el-row :gutter="20" class="activity-row">
            <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                <el-card class="activity-card">
                    <template #header>
                        <div class="card-header">
                            <span>最近活动</span>
                            <el-button type="primary" link>查看全部</el-button>
                        </div>
                    </template>
                    <div class="timeline-container">
                        <el-timeline>
                            <el-timeline-item v-for="(activity, index) in recentActivities" :key="index" :type="activity.type" :color="activity.color" :timestamp="activity.timestamp">
                                {{ activity.content }}
                            </el-timeline-item>
                        </el-timeline>
                    </div>
                </el-card>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                <el-card class="activity-card">
                    <template #header>
                        <div class="card-header">
                            <span>待办事项</span>
                            <el-button type="primary" link>添加</el-button>
                        </div>
                    </template>
                    <div class="todo-container">
                        <el-checkbox-group v-model="checkedTodos">
                            <div v-for="(todo, index) in todos" :key="index" class="todo-item">
                                <el-checkbox :label="todo.id">{{ todo.content }}</el-checkbox>
                                <span class="todo-time">{{ todo.time }}</span>
                            </div>
                        </el-checkbox-group>
                    </div>
                </el-card>
            </el-col>
        </el-row>
    </div>
</template>

<script setup>
import { ref } from 'vue'

// 时间范围
const visitTimeRange = ref('week')
const salesTimeRange = ref('month')

// 最近活动数据
const recentActivities = ref([
    {
        content: '完成了"王敏"学员的AI择校报告',
        timestamp: '2023-10-20 20:46',
        type: 'success',
        color: '#67C23A'
    },
    {
        content: '新增了"数学"考试科目标签',
        timestamp: '2023-10-19 16:32',
        type: 'warning',
        color: '#E6A23C'
    },
    {
        content: '修复了报告生成模块的问题',
        timestamp: '2023-10-18 10:12',
        type: 'primary',
        color: '#409EFF'
    },
    {
        content: '更新了学员标签系统',
        timestamp: '2023-10-17 08:30',
        type: 'info',
        color: '#909399'
    }
])

// 待办事项数据
const todos = ref([
    { id: 1, content: '完成张三的择校报告生成', time: '今天' },
    { id: 2, content: '处理学员反馈的问题', time: '明天' },
    { id: 3, content: '优化系统报告生成速度', time: '本周五' },
    { id: 4, content: '与招生老师讨论新功能', time: '下周一' },
    { id: 5, content: '准备月度工作总结', time: '下周三' }
])
const checkedTodos = ref([1, 3])
</script>

<style scoped>
.dashboard-container {
    padding: 10px;
}

.stat-card {
    margin-bottom: 20px;
    border-radius: 8px;
}

.card-body {
    display: flex;
    align-items: center;
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 16px;
    color: white;
    font-size: 24px;
}

.card-content {
    flex: 1;
}

.card-title {
    font-size: 14px;
    color: #909399;
    margin-bottom: 6px;
}

.card-value {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 4px;
}

.card-desc {
    font-size: 12px;
    color: #909399;
}

.chart-row,
.activity-row {
    margin-top: 10px;
}

.chart-card,
.activity-card {
    margin-bottom: 20px;
    height: 350px;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chart-placeholder {
    height: 280px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.timeline-container {
    padding: 10px;
    height: 280px;
    overflow-y: auto;
}

.todo-container {
    padding: 10px;
    height: 280px;
    overflow-y: auto;
}

.todo-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.todo-time {
    color: #999;
    font-size: 12px;
}
</style>