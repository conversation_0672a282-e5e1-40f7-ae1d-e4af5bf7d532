const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/PreviewPdf-BQ_I87f2.js","assets/index-Cu_gl4pu.js","assets/index-C-H9zjQH.css","assets/index-YsyrxmwF.js","assets/_plugin-vue_export-helper-BCEt7Ct6.js","assets/_plugin-vue_export-helper-CMRK1q-d.css","assets/report-CU5jWwkq.js","assets/school-BRpLdP4V.js","assets/school-C5IeuJ3V.css","assets/PreviewPdf-DWcGHe4B.css"])))=>i.map(i=>d[i]);
import{T as rs,m as Zt,u as It,r as _,x as es,n as ts,p as Ae,U as yt,c as g,o as c,b as e,d as Ft,F as te,s as ve,t as j,q as A,N as Se,e as m,j as Nt,w as M,h as qt,y as at,k as $,V as ss,l as L,A as ls,v as os,P as ae,W as Ut,X as gt,z as ds,a as Wt,Y as cs,Z as us,_ as vs,M as ms,R as dt,D as gs,G as Yt,$ as ps,E as ys,g as fs,a0 as pt,S as _s}from"./index-Cu_gl4pu.js";import{_ as Ot}from"./_plugin-vue_export-helper-BCEt7Ct6.js";import{g as hs,a as bs,b as Gt}from"./school-BRpLdP4V.js";/* empty css                     *//* empty css                   *//* empty css                  */import{c as ws,d as Jt,e as Kt,t as ks}from"./student-Db39rDv4.js";/* empty css                 */import{u as Ss,M as $s,P as Cs,a as Ms}from"./PreviewPdf-BQ_I87f2.js";import{p as Rt}from"./province-Do8ZIeJL.js";import"./index-YsyrxmwF.js";import{b as xs,s as Vs,c as Xt,d as Qt,e as js,f as Ts,h as Ps,u as As,i as Ds,j as zs,k as Ls,l as Es}from"./report-CU5jWwkq.js";const Us=rs("userReport",{state:()=>({reportData:null}),getters:{hasReportData:De=>De.reportData!==null,getReportData:De=>De.reportData},actions:{setReportData(De){this.reportData=De},clearReportData(){this.reportData=null},updateReportData(De){this.reportData&&(this.reportData={...this.reportData,...De})}}}),Rs={class:"content-container"},Fs={class:"step-section"},Ns={class:"step-content"},qs={class:"school-table-container"},Os={class:"school-table-body"},Bs={class:"body-cell"},Hs={class:"body-cell school-name"},Ws={class:"school-tags"},Ys={key:0,class:"tag tag-985"},Gs={key:1,class:"tag tag-211"},Js={key:2,class:"tag tag-double"},Ks={class:"body-cell"},Xs={class:"body-cell"},Qs={class:"body-cell"},Zs={class:"body-cell"},Is={class:"body-cell"},el={class:"body-cell"},tl={class:"body-cell"},sl={class:"step-section"},ll={class:"step-num-tag"},ol={class:"school-detail-card"},al={class:"school-header"},il={class:"school-logo"},nl=["src"],rl={class:"school-info"},dl={class:"school-title"},cl={class:"school-location"},ul={class:"school-tags-row"},vl={key:0,class:"tag tag-double"},ml={key:1,class:"tag tag-985"},gl={key:2,class:"tag tag-211"},pl={class:"school-detail-section"},yl={key:0,class:"detail-item"},fl={class:"item-content"},_l={class:"content-header"},hl={key:0,class:"action-buttons"},bl={key:0},wl={key:1,class:"edit-container"},kl={class:"edit-actions"},Sl={key:1,class:"detail-item"},$l={class:"item-content"},Cl={class:"content-header"},Ml={key:0,class:"action-buttons"},xl={key:0},Vl={key:1,class:"edit-container"},jl={class:"edit-actions"},Tl={key:2,class:"detail-item"},Pl={class:"item-content"},Al={class:"content-header"},Dl={key:0,class:"action-buttons"},zl={key:0},Ll={key:1,class:"edit-container"},El={class:"edit-actions"},Ul={class:"school-detail-section"},Rl={key:0,class:"detail-item"},Fl={class:"item-content"},Nl={class:"content-header"},ql={key:0,class:"action-buttons"},Ol={key:0},Bl={key:1,class:"edit-container"},Hl={class:"edit-actions"},Wl={key:1,class:"detail-item"},Yl={class:"item-content"},Gl={class:"content-header"},Jl={key:0,class:"action-buttons"},Kl={key:0},Xl={key:1,class:"edit-container"},Ql={class:"edit-actions"},Zl={key:2,class:"detail-item"},Il={class:"item-content"},eo={class:"content-header"},to={key:0,class:"action-buttons"},so={key:0},lo={key:1,class:"edit-container"},oo={class:"edit-actions"},ao={class:"step-content-zs"},io={key:0,class:"admission-title"},no={key:1,class:"admission-table"},ro={class:"admission-table"},co={class:"reexam-container"},uo={class:"reexam-card"},vo={class:"reexam-header"},mo={key:0,class:"action-buttons"},go={class:"reexam-content"},po={key:0},yo={key:1,class:"edit-container"},fo={class:"edit-actions"},_o={class:"admission-table"},ho={class:"reexam-container"},bo={class:"reexam-card"},wo={class:"reexam-header"},ko={key:0,class:"action-buttons"},So={class:"reexam-content"},$o={key:0},Co={key:1,class:"edit-container"},Mo={class:"edit-actions"},xo={class:"reexam-card"},Vo={class:"reexam-header"},jo={key:0,class:"action-buttons"},To={class:"reexam-content"},Po={key:0},Ao={key:1,class:"edit-container"},Do={class:"edit-actions"},zo={class:"recommend-school-wraper"},Lo={class:"recommend-school-container"},Eo={class:"recommend-school-card"},Uo={class:"recommend-school-header"},Ro={key:0,class:"action-buttons"},Fo={class:"recommend-school-content"},No={key:0},qo={key:1,class:"edit-container"},Oo={class:"edit-actions"},Bo=Zt({__name:"content",setup(De,{expose:it}){const ze=It(),W=Us(),$e=_({subject_code:"",subject_name:"",years:[],a_total:[],a_single_100:[],a_single_over100:[],b_total:[],b_single_100:[],b_single_over100:[]}),Ce=r=>{console.log("更新国家线数据:",r),$e.value=r},se=_(!1);es(se,r=>{r&&at(()=>{})});const Q=()=>{console.log("显示content组件"),se.value=!0,at(()=>{console.log("nextTick后初始化图表")})},_e=()=>{se.value=!1},Re=()=>{se.value=!se.value,se.value&&at(()=>{})};let w=_({recommend_list:[],high_recommend_list:[],school_list:[]});const D=_({}),N=_({}),z=_({}),ne=_({}),oe=_({}),pe=_({}),ge=_({}),he=_({}),re=_({}),Le=_({}),Me=_({}),Fe=_({});_({});const ce=_({}),be=_({}),s=_({}),X=_({}),xe=_({}),Ee=_({}),me=_({}),ye=_({}),we=_({});_({}),_(!1);const C=_(""),v=_(""),V=_(!1),T=async(r,l,a)=>{var G;if(!C.value)throw new Error("报告ID不存在");const f=(G=w.value.recommend_list)==null?void 0:G.find(Z=>Z.school_id===r),H=(f==null?void 0:f.is_high_recommend)||0;await Qt({report_id:C.value,school_id:parseInt(r),field_name:l,field_value:a,is_high_recommend:H})},Y=(r,l)=>{var H,G,Z;if(!C.value){$.error("报告ID不能为空");return}const a=`${r}_${l}`;ce.value[a]=!0;let f=null;if(console.log("查找目标对象，schoolId:",r),console.log("reportData.value:",w.value),console.log("recommend_list 长度:",(G=(H=w.value)==null?void 0:H.recommend_list)==null?void 0:G.length),r==="-1"?w.value.high_recommend_list&&w.value.high_recommend_list.length>0&&(f=w.value.high_recommend_list[0],console.log("找到高性价比推荐对象")):(console.log("在 recommend_list 中查找院校..."),(Z=w.value)!=null&&Z.recommend_list&&(f=w.value.recommend_list.find(O=>parseInt(O.school_id)===parseInt(r)))),!f){$.error("未找到对应的院校信息");return}console.log("找到目标对象:",f.school_name||"高性价比推荐"),xs(C.value,r,l,O=>{r==="-1"?f.reason=O:l==="competition_difficulty"?f.difficulty_analysis=O:l==="suggestions"&&(f.suggest=O)},()=>{ce.value[a]=!1,$.success("重新生成完成")},O=>{console.error("重新生成失败:",O),ce.value[a]=!1,$.error("重新生成失败，请重试")})};let F=0;const b=async r=>{C.value=r,F=0;let l={school_id:"",school_name:"",major_name:"",difficulty_analysis:"",suggest:"",reason:"",admission_stats:{yearly_stats:[],has_data:!1},retest_stats:{},basic_info:{},school_info:{},current_year_retest_list:{list:[],year:null,count:0},current_year_admission_list:{list:[],year:null,count:0}};w.value.recommend_list.push(l);try{let a="";const f={report_id:r};console.log("开始请求SSE数据，报告ID:",r);let H=!1;Vs(f,G=>{var Z,O,je,Ue,o,Te,le,ie,ke,fe,i,t;if(console.log("type",G.type),G.type=="message"){if(H||(H=!0),a=a+G.data,console.log("recievedata:",a),!a.includes("L.")&&!a.includes("M.")){const n=w.value.recommend_list[F];a.includes("A.")&&!a.includes("B.")?n.school_name=((Z=a.split("A.")[1])==null?void 0:Z.trim())||"":a.includes("B.")&&!a.includes("C.")?n.major_name=((O=a.split("B.")[1])==null?void 0:O.trim())||"":a.includes("C.")&&!a.includes("D.")?(n.basic_info||(n.basic_info={}),n.basic_info.score_formula=((je=a.split("C.")[1])==null?void 0:je.trim())||""):a.includes("D.")&&!a.includes("N.")?(n.basic_info||(n.basic_info={}),n.basic_info.study_years=((Ue=a.split("D.")[1])==null?void 0:Ue.trim())||""):a.includes("N.")&&!a.includes("E.")?(n.basic_info||(n.basic_info={}),n.basic_info.tuition_fee=((o=a.split("N.")[1])==null?void 0:o.trim())||""):a.includes("E.")&&!a.includes("F.")?(n.basic_info||(n.basic_info={}),n.basic_info.exam_range=((Te=a.split("E.")[1])==null?void 0:Te.trim())||""):a.includes("F.")&&!a.includes("G.")?(n.basic_info||(n.basic_info={}),n.basic_info.reference_books=((le=a.split("F.")[1])==null?void 0:le.trim())||""):a.includes("G.")&&!a.includes("H.")?(n.basic_info||(n.basic_info={}),n.basic_info.retest_score_requirement=((ie=a.split("G.")[1])==null?void 0:ie.trim())||""):a.includes("H.")&&!a.includes("J.")?(n.basic_info||(n.basic_info={}),n.basic_info.retest_content=((ke=a.split("H.")[1])==null?void 0:ke.trim())||""):a.includes("J.")&&!a.includes("K.")?n.difficulty_analysis=((fe=a.split("J.")[1])==null?void 0:fe.trim())||"":a.includes("K.")&&!a.includes("L.")&&(n.suggest=((i=a.split("K.")[1])==null?void 0:i.trim())||"")}else if(!a.includes("M.")&&a.includes("L.")){const n=a.split("L."),d=n[n.length-1];a=d;const p=w.value.recommend_list[F];p&&p.school_name&&C.value&&Xt({school_name:p.school_name,report_id:C.value}).then(x=>{if(x.code===0&&x.data){const{basic_info:U,school_name:q}=p;Object.assign(p,x.data.info),U&&(p.basic_info=U),q&&(p.school_name=q),x.data.admission_data&&(p.admission_data=x.data.admission_data),w.value.school_list.push(x.data.item),console.log("成功获取学校详细信息:",p.school_name,x.data)}else console.warn("获取学校详细信息失败:",p.school_name,x.msg)}).catch(x=>{console.error("调用学校详细信息API失败:",x)});const y=d.includes("M."),h=d.includes("A."),k=!y;if(console.log("L.标记处理 - afterLContent:",d),console.log("L.标记处理 - hasM:",y,"hasA:",h,"shouldCreateNextSchool:",k),console.log("L.标记处理 - 当前院校总数:",w.value.recommend_list.length),k){let x={school_id:"",school_name:"",major_name:"",difficulty_analysis:"",suggest:"",reason:"",admission_stats:{yearly_stats:[],has_data:!1},retest_stats:{},basic_info:{},school_info:{},current_year_retest_list:{list:[],year:null,count:0},current_year_admission_list:{list:[],year:null,count:0}};w.value.recommend_list.push(x)}F++}else if(a.includes("M.")){console.log("收到M.标记 - 处理前recommend_list数量:",w.value.recommend_list.length),console.log("收到M.标记 - 原始数据:",a);let n=((t=a.split("M.")[1])==null?void 0:t.trim())||"";w.value.high_recommend_list[0]||(w.value.high_recommend_list=[{reason:"",school_name:"",major_name:"",school_id:""}]),w.value.high_recommend_list[0].reason=n,at(()=>{console.log("M.标记处理 - nextTick后recommend_list数量:",w.value.recommend_list.length)})}}else if(G.type!=="chunk"){if(G.type==="end"){const n=w.value.recommend_list[F];n&&n.school_name&&C.value?Xt({school_name:n.school_name,report_id:C.value}).then(d=>{if(d.code===0&&d.data){const{basic_info:p,school_name:y}=n;Object.assign(n,d.data.info),p&&(n.basic_info=p),y&&(n.school_name=y),d.data.admission_data&&(n.admission_data=d.data.admission_data),w.value.school_list.push(d.data.item),console.log("成功获取学校详细信息:",n.school_name,d.data)}else console.warn("获取学校详细信息失败:",n.school_name,d.msg);bt(),ut(),W.setReportData(w.value)}).catch(d=>{console.error("调用学校详细信息API失败:",d),ut()}):(bt(),ut()),V.value=!0,ss({title:"通知",message:"推荐院校生成完毕",type:"success"}),console.log("end",w)}}}).catch(G=>{console.error("SSE流式接口错误:",G)})}catch(a){console.error("加载报告数据失败:",a)}},Ne=r=>{var a;D.value[r]=!0;const l=(a=w.value.recommend_list)==null?void 0:a.find(f=>f.school_id===r);l&&(z.value[r]=l.difficulty_analysis||"")},qe=r=>{var a;N.value[r]=!0;const l=(a=w.value.recommend_list)==null?void 0:a.find(f=>f.school_id===r);l&&(ne.value[r]=l.suggest||"")},Ie=async r=>{var l;try{const a=(l=w.value.recommend_list)==null?void 0:l.find(f=>f.school_id===r);a&&(a.difficulty_analysis=z.value[r]),await T(r,"competition_difficulty",z.value[r]),D.value[r]=!1,$.success("竞争难度分析保存成功")}catch(a){console.error("保存竞争难度分析失败:",a),$.error("保存失败，请重试")}},R=async r=>{var l;try{const a=(l=w.value.recommend_list)==null?void 0:l.find(f=>f.school_id===r);a&&(a.suggest=ne.value[r]),await T(r,"suggestions",ne.value[r]),N.value[r]=!1,$.success("备考建议保存成功")}catch(a){console.error("保存备考建议失败:",a),$.error("保存失败，请重试")}},Oe=r=>{D.value[r]=!1,delete z.value[r]},Be=r=>{N.value[r]=!1,delete ne.value[r]},He=r=>{var a,f;oe.value[r]=!0;const l=(a=w.value.recommend_list)==null?void 0:a.find(H=>H.school_id===r);l&&(be.value[r]=((f=l.basic_info)==null?void 0:f.score_formula)||"")},Ve=async r=>{var l;try{const a=(l=w.value.recommend_list)==null?void 0:l.find(f=>f.school_id===r);a&&(a.basic_info||(a.basic_info={}),a.basic_info.score_formula=be.value[r]),await T(r,"score_formula",be.value[r]),oe.value[r]=!1,$.success("总成绩计算公式保存成功")}catch(a){console.error("保存总成绩计算公式失败:",a),$.error("保存失败，请重试")}},We=r=>{oe.value[r]=!1,delete be.value[r]},Ye=r=>{var a,f;pe.value[r]=!0;const l=(a=w.value.recommend_list)==null?void 0:a.find(H=>H.school_id===r);l&&(s.value[r]=((f=l.basic_info)==null?void 0:f.study_years)||"")},Ge=async r=>{var l;try{const a=(l=w.value.recommend_list)==null?void 0:l.find(f=>f.school_id===r);a&&(a.basic_info||(a.basic_info={}),a.basic_info.study_years=s.value[r]),await T(r,"study_years",s.value[r]),pe.value[r]=!1,$.success("学制保存成功")}catch(a){console.error("保存学制失败:",a),$.error("保存失败，请重试")}},Je=r=>{pe.value[r]=!1,delete s.value[r]},Ke=r=>{var a,f;ge.value[r]=!0;const l=(a=w.value.recommend_list)==null?void 0:a.find(H=>H.school_id===r);l&&(X.value[r]=((f=l.basic_info)==null?void 0:f.tuition_fee)||"")},Xe=async r=>{var l;try{const a=(l=w.value.recommend_list)==null?void 0:l.find(f=>f.school_id===r);a&&(a.basic_info||(a.basic_info={}),a.basic_info.tuition_fee=X.value[r]),await T(r,"tuition_fee",X.value[r]),ge.value[r]=!1,$.success("学费保存成功")}catch(a){console.error("保存学费失败:",a),$.error("保存失败，请重试")}},I=r=>{ge.value[r]=!1,delete X.value[r]},et=r=>{var a,f;he.value[r]=!0;const l=(a=w.value.recommend_list)==null?void 0:a.find(H=>H.school_id===r);l&&(xe.value[r]=((f=l.basic_info)==null?void 0:f.exam_range)||"")},de=async r=>{var l;try{const a=(l=w.value.recommend_list)==null?void 0:l.find(f=>f.school_id===r);a&&(a.basic_info||(a.basic_info={}),a.basic_info.exam_range=xe.value[r]),await T(r,"exam_subjects",xe.value[r]),he.value[r]=!1,$.success("初试考试科目保存成功")}catch(a){console.error("保存初试考试科目失败:",a),$.error("保存失败，请重试")}},Ct=r=>{he.value[r]=!1,delete xe.value[r]},Mt=r=>{var a,f;re.value[r]=!0;const l=(a=w.value.recommend_list)==null?void 0:a.find(H=>H.school_id===r);l&&(Ee.value[r]=((f=l.basic_info)==null?void 0:f.reference_books)||"")},ft=async r=>{var l;try{const a=(l=w.value.recommend_list)==null?void 0:l.find(f=>f.school_id===r);a&&(a.basic_info||(a.basic_info={}),a.basic_info.reference_books=Ee.value[r]),await T(r,"reference_books",Ee.value[r]),re.value[r]=!1,$.success("考试专业课参考书保存成功")}catch(a){console.error("保存考试专业课参考书失败:",a),$.error("保存失败，请重试")}},st=r=>{re.value[r]=!1,delete Ee.value[r]},xt=r=>{var a,f;Le.value[r]=!0;const l=(a=w.value.recommend_list)==null?void 0:a.find(H=>H.school_id===r);l&&(me.value[r]=((f=l.basic_info)==null?void 0:f.retest_content)||"")},_t=async r=>{var l;try{const a=(l=w.value.recommend_list)==null?void 0:l.find(f=>f.school_id===r);a&&(a.basic_info||(a.basic_info={}),a.basic_info.retest_content=me.value[r]),await T(r,"retest_content",me.value[r]),Le.value[r]=!1,$.success("复试考试内容保存成功")}catch(a){console.error("保存复试考试内容失败:",a),$.error("保存失败，请重试")}},Vt=r=>{Le.value[r]=!1,delete me.value[r]},jt=r=>{var a,f;Me.value[r]=!0;const l=(a=w.value.recommend_list)==null?void 0:a.find(H=>H.school_id===r);l&&(ye.value[r]=((f=l.basic_info)==null?void 0:f.retest_score_requirement)||"")},ht=async r=>{var l;try{const a=(l=w.value.recommend_list)==null?void 0:l.find(f=>f.school_id===r);a&&(a.basic_info||(a.basic_info={}),a.basic_info.retest_score_requirement=ye.value[r]),await T(r,"retest_score_requirement",ye.value[r]),Me.value[r]=!1,$.success("复试考核内容保存成功")}catch(a){console.error("保存复试考核内容失败:",a),$.error("保存失败，请重试")}},Tt=r=>{Me.value[r]=!1,delete ye.value[r]},ct=r=>{var l,a;Fe.value[r]=!0,(a=(l=w.value.high_recommend_list)==null?void 0:l[0])!=null&&a.reason&&(we.value[r]=w.value.high_recommend_list[0].reason)},Pt=async r=>{var l,a;try{(l=w.value.high_recommend_list)!=null&&l[0]&&(w.value.high_recommend_list[0].reason=we.value[r]);const f=(a=w.value.high_recommend_list)==null?void 0:a[0];f&&f.school_id?await T(f.school_id,"high_recommend_reason",we.value[r]):await Qt({report_id:C.value,school_id:0,field_name:"high_recommend_reason",field_value:we.value[r],is_high_recommend:1}),Fe.value[r]=!1,$.success("高性价比院校推荐原因保存成功")}catch(f){console.error("保存高性价比院校推荐原因失败:",f),$.error("保存失败，请重试")}},At=r=>{Fe.value[r]=!1,delete we.value[r]},bt=()=>{console.log("=== 开始清理空院校数据 ==="),console.log("清理前recommend_list详细信息:"),w.value.recommend_list.forEach((f,H)=>{console.log(`  院校${H+1}: "${f.school_name}" - 有内容: ${!!f.school_name}`)});const r=w.value.recommend_list.length,l=[...w.value.recommend_list];w.value.recommend_list=w.value.recommend_list.filter((f,H)=>{const G=f.school_name&&f.school_name.trim()!=="",Z=f.difficulty_analysis&&f.difficulty_analysis.trim()!=="",O=f.suggest&&f.suggest.trim()!=="",je=G||Z||O;return console.log(`院校${H+1}: "${f.school_name||"(空)"}" - 学校名: ${G}, 难度: ${Z}, 建议: ${O} -> ${je?"保留":"移除"}`),je});const a=w.value.recommend_list.length;console.log(`清理结果: ${r} -> ${a} (移除了${r-a}个空院校)`),a===0&&r>0&&(console.warn("⚠️ 警告：清理后所有院校都被移除了，这可能不正常！"),console.log("原始院校列表:",l),w.value.recommend_list=l,console.log("已恢复原始院校数据")),console.log("=== 清理完成 ===")},ut=async()=>{var r,l,a,f;try{if(!C.value){console.error("报告ID不存在");return}const H=w.value.recommend_list.map((O,je)=>{var o,Te,le,ie,ke,fe,i,t,n,d,p;const Ue=(o=w.value.high_recommend_list)!=null&&o.some(y=>y.school_name===O.school_name)?1:0;return{school_id:Number(O.school_id)||0,school_name:O.school_name||"",is_high_recommend:Ue,difficulty_analysis:O.difficulty_analysis||"",suggest:O.suggest||"",reason:O.reason||"",score_formula:((Te=O.basic_info)==null?void 0:Te.score_formula)||"",study_years:((le=O.basic_info)==null?void 0:le.study_years)||"",tuition_fee:((ie=O.basic_info)==null?void 0:ie.tuition_fee)||"",exam_subjects:((ke=O.basic_info)==null?void 0:ke.exam_range)||"",reference_books:((fe=O.basic_info)==null?void 0:fe.reference_books)||"",retest_content:((i=O.basic_info)==null?void 0:i.retest_content)||"",retest_score_requirement:((t=O.basic_info)==null?void 0:t.retest_score_requirement)||"",high_recommend_reason:Ue&&((d=(n=w.value.high_recommend_list)==null?void 0:n[0])==null?void 0:d.reason)||"",admission_requirements:((p=O.basic_info)==null?void 0:p.admission_requirements)||""}}).filter(O=>O.school_name);H.push({school_id:0,school_name:((l=(r=w.value.high_recommend_list[0].school_name)==null?void 0:r.split(",")[0])==null?void 0:l.split(":")[1])||"",is_high_recommend:1,difficulty_analysis:"",suggest:"",reason:w.value.high_recommend_list[0].reason||"",score_formula:"",study_years:"",tuition_fee:"",exam_subjects:"",reference_books:"",retest_content:"",retest_score_requirement:"",high_recommend_reason:w.value.high_recommend_list[0].reason||"",admission_requirements:""});const G={report_id:C.value,school_list:H};console.log("准备保存的报告数据:",G);const Z=await js(G);console.log("saveReport:",Z),ze.getUserInfo(),((a=Z.data)==null?void 0:a.code)===0?console.log("报告数据保存成功"):console.error("报告数据保存失败:",(f=Z.data)==null?void 0:f.msg)}catch(H){console.error("提取并保存报告数据失败:",H)}};return it({show:Q,hide:_e,toggle:Re,isVisible:se,updateNationalLineData:Ce,setReportId:b,resetData:()=>{console.log("重置 content 组件数据"),w.value={recommend_list:[],high_recommend_list:[],school_list:[]},D.value={},N.value={},z.value={},ne.value={},C.value="",$e.value={subject_code:"",subject_name:"",years:[],a_total:[],a_single_100:[],a_single_over100:[],b_total:[],b_single_100:[],b_single_over100:[]},console.log("content 组件数据重置完成")},pdfUrl:v,currentReportId:C}),ts(()=>{W.hasReportData&&(w.value=W.reportData)}),(r,l)=>{var H,G,Z,O,je,Ue;const a=Nt,f=qt;return Ae((c(),g("div",Rs,[Ae(e("div",Fs,[l[6]||(l[6]=e("div",{class:"step-header"},"第三部分：院校总览",-1)),e("div",Ns,[e("div",qs,[l[5]||(l[5]=Ft('<div class="school-table-header" data-v-786b015a><div class="header-cell" data-v-786b015a>序号</div><div class="header-cell" data-v-786b015a>院校名称</div><div class="header-cell" data-v-786b015a>所在地区</div><div class="header-cell" data-v-786b015a>所在省市</div><div class="header-cell" data-v-786b015a>学院</div><div class="header-cell" data-v-786b015a>专业</div><div class="header-cell" data-v-786b015a>专业代码</div><div class="header-cell" data-v-786b015a>最低分</div><div class="header-cell" data-v-786b015a>最低分分差</div></div>',1)),e("div",Os,[(c(!0),g(te,null,ve(((H=Se(w))==null?void 0:H.school_list)||[],(o,Te)=>(c(),g("div",{class:"table-row",key:o.id},[e("div",Bs,j(Te+1),1),e("div",Hs,[e("div",null,j(o.school_name),1),e("div",Ws,[o.tag_985?(c(),g("span",Ys,"985")):A("",!0),o.tag_211?(c(),g("span",Gs,"211")):A("",!0),o.tag_double?(c(),g("span",Js,"双一流")):A("",!0)])]),e("div",Ks,j(o.region),1),e("div",Xs,j(o.city),1),e("div",Qs,j(o.college),1),e("div",Zs,j(o.major_name),1),e("div",Is,j(o.major_code),1),e("div",el,j(o.min_score),1),e("div",tl,j(o.score_diff),1)]))),128))])])])],512),[[yt,((G=Se(w))==null?void 0:G.school_list.length)>0]]),e("div",null,[e("div",sl,[l[62]||(l[62]=e("div",{class:"step-header"},"第四部分：院校分析",-1)),(c(!0),g(te,null,ve(((Z=Se(w))==null?void 0:Z.recommend_list)||[],(o,Te)=>{var le,ie,ke,fe,i,t,n,d,p,y,h,k,x,U,q,B,ue,u,J,K,vt,mt,lt,Qe;return c(),g("div",{class:"step-content",key:o.school_id},[e("div",ll,[e("span",null,j(Te+1),1),l[7]||(l[7]=e("div",{class:"tag-text"},"院校推荐",-1))]),e("div",ol,[e("div",al,[Ae(e("div",il,[e("img",{src:"https://"+((le=o==null?void 0:o.school_info)==null?void 0:le.logo)||"@/assets/images/school.png",alt:"学校logo"},null,8,nl)],512),[[yt,(ie=o==null?void 0:o.school_info)==null?void 0:ie.logo]]),e("div",rl,[e("div",dl,[e("h2",null,j((o==null?void 0:o.school_name)||""),1),e("span",cl,j(((ke=o==null?void 0:o.school_info)==null?void 0:ke.address)||""),1)]),e("div",ul,[(fe=o==null?void 0:o.school_info)!=null&&fe.is_dual_class?(c(),g("span",vl,"双一流")):A("",!0),(i=o==null?void 0:o.school_info)!=null&&i.tag_985?(c(),g("span",ml,"985")):A("",!0),(t=o==null?void 0:o.school_info)!=null&&t.tag_211?(c(),g("span",gl,"211")):A("",!0)])])]),Object.keys(o==null?void 0:o.basic_info).length!=0?(c(),g(te,{key:0},[l[38]||(l[38]=e("h3",{class:"section-title"},"院校情况",-1)),e("div",pl,[(n=o==null?void 0:o.basic_info)!=null&&n.score_formula?(c(),g("div",yl,[l[12]||(l[12]=e("div",{class:"item-icon"},[e("img",{src:"https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png",alt:"标签图标"})],-1)),e("div",fl,[e("div",_l,[l[9]||(l[9]=e("h4",null,"总成绩计算公式",-1)),!oe.value[o.school_id]&&V.value?(c(),g("div",hl,[m(a,{type:"text",size:"small",onClick:S=>He(o.school_id),class:"edit-btn"},{default:M(()=>l[8]||(l[8]=[L(" 编辑 ")])),_:2},1032,["onClick"])])):A("",!0)]),oe.value[o.school_id]?(c(),g("div",wl,[m(f,{modelValue:be.value[o.school_id],"onUpdate:modelValue":S=>be.value[o.school_id]=S,type:"textarea",rows:3,placeholder:"请输入总成绩计算公式",class:"edit-textarea"},null,8,["modelValue","onUpdate:modelValue"]),e("div",kl,[m(a,{size:"small",onClick:S=>Ve(o.school_id),class:"save-btn"},{default:M(()=>l[10]||(l[10]=[L("保存")])),_:2},1032,["onClick"]),m(a,{size:"small",onClick:S=>We(o.school_id)},{default:M(()=>l[11]||(l[11]=[L("取消")])),_:2},1032,["onClick"])])])):(c(),g("div",bl,[e("p",null,j((d=o==null?void 0:o.basic_info)==null?void 0:d.score_formula),1)]))])])):A("",!0),(p=o==null?void 0:o.basic_info)!=null&&p.study_years?(c(),g("div",Sl,[l[17]||(l[17]=e("div",{class:"item-icon"},[e("img",{src:"https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png",alt:"标签图标"})],-1)),e("div",$l,[e("div",Cl,[l[14]||(l[14]=e("h4",null,"学制",-1)),!pe.value[o.school_id]&&V.value?(c(),g("div",Ml,[m(a,{type:"text",size:"small",onClick:S=>Ye(o.school_id),class:"edit-btn"},{default:M(()=>l[13]||(l[13]=[L(" 编辑 ")])),_:2},1032,["onClick"])])):A("",!0)]),pe.value[o.school_id]?(c(),g("div",Vl,[m(f,{modelValue:s.value[o.school_id],"onUpdate:modelValue":S=>s.value[o.school_id]=S,type:"textarea",rows:2,placeholder:"请输入学制",class:"edit-textarea"},null,8,["modelValue","onUpdate:modelValue"]),e("div",jl,[m(a,{size:"small",onClick:S=>Ge(o.school_id),class:"save-btn"},{default:M(()=>l[15]||(l[15]=[L("保存")])),_:2},1032,["onClick"]),m(a,{size:"small",onClick:S=>Je(o.school_id)},{default:M(()=>l[16]||(l[16]=[L("取消")])),_:2},1032,["onClick"])])])):(c(),g("div",xl,[e("p",null,j((y=o==null?void 0:o.basic_info)==null?void 0:y.study_years),1)]))])])):A("",!0),(h=o==null?void 0:o.basic_info)!=null&&h.tuition_fee?(c(),g("div",Tl,[l[22]||(l[22]=e("div",{class:"item-icon"},[e("img",{src:"https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png",alt:"标签图标"})],-1)),e("div",Pl,[e("div",Al,[l[19]||(l[19]=e("h4",null,"学费",-1)),!ge.value[o.school_id]&&V.value?(c(),g("div",Dl,[m(a,{type:"text",size:"small",onClick:S=>Ke(o.school_id),class:"edit-btn"},{default:M(()=>l[18]||(l[18]=[L(" 编辑 ")])),_:2},1032,["onClick"])])):A("",!0)]),ge.value[o.school_id]?(c(),g("div",Ll,[m(f,{modelValue:X.value[o.school_id],"onUpdate:modelValue":S=>X.value[o.school_id]=S,type:"textarea",rows:2,placeholder:"请输入学费",class:"edit-textarea"},null,8,["modelValue","onUpdate:modelValue"]),e("div",El,[m(a,{size:"small",onClick:S=>Xe(o.school_id),class:"save-btn"},{default:M(()=>l[20]||(l[20]=[L("保存")])),_:2},1032,["onClick"]),m(a,{size:"small",onClick:S=>I(o.school_id)},{default:M(()=>l[21]||(l[21]=[L("取消")])),_:2},1032,["onClick"])])])):(c(),g("div",zl,[e("p",null,j((k=o==null?void 0:o.basic_info)==null?void 0:k.tuition_fee),1)]))])])):A("",!0)]),l[39]||(l[39]=e("h3",{class:"section-title"},"初试模块",-1)),e("div",Ul,[(x=o==null?void 0:o.basic_info)!=null&&x.exam_range?(c(),g("div",Rl,[l[27]||(l[27]=e("div",{class:"item-icon"},[e("img",{src:"https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png",alt:"标签图标"})],-1)),e("div",Fl,[e("div",Nl,[l[24]||(l[24]=e("h4",null,"初试考试科目",-1)),!he.value[o.school_id]&&V.value?(c(),g("div",ql,[m(a,{type:"text",size:"small",onClick:S=>et(o.school_id),class:"edit-btn"},{default:M(()=>l[23]||(l[23]=[L(" 编辑 ")])),_:2},1032,["onClick"])])):A("",!0)]),he.value[o.school_id]?(c(),g("div",Bl,[m(f,{modelValue:xe.value[o.school_id],"onUpdate:modelValue":S=>xe.value[o.school_id]=S,type:"textarea",rows:3,placeholder:"请输入初试考试科目",class:"edit-textarea"},null,8,["modelValue","onUpdate:modelValue"]),e("div",Hl,[m(a,{size:"small",onClick:S=>de(o.school_id),class:"save-btn"},{default:M(()=>l[25]||(l[25]=[L("保存")])),_:2},1032,["onClick"]),m(a,{size:"small",onClick:S=>Ct(o.school_id)},{default:M(()=>l[26]||(l[26]=[L("取消")])),_:2},1032,["onClick"])])])):(c(),g("div",Ol,[e("p",null,j((U=o==null?void 0:o.basic_info)==null?void 0:U.exam_range),1)]))])])):A("",!0),(q=o==null?void 0:o.basic_info)!=null&&q.reference_books?(c(),g("div",Wl,[l[32]||(l[32]=e("div",{class:"item-icon"},[e("img",{src:"https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png",alt:"标签图标"})],-1)),e("div",Yl,[e("div",Gl,[l[29]||(l[29]=e("h4",null,"考试专业课参考书",-1)),!re.value[o.school_id]&&V.value?(c(),g("div",Jl,[m(a,{type:"text",size:"small",onClick:S=>Mt(o.school_id),class:"edit-btn"},{default:M(()=>l[28]||(l[28]=[L(" 编辑 ")])),_:2},1032,["onClick"])])):A("",!0)]),re.value[o.school_id]?(c(),g("div",Xl,[m(f,{modelValue:Ee.value[o.school_id],"onUpdate:modelValue":S=>Ee.value[o.school_id]=S,type:"textarea",rows:4,placeholder:"请输入考试专业课参考书",class:"edit-textarea"},null,8,["modelValue","onUpdate:modelValue"]),e("div",Ql,[m(a,{size:"small",onClick:S=>ft(o.school_id),class:"save-btn"},{default:M(()=>l[30]||(l[30]=[L("保存")])),_:2},1032,["onClick"]),m(a,{size:"small",onClick:S=>st(o.school_id)},{default:M(()=>l[31]||(l[31]=[L("取消")])),_:2},1032,["onClick"])])])):(c(),g("div",Kl,[e("p",null,j((B=o==null?void 0:o.basic_info)==null?void 0:B.reference_books),1)]))])])):A("",!0),(ue=o==null?void 0:o.basic_info)!=null&&ue.retest_score_requirement?(c(),g("div",Zl,[l[37]||(l[37]=e("div",{class:"item-icon"},[e("img",{src:"https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png",alt:"标签图标"})],-1)),e("div",Il,[e("div",eo,[l[34]||(l[34]=e("h4",null,"复试考核内容",-1)),!Me.value[o.school_id]&&V.value?(c(),g("div",to,[m(a,{type:"text",size:"small",onClick:S=>jt(o.school_id),class:"edit-btn"},{default:M(()=>l[33]||(l[33]=[L(" 编辑 ")])),_:2},1032,["onClick"])])):A("",!0)]),Me.value[o.school_id]?(c(),g("div",lo,[m(f,{modelValue:ye.value[o.school_id],"onUpdate:modelValue":S=>ye.value[o.school_id]=S,type:"textarea",rows:3,placeholder:"请输入复试考核内容",class:"edit-textarea"},null,8,["modelValue","onUpdate:modelValue"]),e("div",oo,[m(a,{size:"small",onClick:S=>ht(o.school_id),class:"save-btn"},{default:M(()=>l[35]||(l[35]=[L("保存")])),_:2},1032,["onClick"]),m(a,{size:"small",onClick:S=>Tt(o.school_id)},{default:M(()=>l[36]||(l[36]=[L("取消")])),_:2},1032,["onClick"])])])):(c(),g("div",so,[e("p",null,j((u=o==null?void 0:o.basic_info)==null?void 0:u.retest_score_requirement),1)]))])])):A("",!0)])],64)):A("",!0),e("div",ao,[o!=null&&o.admission_data&&o.admission_data.length>0?(c(),g("div",io," 招生情况（"+j((J=o.admission_data[0])==null?void 0:J.year)+"年） ",1)):A("",!0),o!=null&&o.admission_data&&o.admission_data.length>0?(c(),g("table",no,[l[40]||(l[40]=e("thead",null,[e("tr",null,[e("th",null,"招生计划"),e("th",null,"一志愿人数"),e("th",null,"总录取数"),e("th",null,"一志愿录取比"),e("th",null,"调剂人数"),e("th",null,"最高分"),e("th",null,"最低分"),e("th",null,"平均分")])],-1)),e("tbody",null,[(c(!0),g(te,null,ve(o.admission_data,(S,tt)=>(c(),g("tr",{key:tt},[e("td",null,j(S.planCount),1),e("td",null,j(S.examCount),1),e("td",null,j(S.admitCount),1),e("td",null,j(S.ratio),1),e("td",null,j(S.studentCount),1),e("td",null,j(S.highestScore),1),e("td",null,j(S.lowestScore),1),e("td",null,j(S.averageScore),1)]))),128))])])):A("",!0),(o==null?void 0:o.current_year_retest_list.list.length)>0?(c(),g(te,{key:2},[l[42]||(l[42]=e("div",{class:"admission-title"},"一志愿考试名单",-1)),e("table",ro,[l[41]||(l[41]=e("thead",null,[e("tr",null,[e("th",null,"编号"),e("th",null,"学生姓名"),e("th",null,"政治"),e("th",null,"英语"),e("th",null,"专业课一"),e("th",null,"专业课二"),e("th",null,"初试成绩"),e("th",null,"是否一志愿")])],-1)),e("tbody",null,[(c(!0),g(te,null,ve(((K=o==null?void 0:o.current_year_retest_list)==null?void 0:K.list)||[],(S,tt)=>(c(),g("tr",{key:tt},[e("td",null,j(tt+1),1),e("td",null,j(S.name+"**"),1),e("td",null,j(S.politics_score),1),e("td",null,j(S.english_score),1),e("td",null,j(S.major1_score),1),e("td",null,j(S.major2_score),1),e("td",null,j(S.initial_score),1),e("td",null,j(S.admission_status),1)]))),128))])])],64)):A("",!0),(vt=o==null?void 0:o.basic_info)!=null&&vt.retest_content?(c(),g(te,{key:3},[l[48]||(l[48]=e("div",{class:"admission-title"},"复试模块",-1)),e("div",co,[e("div",uo,[e("div",vo,[l[44]||(l[44]=e("img",{src:"https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png",alt:"标签图标"},null,-1)),l[45]||(l[45]=e("div",{class:"reexam-title"},"复试考试内容",-1)),!Le.value[o.school_id]&&V.value?(c(),g("div",mo,[m(a,{type:"text",size:"small",onClick:S=>xt(o.school_id),class:"edit-btn"},{default:M(()=>l[43]||(l[43]=[L(" 编辑 ")])),_:2},1032,["onClick"])])):A("",!0)]),e("div",go,[Le.value[o.school_id]?(c(),g("div",yo,[m(f,{modelValue:me.value[o.school_id],"onUpdate:modelValue":S=>me.value[o.school_id]=S,type:"textarea",rows:4,placeholder:"请输入复试考试内容",class:"edit-textarea"},null,8,["modelValue","onUpdate:modelValue"]),e("div",fo,[m(a,{size:"small",onClick:S=>_t(o.school_id),class:"save-btn"},{default:M(()=>l[46]||(l[46]=[L("保存")])),_:2},1032,["onClick"]),m(a,{size:"small",onClick:S=>Vt(o.school_id)},{default:M(()=>l[47]||(l[47]=[L("取消")])),_:2},1032,["onClick"])])])):(c(),g("div",po,j((mt=o==null?void 0:o.basic_info)==null?void 0:mt.retest_content),1))])])])],64)):A("",!0),((lt=o==null?void 0:o.current_year_admission_list)==null?void 0:lt.list.length)>0?(c(),g(te,{key:4},[l[50]||(l[50]=e("div",{class:"admission-title"},"拟录取名单",-1)),e("table",_o,[l[49]||(l[49]=e("thead",null,[e("tr",null,[e("th",null,"编号"),e("th",null,"学生姓名"),e("th",null,"初试成绩"),e("th",null,"复试成绩"),e("th",null,"两项总成绩"),e("th",null,"一志愿学校")])],-1)),e("tbody",null,[(c(!0),g(te,null,ve(((Qe=o==null?void 0:o.current_year_admission_list)==null?void 0:Qe.list)||[],(S,tt)=>(c(),g("tr",{key:tt},[e("td",null,j(tt+1),1),e("td",null,j(S.name+"**"),1),e("td",null,j(S.initial_score),1),e("td",null,j(S.retest_score),1),e("td",null,j(S.total_score),1),e("td",null,j(S.first_choice_school),1)]))),128))])])],64)):A("",!0),l[61]||(l[61]=e("div",{class:"admission-title"},"综合建议",-1)),e("div",ho,[e("div",bo,[e("div",wo,[l[52]||(l[52]=e("img",{src:"https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png",alt:"标签图标"},null,-1)),l[53]||(l[53]=e("div",{class:"reexam-title"},"竞争难度分析",-1)),!D.value[o.school_id]&&V.value?(c(),g("div",ko,[m(a,{type:"text",size:"small",onClick:S=>Ne(o.school_id),class:"edit-btn"},{default:M(()=>l[51]||(l[51]=[L(" 编辑 ")])),_:2},1032,["onClick"]),m(a,{type:"text",size:"small",onClick:S=>Y(o.school_id,"competition_difficulty"),class:"regenerate-btn",loading:ce.value[`${o.school_id}_competition_difficulty`],disabled:ce.value[`${o.school_id}_competition_difficulty`]},{default:M(()=>[L(j(ce.value[`${o.school_id}_competition_difficulty`]?"生成中...":"重新生成"),1)]),_:2},1032,["onClick","loading","disabled"])])):A("",!0)]),e("div",So,[D.value[o.school_id]?(c(),g("div",Co,[m(f,{modelValue:z.value[o.school_id],"onUpdate:modelValue":S=>z.value[o.school_id]=S,type:"textarea",rows:4,placeholder:"请输入竞争难度分析",class:"edit-textarea"},null,8,["modelValue","onUpdate:modelValue"]),e("div",Mo,[m(a,{size:"small",onClick:S=>Ie(o.school_id)},{default:M(()=>l[54]||(l[54]=[L("保存")])),_:2},1032,["onClick"]),m(a,{size:"small",onClick:S=>Oe(o.school_id)},{default:M(()=>l[55]||(l[55]=[L("取消")])),_:2},1032,["onClick"])])])):(c(),g("div",$o,j(o.difficulty_analysis),1))])]),e("div",xo,[e("div",Vo,[l[57]||(l[57]=e("img",{src:"https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png",alt:"标签图标"},null,-1)),l[58]||(l[58]=e("div",{class:"reexam-title"},"备考目标建议",-1)),!N.value[o.school_id]&&V.value?(c(),g("div",jo,[m(a,{type:"text",size:"small",onClick:S=>qe(o.school_id),class:"edit-btn"},{default:M(()=>l[56]||(l[56]=[L(" 编辑 ")])),_:2},1032,["onClick"]),m(a,{type:"text",size:"small",onClick:S=>Y(o.school_id,"suggestions"),class:"regenerate-btn",loading:ce.value[`${o.school_id}_suggestions`],disabled:ce.value[`${o.school_id}_suggestions`]},{default:M(()=>[L(j(ce.value[`${o.school_id}_suggestions`]?"生成中...":"重新生成"),1)]),_:2},1032,["onClick","loading","disabled"])])):A("",!0)]),e("div",To,[N.value[o.school_id]?(c(),g("div",Ao,[m(f,{modelValue:ne.value[o.school_id],"onUpdate:modelValue":S=>ne.value[o.school_id]=S,type:"textarea",rows:4,placeholder:"请输入备考目标建议",class:"edit-textarea"},null,8,["modelValue","onUpdate:modelValue"]),e("div",Do,[m(a,{size:"small",onClick:S=>R(o.school_id)},{default:M(()=>l[59]||(l[59]=[L("保存")])),_:2},1032,["onClick"]),m(a,{size:"small",onClick:S=>Be(o.school_id)},{default:M(()=>l[60]||(l[60]=[L("取消")])),_:2},1032,["onClick"])])])):(c(),g("div",Po,j(o.suggest),1))])])])])])])}),128))])]),e("div",zo,[l[68]||(l[68]=e("div",{class:"step-num-tag"},[e("span",null,"11"),e("div",{class:"tag-text"},"推荐综合性价比高的院校")],-1)),e("div",Lo,[e("div",Eo,[e("div",Uo,[l[64]||(l[64]=e("div",{class:"recommend-icon"},[e("img",{src:"https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png",alt:"标签图标"})],-1)),l[65]||(l[65]=e("div",{class:"recommend-title"},"推荐原因",-1)),!Fe.value.high_recommend&&V.value?(c(),g("div",Ro,[m(a,{type:"text",size:"small",onClick:l[0]||(l[0]=o=>ct("high_recommend")),class:"edit-btn"},{default:M(()=>l[63]||(l[63]=[L(" 编辑 ")])),_:1}),m(a,{type:"text",size:"small",class:"regenerate-btn",onClick:l[1]||(l[1]=o=>Y("-1","highrecommendreason")),loading:ce.value["-1_highrecommendreason"],disabled:ce.value["-1_highrecommendreason"]},{default:M(()=>[L(j(ce.value["-1_highrecommendreason"]?"生成中...":"重新生成"),1)]),_:1},8,["loading","disabled"])])):A("",!0)]),e("div",Fo,[Fe.value.high_recommend?(c(),g("div",qo,[m(f,{modelValue:we.value.high_recommend,"onUpdate:modelValue":l[2]||(l[2]=o=>we.value.high_recommend=o),type:"textarea",rows:4,placeholder:"请输入高性价比院校推荐原因",class:"edit-textarea"},null,8,["modelValue"]),e("div",Oo,[m(a,{size:"small",onClick:l[3]||(l[3]=o=>Pt("high_recommend")),class:"save-btn"},{default:M(()=>l[66]||(l[66]=[L("保存")])),_:1}),m(a,{size:"small",onClick:l[4]||(l[4]=o=>At("high_recommend"))},{default:M(()=>l[67]||(l[67]=[L("取消")])),_:1})])])):(c(),g("div",No,j(((Ue=(je=(O=Se(w))==null?void 0:O.high_recommend_list)==null?void 0:je[0])==null?void 0:Ue.reason)||"暂无推荐信息"),1))])])])])],512)),[[yt,se.value]])}}}),Ho=Ot(Bo,[["__scopeId","data-v-786b015a"]]),Wo={class:"weak-module-analysis","element-loading-text":"正在加载学习计划数据..."},Yo={key:0,class:"weak-module-container"},Go={class:"detail-section"},Jo={class:"subtitle-container"},Ko={key:1,class:"detail-text"},Xo=["textContent"],Qo={key:0,class:"typing-cursor"},Zo={class:"edit-button-container"},Io={key:1,class:"edit-actions"},ea={key:0,class:"analysis-section"},ta={key:1,class:"analysis-text"},sa=["textContent"],la={key:0,class:"typing-cursor"},oa={key:1,class:"solutions-section"},aa={class:"solutions-content"},ia={key:1,class:"solution-text"},na=["textContent"],ra={key:0,class:"typing-cursor"},da={class:"target-score-section"},ca={class:"target-score-header"},ua={class:"target-score-actions"},va={class:"target-score-table"},ma={class:"score-row data-row"},ga={class:"score-cell bottom-cell"},pa={class:"score-cell bottom-cell"},ya={class:"score-cell bottom-cell"},fa={class:"score-cell bottom-cell"},_a={class:"score-cell bottom-cell"},ha={key:1,class:"study-planning-section"},ba={class:"step-num-tag"},wa={class:"tag-text"},ka={key:0,class:"stage-title"},Sa=["textContent"],$a={key:0,class:"typing-cursor"},Ca={key:1,class:"stage-content"},Ma={key:0,class:"module-title"},xa={key:1},Va=["textContent"],ja={key:0,class:"typing-cursor"},Ta={class:"edit-button-container"},Pa={key:1,class:"edit-actions"},Aa={key:1,class:"study-item"},Da={key:1,class:"study-item-content"},za=["textContent"],La={key:0,class:"typing-cursor"},Ea={key:2,class:"study-item"},Ua={key:1,class:"study-item-content"},Ra=["textContent"],Fa={key:0,class:"typing-cursor"},Na={key:3,class:"study-item"},qa={key:1,class:"study-item-content"},Oa=["textContent"],Ba={key:0,class:"typing-cursor"},Ha={key:4,class:"study-item"},Wa={key:1,class:"study-item-content"},Ya=["textContent"],Ga={key:0,class:"typing-cursor"},Ja={key:2,class:"comprehensive-advice-section"},Ka={class:"advice-content"},Xa={class:"advice-content-container"},Qa={key:1,class:"advice-text"},Za=["textContent"],Ia={key:0,class:"typing-cursor"},ei={class:"edit-button-container"},ti={key:1,class:"edit-actions"},si=Zt({__name:"WeakModuleAnalysis",setup(De,{expose:it}){let ze=_(!1);const W=_({english:0,politics:0,business1:0,business2:0}),$e=ls(()=>W.value.politics+W.value.english+W.value.business1+W.value.business2),Ce=_({weakModuleAnalysis:[],studyPlanning:{title:"初试各科学习规划",stages:[]},comprehensiveAdvice:""}),se=_([]),Q=_(!1),_e=_(!1),Re=_(!1),w=_(""),D=_({weakModules:[],studyModules:{},comprehensiveAdvice:!1}),N=_({weakModules:[],studyModules:{},comprehensiveAdvice:""}),z=_({weakModules:[],studyPlanning:{stages:[]},comprehensiveAdvice:{text:"",isTyping:!1}}),ne=_({title:"初试各科学习规划",stages:[]}),oe=(C,v,V=80)=>new Promise(T=>{v.isTyping=!0,v.text="";let Y=0;const F=setInterval(()=>{Y<C.length?(v.text+=C[Y],Y++):(clearInterval(F),v.isTyping=!1,T())},V)}),pe=async C=>{se.value=C,z.value.weakModules=C.map(()=>({subject:{text:"",isTyping:!1},problemAnalysis:{text:"",isTyping:!1},solutions:{text:"",isTyping:!1}}));for(let v=0;v<C.length;v++){const V=C[v],T=z.value.weakModules[v];console.log(`渲染薄弱模块 ${v+1}: ${V.subject}`),await oe(V.subject,T.subject,80),await oe(V.problemAnalysis,T.problemAnalysis,40),await oe(V.solutions,T.solutions,40)}},ge=async C=>{console.log("renderStudyPlanning 开始，数据:",C),ne.value=C,z.value.studyPlanning.stages=C.stages.map(v=>({title:{text:"",isTyping:!1},modules:v.modules.map(()=>({name:{text:"",isTyping:!1},studyContent:{text:"",isTyping:!1},studyMethod:{text:"",isTyping:!1},studyMaterials:{text:"",isTyping:!1},studyReminder:{text:"",isTyping:!1}}))})),console.log("打字机状态初始化完成，阶段数量:",z.value.studyPlanning.stages.length);for(let v=0;v<C.stages.length;v++){const V=C.stages[v],T=z.value.studyPlanning.stages[v];console.log(`开始渲染阶段 ${v+1}: ${V.title}`),await oe(V.title,T.title,80);for(let Y=0;Y<V.modules.length;Y++){const F=V.modules[Y],b=T.modules[Y];console.log(`  渲染模块 ${Y+1}: ${F.name}`),await oe(F.name,b.name,80),await oe(F.studyContent,b.studyContent,25),await oe(F.studyMethod,b.studyMethod,25),await oe(F.studyMaterials,b.studyMaterials,25),await oe(F.studyReminder,b.studyReminder,25)}}console.log("renderStudyPlanning 完成")},he=async C=>{await oe(C,z.value.comprehensiveAdvice,20),ss({title:"通知",message:"学习计划生成完毕",type:"success"})},re=async C=>{try{const v=await Ts({report_id:C});if(v.code===0&&v.data){const V=v.data;Ce.value=V,console.log("开始渲染学习计划数据:",V),_e.value=!1,V.weakModuleAnalysis&&V.weakModuleAnalysis.length>0&&(console.log("渲染薄弱模块分析，模块数量:",V.weakModuleAnalysis.length),await pe(V.weakModuleAnalysis)),console.log("渲染学习规划，阶段数量:",V.studyPlanning.stages.length),await ge(V.studyPlanning),console.log("渲染综合建议"),await he(V.comprehensiveAdvice)}else console.error("获取学习计划失败:",v.msg),$.error(v.msg||"获取学习计划失败"),_e.value=!1}catch(v){console.error("获取学习计划异常:",v),$.error("获取学习计划失败"),_e.value=!1}},Le=()=>{se.value=[],ne.value={title:"初试各科学习规划",stages:[]},z.value={weakModules:[],studyPlanning:{stages:[]},comprehensiveAdvice:{text:"",isTyping:!1}}},Me=async C=>{try{_e.value=!0,ze.value=!0,Q.value=!0,w.value=C;const v=await Ps({report_id:C});v.code===0?await re(C):(console.error("生成学习计划失败:",v.msg),$.error(v.msg||"生成学习计划失败"),ze.value=!1,_e.value=!1)}catch(v){console.error("获取学习计划异常:",v),$.error("获取学习计划失败"),ze.value=!1,_e.value=!1}finally{Q.value=!1}},Fe=C=>{D.value.weakModules[C]=!0;const v=se.value[C];N.value.weakModules[C]={id:v.id,subject:v.subject,problemAnalysis:v.problemAnalysis,solutions:v.solutions}},ce=async C=>{try{Q.value=!0;const v=N.value.weakModules[C];if(!v.id){$.error("缺少数据ID，无法保存");return}const V=await As({id:v.id,subject:v.subject,problem_analysis:v.problemAnalysis,solutions:v.solutions});V.code===0?(se.value[C]={...v},z.value.weakModules[C]={subject:{text:v.subject,isTyping:!1},problemAnalysis:{text:v.problemAnalysis,isTyping:!1},solutions:{text:v.solutions,isTyping:!1}},D.value.weakModules[C]=!1,$.success("保存成功")):$.error(V.msg||"保存失败")}catch(v){console.error("保存薄弱模块失败:",v),$.error("保存失败")}finally{Q.value=!1}},be=C=>{D.value.weakModules[C]=!1,delete N.value.weakModules[C]},s=(C,v)=>{const V=`${C}-${v}`;D.value.studyModules[V]=!0;const T=ne.value.stages[C].modules[v];N.value.studyModules[V]={id:T.id,dbId:T.dbId,name:T.name,studyContent:T.studyContent,studyMethod:T.studyMethod,studyMaterials:T.studyMaterials,studyReminder:T.studyReminder}},X=async(C,v)=>{try{Q.value=!0;const V=`${C}-${v}`,T=N.value.studyModules[V];if(!T.dbId){$.error("缺少数据ID，无法保存");return}const Y=await Ds({id:T.dbId,name:T.name,study_content:T.studyContent,study_method:T.studyMethod,study_materials:T.studyMaterials,study_reminder:T.studyReminder});if(Y.code===0){ne.value.stages[C].modules[v]={...T};const F=z.value.studyPlanning.stages[C].modules[v];F.name={text:T.name,isTyping:!1},F.studyContent={text:T.studyContent,isTyping:!1},F.studyMethod={text:T.studyMethod,isTyping:!1},F.studyMaterials={text:T.studyMaterials,isTyping:!1},F.studyReminder={text:T.studyReminder,isTyping:!1},D.value.studyModules[V]=!1,$.success("保存成功")}else $.error(Y.msg||"保存失败")}catch(V){console.error("保存学习模块失败:",V),$.error("保存失败")}finally{Q.value=!1}},xe=(C,v)=>{const V=`${C}-${v}`;D.value.studyModules[V]=!1,delete N.value.studyModules[V]},Ee=()=>{D.value.comprehensiveAdvice=!0,N.value.comprehensiveAdvice=Ce.value.comprehensiveAdvice},me=async()=>{try{if(Q.value=!0,!Ce.value.comprehensiveAdviceId){$.error("缺少数据ID，无法保存");return}const C=await zs({id:Ce.value.comprehensiveAdviceId,advice_content:N.value.comprehensiveAdvice});C.code===0?(Ce.value.comprehensiveAdvice=N.value.comprehensiveAdvice,z.value.comprehensiveAdvice={text:N.value.comprehensiveAdvice,isTyping:!1},D.value.comprehensiveAdvice=!1,$.success("保存成功")):$.error(C.msg||"保存失败")}catch(C){console.error("保存综合建议失败:",C),$.error("保存失败")}finally{Q.value=!1}},ye=()=>{D.value.comprehensiveAdvice=!1,N.value.comprehensiveAdvice=""},we=async()=>{try{if(!w.value){$.error("缺少报告ID，无法保存");return}Re.value=!0;const C=await Ls({report_id:w.value,politics:W.value.politics||0,english:W.value.english||0,business1:W.value.business1||0,business2:W.value.business2||0,total:$e.value||0});C.code===0?$.success("目标分数保存成功"):$.error(C.msg||"保存失败")}catch(C){console.error("保存目标分数失败:",C),$.error("保存失败")}finally{Re.value=!1}};return it({fetchStudyPlan:Me,fetchAndRenderStudyPlan:re,resetStates:Le}),(C,v)=>{const V=qt,T=Nt,Y=os;return Ae((c(),g("div",Wo,[v[27]||(v[27]=e("img",{width:"320",src:"https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/study_sche.png"},null,-1)),v[28]||(v[28]=e("div",{class:"step-num-tag"},[e("span",null,"01"),e("div",{class:"tag-text"},"薄弱模块分析")],-1)),se.value.length>0?(c(),g("div",Yo,[(c(!0),g(te,null,ve(se.value,(F,b)=>{var Ne,qe,Ie,R,Oe,Be,He,Ve,We,Ye,Ge,Je,Ke,Xe,I,et;return c(),g("div",{key:b},[e("div",Go,[v[9]||(v[9]=e("div",{class:"subtitle"},[e("img",{width:"26",src:"https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png",alt:"标签图标"}),e("span",{class:"subtitle-text"},"科目")],-1)),e("div",Jo,[D.value.weakModules[b]?(c(),ae(V,{key:0,modelValue:N.value.weakModules[b].subject,"onUpdate:modelValue":de=>N.value.weakModules[b].subject=de,placeholder:"请输入科目名称",class:"edit-input analysis-text"},null,8,["modelValue","onUpdate:modelValue"])):(c(),g("p",Ko,[e("span",{textContent:j(((qe=(Ne=z.value.weakModules[b])==null?void 0:Ne.subject)==null?void 0:qe.text)||"")},null,8,Xo),(R=(Ie=z.value.weakModules[b])==null?void 0:Ie.subject)!=null&&R.isTyping?(c(),g("span",Qo,"|")):A("",!0)])),e("div",Zo,[D.value.weakModules[b]?(c(),g("div",Io,[m(T,{size:"small",onClick:de=>ce(b),loading:Q.value,class:"save-btn"},{default:M(()=>v[7]||(v[7]=[L(" 保存 ")])),_:2},1032,["onClick","loading"]),m(T,{size:"small",onClick:de=>be(b)},{default:M(()=>v[8]||(v[8]=[L(" 取消 ")])),_:2},1032,["onClick"])])):(c(),ae(T,{key:0,type:"text",size:"small",onClick:de=>Fe(b),icon:Se(Ut),class:"edit-btn"},{default:M(()=>v[6]||(v[6]=[L(" 编辑 ")])),_:2},1032,["onClick","icon"]))])])]),(Be=(Oe=z.value.weakModules[b])==null?void 0:Oe.problemAnalysis)!=null&&Be.text||D.value.weakModules[b]?(c(),g("div",ea,[v[10]||(v[10]=e("div",{class:"subtitle"},[e("img",{width:"26",src:"https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png",alt:"标签图标"}),e("span",{class:"subtitle-text"},"问题分析")],-1)),D.value.weakModules[b]?(c(),ae(V,{key:0,modelValue:N.value.weakModules[b].problemAnalysis,"onUpdate:modelValue":de=>N.value.weakModules[b].problemAnalysis=de,type:"textarea",rows:4,placeholder:"请输入问题分析",class:"edit-textarea analysis-text"},null,8,["modelValue","onUpdate:modelValue"])):(c(),g("p",ta,[e("span",{textContent:j(((Ve=(He=z.value.weakModules[b])==null?void 0:He.problemAnalysis)==null?void 0:Ve.text)||"")},null,8,sa),(Ye=(We=z.value.weakModules[b])==null?void 0:We.problemAnalysis)!=null&&Ye.isTyping?(c(),g("span",la,"|")):A("",!0)]))])):A("",!0),(Je=(Ge=z.value.weakModules[b])==null?void 0:Ge.solutions)!=null&&Je.text||D.value.weakModules[b]?(c(),g("div",oa,[v[11]||(v[11]=e("div",{class:"subtitle"},[e("img",{width:"26",src:"https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png",alt:"标签图标"}),e("span",{class:"subtitle-text"},"解决方案")],-1)),e("div",aa,[D.value.weakModules[b]?(c(),ae(V,{key:0,modelValue:N.value.weakModules[b].solutions,"onUpdate:modelValue":de=>N.value.weakModules[b].solutions=de,type:"textarea",rows:6,placeholder:"请输入解决方案",class:"edit-textarea"},null,8,["modelValue","onUpdate:modelValue"])):(c(),g("p",ia,[e("span",{textContent:j(((Xe=(Ke=z.value.weakModules[b])==null?void 0:Ke.solutions)==null?void 0:Xe.text)||"")},null,8,na),(et=(I=z.value.weakModules[b])==null?void 0:I.solutions)!=null&&et.isTyping?(c(),g("span",ra,"|")):A("",!0)]))])])):A("",!0)])}),128))])):A("",!0),e("div",da,[e("div",ca,[v[13]||(v[13]=e("div",{class:"target-score-title-container"},[e("img",{width:"26",src:"https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png",alt:"标签图标"}),e("span",{class:"target-score-title"},"目标分数")],-1)),e("div",ua,[m(T,{size:"small",onClick:we,loading:Re.value,class:"save-btn"},{default:M(()=>v[12]||(v[12]=[L(" 保存 ")])),_:1},8,["loading"])])]),e("div",va,[v[14]||(v[14]=Ft('<div class="score-row header-row" data-v-0308e626><div class="score-cell" data-v-0308e626>政治</div><div class="score-cell" data-v-0308e626>英语</div><div class="score-cell" data-v-0308e626>业务课一</div><div class="score-cell" data-v-0308e626>业务课二</div><div class="score-cell" data-v-0308e626>总分</div></div>',1)),e("div",ma,[e("div",ga,[Ae(e("input",{class:"score-input","onUpdate:modelValue":v[0]||(v[0]=F=>W.value.politics=F)},null,512),[[gt,W.value.politics,void 0,{number:!0}]])]),e("div",pa,[Ae(e("input",{class:"score-input","onUpdate:modelValue":v[1]||(v[1]=F=>W.value.english=F)},null,512),[[gt,W.value.english,void 0,{number:!0}]])]),e("div",ya,[Ae(e("input",{class:"score-input","onUpdate:modelValue":v[2]||(v[2]=F=>W.value.business1=F)},null,512),[[gt,W.value.business1,void 0,{number:!0}]])]),e("div",fa,[Ae(e("input",{class:"score-input","onUpdate:modelValue":v[3]||(v[3]=F=>W.value.business2=F)},null,512),[[gt,W.value.business2,void 0,{number:!0}]])]),e("div",_a,[Ae(e("input",{class:"score-input","onUpdate:modelValue":v[4]||(v[4]=F=>$e.value=F),disabled:""},null,512),[[gt,$e.value,void 0,{number:!0}]])])])])]),ne.value.stages.length>0?(c(),g("div",ha,[e("div",ba,[v[15]||(v[15]=e("span",null,"03",-1)),e("div",wa,j(ne.value.title),1)]),(c(!0),g(te,null,ve(ne.value.stages,(F,b)=>{var Ne,qe;return c(),g("div",{key:F.id,class:"stage-section"},[(Ne=z.value.studyPlanning.stages[b])!=null&&Ne.title.text?(c(),g("div",ka,[e("span",{textContent:j(z.value.studyPlanning.stages[b].title.text)},null,8,Sa),z.value.studyPlanning.stages[b].title.isTyping?(c(),g("span",$a,"|")):A("",!0)])):A("",!0),(qe=z.value.studyPlanning.stages[b])!=null&&qe.title.text?(c(),g("div",Ca,[(c(!0),g(te,null,ve(F.modules,(Ie,R)=>{var Oe,Be,He,Ve,We,Ye,Ge,Je,Ke,Xe;return c(),g("div",{key:Ie.id,class:"module-container"},[(Be=(Oe=z.value.studyPlanning.stages[b])==null?void 0:Oe.modules[R])!=null&&Be.name.text||D.value.studyModules[`${b}-${R}`]?(c(),g("div",Ma,[D.value.studyModules[`${b}-${R}`]?(c(),ae(V,{key:0,modelValue:N.value.studyModules[`${b}-${R}`].name,"onUpdate:modelValue":I=>N.value.studyModules[`${b}-${R}`].name=I,placeholder:"请输入模块名称",class:"edit-input"},null,8,["modelValue","onUpdate:modelValue"])):(c(),g("div",xa,[e("span",{class:"module-title-text",textContent:j(z.value.studyPlanning.stages[b].modules[R].name.text)},null,8,Va),z.value.studyPlanning.stages[b].modules[R].name.isTyping?(c(),g("span",ja,"|")):A("",!0)])),e("div",Ta,[D.value.studyModules[`${b}-${R}`]?(c(),g("div",Pa,[m(T,{size:"small",onClick:I=>X(b,R),loading:Q.value,class:"save-btn"},{default:M(()=>v[17]||(v[17]=[L(" 保存 ")])),_:2},1032,["onClick","loading"]),m(T,{size:"small",onClick:I=>xe(b,R)},{default:M(()=>v[18]||(v[18]=[L(" 取消 ")])),_:2},1032,["onClick"])])):(c(),ae(T,{key:0,type:"text",size:"small",onClick:I=>s(b,R),icon:Se(Ut),class:"edit-btn"},{default:M(()=>v[16]||(v[16]=[L(" 编辑 ")])),_:2},1032,["onClick","icon"]))])])):A("",!0),(Ve=(He=z.value.studyPlanning.stages[b])==null?void 0:He.modules[R])!=null&&Ve.studyContent.text||D.value.studyModules[`${b}-${R}`]?(c(),g("div",Aa,[v[19]||(v[19]=e("div",{class:"study-item-title"},[e("img",{width:"26",src:"https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png",alt:"菱形图标"}),e("span",{class:"study-item-title-text"},"学习内容")],-1)),D.value.studyModules[`${b}-${R}`]?(c(),ae(V,{key:0,modelValue:N.value.studyModules[`${b}-${R}`].studyContent,"onUpdate:modelValue":I=>N.value.studyModules[`${b}-${R}`].studyContent=I,type:"textarea",rows:4,placeholder:"请输入学习内容",class:"edit-textarea"},null,8,["modelValue","onUpdate:modelValue"])):(c(),g("p",Da,[e("span",{textContent:j(z.value.studyPlanning.stages[b].modules[R].studyContent.text)},null,8,za),z.value.studyPlanning.stages[b].modules[R].studyContent.isTyping?(c(),g("span",La,"|")):A("",!0)]))])):A("",!0),(Ye=(We=z.value.studyPlanning.stages[b])==null?void 0:We.modules[R])!=null&&Ye.studyMethod.text||D.value.studyModules[`${b}-${R}`]?(c(),g("div",Ea,[v[20]||(v[20]=e("div",{class:"study-item-title"},[e("img",{width:"26",src:"https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png",alt:"菱形图标"}),e("span",{class:"study-item-title-text"},"学习方法")],-1)),D.value.studyModules[`${b}-${R}`]?(c(),ae(V,{key:0,modelValue:N.value.studyModules[`${b}-${R}`].studyMethod,"onUpdate:modelValue":I=>N.value.studyModules[`${b}-${R}`].studyMethod=I,type:"textarea",rows:4,placeholder:"请输入学习方法",class:"edit-textarea"},null,8,["modelValue","onUpdate:modelValue"])):(c(),g("p",Ua,[e("span",{textContent:j(z.value.studyPlanning.stages[b].modules[R].studyMethod.text)},null,8,Ra),z.value.studyPlanning.stages[b].modules[R].studyMethod.isTyping?(c(),g("span",Fa,"|")):A("",!0)]))])):A("",!0),(Je=(Ge=z.value.studyPlanning.stages[b])==null?void 0:Ge.modules[R])!=null&&Je.studyMaterials.text||D.value.studyModules[`${b}-${R}`]?(c(),g("div",Na,[v[21]||(v[21]=e("div",{class:"study-item-title"},[e("img",{width:"26",src:"https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png",alt:"菱形图标"}),e("span",{class:"study-item-title-text"},"资料推荐")],-1)),D.value.studyModules[`${b}-${R}`]?(c(),ae(V,{key:0,modelValue:N.value.studyModules[`${b}-${R}`].studyMaterials,"onUpdate:modelValue":I=>N.value.studyModules[`${b}-${R}`].studyMaterials=I,type:"textarea",rows:4,placeholder:"请输入资料推荐",class:"edit-textarea"},null,8,["modelValue","onUpdate:modelValue"])):(c(),g("p",qa,[e("span",{textContent:j(z.value.studyPlanning.stages[b].modules[R].studyMaterials.text)},null,8,Oa),z.value.studyPlanning.stages[b].modules[R].studyMaterials.isTyping?(c(),g("span",Ba,"|")):A("",!0)]))])):A("",!0),(Xe=(Ke=z.value.studyPlanning.stages[b])==null?void 0:Ke.modules[R])!=null&&Xe.studyReminder.text||D.value.studyModules[`${b}-${R}`]?(c(),g("div",Ha,[v[22]||(v[22]=e("div",{class:"study-item-title"},[e("img",{width:"26",src:"https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png",alt:"菱形图标"}),e("span",{class:"study-item-title-text"},"要点提醒")],-1)),D.value.studyModules[`${b}-${R}`]?(c(),ae(V,{key:0,modelValue:N.value.studyModules[`${b}-${R}`].studyReminder,"onUpdate:modelValue":I=>N.value.studyModules[`${b}-${R}`].studyReminder=I,type:"textarea",rows:4,placeholder:"请输入要点提醒",class:"edit-textarea"},null,8,["modelValue","onUpdate:modelValue"])):(c(),g("p",Wa,[e("span",{textContent:j(z.value.studyPlanning.stages[b].modules[R].studyReminder.text)},null,8,Ya),z.value.studyPlanning.stages[b].modules[R].studyReminder.isTyping?(c(),g("span",Ga,"|")):A("",!0)]))])):A("",!0)])}),128))])):A("",!0)])}),128))])):A("",!0),z.value.comprehensiveAdvice.text||D.value.comprehensiveAdvice?(c(),g("div",Ja,[v[26]||(v[26]=e("div",{class:"step-num-tag"},[e("span",null,"04"),e("div",{class:"tag-text"},"综合建议")],-1)),e("div",Ka,[e("div",Xa,[D.value.comprehensiveAdvice?(c(),ae(V,{key:0,modelValue:N.value.comprehensiveAdvice,"onUpdate:modelValue":v[5]||(v[5]=F=>N.value.comprehensiveAdvice=F),type:"textarea",rows:8,placeholder:"请输入综合建议",class:"edit-textarea"},null,8,["modelValue"])):(c(),g("p",Qa,[e("span",{textContent:j(z.value.comprehensiveAdvice.text)},null,8,Za),z.value.comprehensiveAdvice.isTyping?(c(),g("span",Ia,"|")):A("",!0)]))]),e("div",ei,[D.value.comprehensiveAdvice?(c(),g("div",ti,[m(T,{size:"small",onClick:me,loading:Q.value,class:"save-btn"},{default:M(()=>v[24]||(v[24]=[L(" 保存 ")])),_:1},8,["loading"]),m(T,{size:"small",onClick:ye},{default:M(()=>v[25]||(v[25]=[L(" 取消 ")])),_:1})])):(c(),ae(T,{key:0,type:"text",size:"small",onClick:Ee,icon:Se(Ut),class:"edit-btn"},{default:M(()=>v[23]||(v[23]=[L(" 编辑 ")])),_:1},8,["icon"]))])])])):A("",!0)])),[[yt,Se(ze)],[Y,_e.value]])}}}),li=Ot(si,[["__scopeId","data-v-0308e626"]]),oi={class:"generate-container"},ai={class:"credits-info"},ii={class:"credits-count"},ni={class:"report-container"},ri={class:"steps"},di={class:"step-section"},ci={class:"step-content"},ui={class:"form-grid"},vi={class:"form-item"},mi={class:"form-item"},gi={class:"form-item"},pi={class:"form-item"},yi={key:1,class:"loading-option"},fi={class:"form-item"},_i={class:"form-item"},hi={class:"form-item"},bi={class:"form-item"},wi={class:"form-item"},ki={class:"form-item"},Si={class:"form-item"},$i={class:"form-item"},Ci={class:"step-section"},Mi={class:"step-content"},xi={class:"step-container"},Vi={class:"create-btn"},ji={class:"score-grid"},Ti={class:"score-row"},Pi=["onDragstart","onDrop"],Ai={class:"score-label"},Di={class:"score-input-container"},zi={class:"step-section"},Li={class:"step-content"},Ei={class:"english-grid"},Ui={class:"english-row"},Ri={class:"english-item"},Fi={class:"english-item"},Ni={class:"english-item"},qi={class:"english-item"},Oi={class:"english-row"},Bi={class:"english-item"},Hi={class:"english-item"},Wi={class:"step-section"},Yi={class:"step-content"},Gi={class:"school-grid"},Ji={class:"form-item"},Ki={class:"form-item"},Xi={class:"form-item"},Qi={class:"form-item"},Zi={class:"form-item wide-item"},Ii={class:"step-section"},en={class:"step-content"},tn={class:"score-table"},sn={class:"table-row-score"},ln={class:"td-cell"},on={class:"td-cell"},an={class:"td-cell"},nn={class:"td-cell"},rn={class:"td-cell"},dn={class:"personal-demands"},cn={class:"expertise-advice"},un={key:0,class:"ai-animation-container"},vn={key:1,class:"ai-overlay"},mn={class:"ai-overlay-content"},gn={class:"ai-large-icon-wrapper"},pn={key:0,class:"preview-loading","element-loading-text":"正在生成预览..."},yn=["innerHTML"],fn={key:2,class:"preview-empty"},_n={class:"dialog-footer"},hn={class:"dialog-footer"},bn={__name:"generate",setup(De){const it=_(null),ze=_(!1),W=_(null),$e=_(null);let Ce=_(!0);_(!1);const se=_(!1),Q=_([]),_e=_(null),Re={},w=_(!1),D=_([]),N=_(null),z={};_(!1);const ne=_([]);_(null);const oe=_([]),pe=_([]),ge=_([]),he=_(!1),re=_([]),Le=_(null),Me={},Fe=It(),{userInfo:ce}=ds(Fe),be=Ss();be.clearPdfUrl();const s=Wt({student_id:0,name:"",sex:"",undergraduateSchool:"",undergraduateSchoolName:"",undergraduateMajor:"",undergraduateMajorName:"",disciplineCategory:null,firstLevelDiscipline:null,targetMajor:"",targetMajorName:"",majorCode:"",phone:"",examYear:"",isMultiDisciplinary:"",educationalStyle:"",gaokaoScore:"",mathScore1:"",mathScore2:"",statScore:"",linearScore:"",majorScore1:"",majorScore2:"",majorScore3:"",englishScore:"",cet4:"",cet6:"",tofelScore:"",ieltsScore:"",englishLevel:"",region:[],intendedSchools:[],targetSchool:"",targetSchoolName:"",schoolLevel:"",referenceBooks:"",politics:"",englishType:"",mathType:"",professional:"",totalScore:"",personalNeeds:"",weakModules:""});let X=_([]),xe=_(!1),Ee=_(""),me=_("");const ye=_(!1),we=_(!1),C=_(""),v=_(null),V=_(!1),T=_(!1),Y=Wt({title:"",score:""}),F=_(null),b=(i,t)=>{F.value=t,i.dataTransfer.effectAllowed="move",i.target.style.opacity="0.5"},Ne=i=>{i.preventDefault(),i.dataTransfer.dropEffect="move";const t=i.currentTarget;t&&F.value&&t!==F.value&&t.classList.add("drag-over")},qe=(i,t)=>{if(i.preventDefault(),document.querySelectorAll(".score-item").forEach(k=>{k.classList.remove("drag-over")}),!F.value||F.value.id===t.id)return;const d=X.value.findIndex(k=>k.id===F.value.id),p=X.value.findIndex(k=>k.id===t.id);if(d<0||p<0)return;const y=[...X.value],[h]=y.splice(d,1);y.splice(p,0,h),X.value=y,F.value=null,document.querySelectorAll(".score-item").forEach(k=>{k.style.opacity="1"}),$.success("排序已更新")},Ie=i=>{i.target.style.opacity="1",document.querySelectorAll(".score-item").forEach(n=>{n.classList.remove("drag-over")})},R=()=>{T.value=!0,Y.title="",Y.score=""},Oe=()=>{if(!Y.title||!Y.score){$.warning("科目名称和成绩不能为空");return}const t=Math.max(...X.value.map(n=>n.id),0)+1;X.value.push({id:t,title:Y.title,score:Number(Y.score)||Y.score}),T.value=!1,$.success("添加成功")},Be=i=>{if(i!==""){if(se.value=!0,Re[i]){Q.value=Re[i],se.value=!1;return}clearTimeout(_e.value),_e.value=setTimeout(()=>{Gt(i).then(t=>{if(t.code===0&&t.data){const n=t.data.map(d=>({value:d.id.toString(),label:d.name}));Q.value=n,Re[i]=n}else Q.value=[]}).catch(()=>{Q.value=[]}).finally(()=>{se.value=!1})},300)}else Q.value=[]},He=i=>{_s.confirm("确定删除该成绩吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",closeOnClickModal:!1,closeOnPressEscape:!1,showClose:!1}).then(()=>{X.value=X.value.filter(t=>t.id!==i),$.success("删除成功")}).catch(()=>{})},Ve=i=>{if(!s.undergraduateSchool){$.warning("请先选择本科院校");return}i!==fe.value&&(le.value=1,D.value=[],ie.value=!1,fe.value=i),w.value=!0;const t=`${s.undergraduateSchool}_${i||""}_${le.value}`;if(z[t]){const n=z[t];le.value===1?D.value=n.data:D.value=[...D.value,...n.data],ie.value=n.hasMore,w.value=!1;return}clearTimeout(N.value),N.value=setTimeout(()=>{bs(s.undergraduateSchool,i||"",le.value).then(n=>{if(n.code===0&&n.data){const d=n.data.map(p=>({value:p.id.toString(),label:p.major_name}));le.value===1?D.value=d:D.value=[...D.value,...d],ie.value=n.pagination?n.pagination.has_more:!1,z[t]={data:d,hasMore:ie.value}}else le.value===1&&(D.value=[]),ie.value=!1}).catch(()=>{le.value===1&&(D.value=[]),ie.value=!1}).finally(()=>{w.value=!1,ke.value=!0})},300)},We=i=>{if(!i){re.value=[];return}if(he.value=!0,console.log("搜索梦校:",i),Me[i]){re.value=Me[i],he.value=!1;return}clearTimeout(Le.value),Le.value=setTimeout(()=>{Gt(i).then(t=>{if(console.log("搜索梦校结果:",t),t.code===0&&t.data){const n=t.data.map(d=>({value:d.id.toString(),label:d.name}));if(re.value=n,Me[i]=n,s.targetSchool){const d=n.find(p=>p.value===s.targetSchool);d&&(s.targetSchoolName=d.label)}}else re.value=[]}).catch(t=>{console.error("获取梦校列表失败",t),re.value=[]}).finally(()=>{he.value=!1})},300)},Ye=()=>{s.major="",s.targetMajor="",D.value=[],ne.value=[],le.value=1,ie.value=!1,ke.value=!1,fe.value=""},Ge=async i=>{if(console.log("学科门类变更:",i),s.firstLevelDiscipline=null,s.targetMajor="",s.targetMajorName="",s.majorCode="",pe.value=[],ge.value=[],i)try{const t=await Jt(i);t.code===0?pe.value=t.data:$.error(t.msg||"获取一级学科失败")}catch(t){console.error("获取一级学科失败",t),$.error("获取一级学科失败，请稍后重试")}},Je=async i=>{if(console.log("一级学科变更:",i),s.targetMajor="",s.targetMajorName="",s.majorCode="",ge.value=[],i)try{const t=await Kt(i);if(t.code===0){ge.value=t.data;const n=pe.value.find(d=>d.id===i);n&&n.value&&(s.majorCode=n.value+"*",s.targetMajorName=n.label)}else $.error(t.msg||"获取目标专业失败")}catch(t){console.error("获取目标专业失败",t),$.error("获取目标专业失败，请稍后重试")}},Ke=i=>{const t=Array.isArray(i)?i:[],n=ge.value.filter(d=>t.includes(d.id));n.length>0?(s.targetMajorName=n.map(d=>d.label),s.majorCode=n.map(d=>d.major_code).join(","),s.targetMajor=n.map(d=>d.id),console.log("设置后的targetMajor:",s.targetMajor)):(s.majorCode="",s.targetMajorName=[],s.targetMajor=[])};es(()=>s.region,()=>{s.intendedSchools=[]},{deep:!0});const Xe=ls(()=>Rt),I=_(!1),et=_(!1),de=_(!1),Ct=()=>{et.value=!0},Mt=()=>{et.value=!1},ft=i=>{if(!i||i.length<2)return"";const t=i.substring(0,2);return{"01":"哲学","02":"经济学","03":"法学","04":"教育学","05":"文学","06":"历史学","07":"理学","08":"工学","09":"农学",10:"医学",11:"军事学",12:"管理学",13:"艺术学"}[t]||""},st=()=>{const i=parseFloat(s.politics)||0,t=parseFloat(s.englishS)||0,n=parseFloat(s.englishType)||0,d=parseFloat(s.mathType)||0;s.totalScore=(i+t+n+d).toString()},xt=async()=>{var i,t;if(!xe.value){const d=(()=>{const p=[];return s.name||p.push("学员姓名不能为空"),s.sex||p.push("性别不能为空"),s.undergraduateSchool||p.push("本科院校不能为空"),s.undergraduateMajor||p.push("本科专业不能为空"),s.targetMajor||p.push("目标专业不能为空"),s.majorCode||p.push("专业代码不能为空"),s.educationalStyle||p.push("培养方式不能为空"),s.phone||p.push("联系方式不能为空"),s.examYear||p.push("考研年份不能为空"),s.isMultiDisciplinary||p.push("跨专业选项不能为空"),(!s.region||s.region.length===0)&&p.push("地区倾向不能为空"),(!s.intendedSchools||s.intendedSchools.length===0)&&p.push("省份选择不能为空"),s.targetSchool||p.push("梦校不能为空"),s.schoolLevel||p.push("院校层次不能为空"),s.isMultiDisciplinary==1&&X.value.length===0&&p.push("请添加至少一项本科成绩"),p})();if(d.length>0){$.error(d[0]);return}}if(de.value=!de.value,de.value){I.value=!0,window.reportData={targetMajorName:s.targetMajor,majorCode:s.majorCode,firstLevelSubject:ft(s.majorCode)};let n="";s.undergraduateMajorName?n=s.undergraduateMajorName:n=((i=D.value.find(y=>y.value===s.undergraduateMajor))==null?void 0:i.label)||"";let d="";s.undergraduateSchoolName?d=s.undergraduateSchoolName:d=((t=Q.value.find(y=>y.value===s.undergraduateSchool))==null?void 0:t.label)||"";const p={name:s.name,sex:s.sex,phone:s.phone,undergraduateSchool:s.undergraduateSchool,undergraduateSchoolName:d,undergraduateMajor:s.undergraduateMajor,undergraduateMajorName:n,disciplineCategory:s.disciplineCategory,firstLevelDiscipline:s.firstLevelDiscipline,targetMajor:s.targetMajor,targetMajorName:s.targetMajorName,majorCode:s.majorCode,examYear:s.examYear,isMultiDisciplinary:s.isMultiDisciplinary,educationalStyle:s.educationalStyle,mathScores:X.value,undergraduateTranscript:X.value,englishScore:s.englishScore,cet4:s.cet4!==void 0?s.cet4:"",cet6:s.cet6!==void 0?s.cet6:"",tofelScore:s.tofelScore,ieltsScore:s.ieltsScore,englishAbility:s.englishLevel!==void 0?s.englishLevel:"",targetRegion:Array.isArray(s.region)&&s.region.length>0?s.region[0]:s.region||"",region:Array.isArray(s.region)&&s.region.length>0?s.region[0]:s.region||"",targetProvinces:Array.isArray(s.intendedSchools)?s.intendedSchools.join(","):s.intendedSchools||"",targetSchool:s.targetSchool,targetSchoolName:s.targetSchoolName,schoolLevel:s.schoolLevel,referenceBooks:s.referenceBooks,politics:s.politics,englishType:s.englishType,englishS:s.englishS,mathType:s.mathType,mathScore:s.mathScore1,professionalScore:s.professional,totalScore:s.totalScore,personalNeeds:s.personalNeeds,weakModules:s.weakModules};s.student_id!=0&&(p.student_id=s.student_id),console.log(p);try{console.log("开始处理学生信息..."),console.log(p);const y=await ks(p);if(y.code!==0)throw new Error(y.msg||"学生信息处理失败");console.log("学生信息处理成功:",y.data);const h=y.data.report_id;if(me.value=y.data.report_id,de.value=!1,W.value.show(),h)if(it.value.fetchStudyPlan(h),await W.value.setReportId(h),Ce.value=!1,s.majorCode){console.log("开始获取国家线数据...",s.firstLevelDiscipline);try{const k=await hs(s.firstLevelDiscipline);k.code===0&&k.data?(console.log("获取国家线数据成功:",k.data),G.value=k.data,Z.value=!0,W.value&&W.value.updateNationalLineData(k.data)):(console.error("获取国家线数据失败:",k.msg),$.warning("获取国家线数据失败: "+(k.msg||"未知错误")))}catch(k){console.error("获取国家线数据异常:",k),$.error("获取国家线数据异常，将使用默认数据")}}else console.warn("缺少专业代码，无法获取国家线数据"),$.warning("缺少专业代码，无法获取国家线数据")}catch(y){console.error("处理过程中发生错误:",y),de.value=!1}}},_t=()=>{};ts(()=>{at(()=>{window.addEventListener("resize",_t),window.addEventListener("student-selected",ht)}),Vt()});const Vt=async()=>{try{const i=await ws();i.code===0?oe.value=i.data:$.error(i.msg||"获取学科门类失败")}catch(i){console.error("获取学科门类失败",i),$.error("获取学科门类失败，请稍后重试")}},jt=async()=>{if(s.disciplineCategory)try{const i=await Jt(s.disciplineCategory);i.code===0&&(pe.value=i.data)}catch(i){console.error("加载一级学科失败",i)}if(s.firstLevelDiscipline)try{const i=await Kt(s.firstLevelDiscipline);i.code===0&&(ge.value=i.data)}catch(i){console.error("加载二级学科失败",i)}};cs(()=>{window.removeEventListener("resize",_t),window.removeEventListener("student-selected",ht)});const ht=i=>{const t=i.detail;console.log("studentData",t),t&&(W.value&&W.value.isVisible&&(console.log("检测到已有报告，开始重置状态"),Ce.value=!0,W.value.hide(),typeof W.value.resetData=="function"&&W.value.resetData(),Z.value=!1,console.log("报告状态重置完成")),console.log("选中的学员完整数据:",JSON.stringify(t,null,2)),console.log("targetProvinces 数据类型:",typeof t.targetProvinces),console.log("targetProvinces 值:",t.targetProvinces),Tt(t),console.log("填充后的省份数据:",s.intendedSchools),console.log("填充后的区域数据:",s.region))},Tt=i=>{if(console.log("填充学员数据到表单:",i),s.student_id=i.id||0,s.name=i.name||"",s.sex=i.sex===1?"1":i.sex===2?"2":"",s.phone=i.phone||"",s.undergraduateSchool=i.undergraduateSchool||"",s.undergraduateMajor=i.undergraduateMajor||"",s.undergraduateSchoolName=i.undergraduateSchoolName||"",s.undergraduateMajorName=i.undergraduateMajorName||"",s.disciplineCategory=i.disciplineCategory||null,s.firstLevelDiscipline=i.firstLevelDiscipline||null,s.targetMajor=i.targetMajor||"",s.targetMajorName=i.targetMajorName||"",s.majorCode=i.majorCode||"",i.undergraduateSchool&&i.undergraduateSchoolName&&(Q.value.find(d=>d.value===i.undergraduateSchool)||Q.value.push({value:i.undergraduateSchool,label:i.undergraduateSchoolName})),i.undergraduateMajor&&i.undergraduateMajorName&&(D.value.find(d=>d.value===i.undergraduateMajor)||D.value.push({value:i.undergraduateMajor,label:i.undergraduateMajorName})),i.examYear&&(s.examYear=i.examYear.toString()),s.isMultiDisciplinary=i.isMultiDisciplinary===1?"1":"2",s.educationalStyle=i.educationalStyle===1?"1":"0",s.englishScore=i.englishScore||"",s.cet4=i.cet4||"",s.cet6=i.cet6||"",s.tofelScore=i.tofelScore||"",s.ieltsScore=i.ieltsScore||"",s.englishLevel=i.englishAbility||"",s.intendedSchools=[],i.targetProvinces){if(Array.isArray(i.targetProvinces)){const n=i.targetProvinces.filter(d=>Rt.some(p=>p.value===d));n.length>0?(s.intendedSchools=n,console.log("有效的省份数据:",n)):console.warn("没有找到有效的省份数据")}else if(typeof i.targetProvinces=="string"){const d=i.targetProvinces.split(",").filter(p=>{const y=p.trim();return y!==""&&Rt.some(h=>h.value===y)});d.length>0?(s.intendedSchools=d,console.log("解析后的有效省份:",d)):console.warn("解析后没有找到有效的省份数据")}}console.log("设置省份选择:",s.intendedSchools),s.region=i.targetRegion;const t=s.intendedSchools;if(at(()=>{s.intendedSchools=t}),s.targetSchool=i.targetSchool||"",s.targetSchoolName=i.targetSchoolName||"",s.targetSchool&&s.targetSchoolName&&(re.value=[{value:s.targetSchool,label:s.targetSchoolName}]),s.schoolLevel=i.schoolLevel||"",s.referenceBooks=i.referenceBooks||"",i.undergraduateTranscript)try{let n=i.undergraduateTranscript;typeof n=="string"&&(n=JSON.parse(n)),Array.isArray(n)&&n.length>0&&(X.value=n.map((d,p)=>({id:p+1,title:d.title||d.name||`课程${p+1}`,score:d.score||""})))}catch(n){console.error("解析undergraduateTranscript失败:",n)}if(X.value.length===0&&(X.value=[{id:1,title:"高数(上)",score:""},{id:2,title:"高数(下)",score:""},{id:3,title:"概率论",score:""},{id:4,title:"线性代数",score:""}]),s.politics=i.politics||"",s.englishS=i.englishS||"",s.englishType=i.englishType||"",s.mathType=i.mathType||"",s.totalScore=i.totalScore||"",st(),console.log("填充考研预估成绩:",{政治:s.politics,英语成绩:s.englishS,业务课一:s.englishType,业务课二:s.mathType,总分:s.totalScore}),s.politics&&s.englishS&&s.englishType&&s.mathType){const n=parseFloat(s.politics)||0,d=parseFloat(s.englishS)||0,p=parseFloat(s.englishType)||0,y=parseFloat(s.mathType)||0;s.totalScore=(n+d+p+y).toString()}s.personalNeeds=i.personalNeeds||"",s.weakModules=i.weakModules||"",jt(),$.success("学员信息已填充到表单中")},ct=(i=!0)=>{console.log(`生成${i?"A区":"B区"}图表配置`);const t=G.value||{subject_code:"",subject_name:"",years:[],a_total:[],a_single_100:[],a_single_over100:[],b_total:[],b_single_100:[],b_single_over100:[]};console.log("国家线数据:",t);const n=t.years.length>0?t.years:["2021","2022","2023","2024","2025"],d=i?t.a_total.length>0?t.a_total:[360,370,360,370,370]:t.b_total.length>0?t.b_total:[340,350,340,350,350],p=i?t.a_single_100.length>0?t.a_single_100:[60,65,60,65,65]:t.b_single_100.length>0?t.b_single_100:[55,60,55,60,60],y=i?t.a_single_over100.length>0?t.a_single_over100:[90,95,90,95,95]:t.b_single_over100.length>0?t.b_single_over100:[85,90,85,90,90];console.log(`${i?"A区":"B区"}图表数据:`,{years:n,totalScores:d,single100Scores:p,singleOver100Scores:y});const h=i?"A区":"B区";let k="",x="",U="";return s.targetMajor&&(k=s.targetMajor),s.majorCode&&(x=s.majorCode),s.majorCode&&(U=ft(s.majorCode)),{title:{text:`${h}  专业名称：${k}  专业代码：${x}  一级学科：${U}`,left:"left",textStyle:{fontSize:14,fontWeight:"bold",color:"#1bb394"},top:10},grid:{left:40,right:60,top:70,bottom:50,containLabel:!0},legend:{data:["总分","单科(满分=100)","单科(满分>100)"],right:10,top:10,icon:"rect",itemWidth:16,itemHeight:8,textStyle:{fontSize:12}},tooltip:{trigger:"axis",backgroundColor:"rgba(0,0,0,0.7)",borderRadius:8,textStyle:{color:"#fff"}},xAxis:{type:"category",boundaryGap:!1,data:n,axisLine:{lineStyle:{color:"#1bb394"}},axisLabel:{color:"#666"}},yAxis:{type:"value",min:0,max:400,interval:50,splitNumber:8,splitLine:{show:!0,lineStyle:{color:"#eee",type:"dashed"}},axisLine:{show:!1},axisLabel:{color:"#666",fontSize:12}},series:[{name:"总分",type:"line",smooth:!0,symbol:"circle",symbolSize:6,areaStyle:{color:"rgba(255, 153, 0, 0.15)",origin:"start"},lineStyle:{color:"#ff9900",width:2},label:{show:!0,position:"top",fontSize:12,color:"#ff9900",fontWeight:"bold",offset:[0,-20],formatter:function(B){return B.value}},data:d},{name:"单科(满分=100)",type:"line",smooth:!0,symbol:"circle",symbolSize:6,areaStyle:{color:"rgba(27, 179, 148, 0.15)",origin:"start"},lineStyle:{color:"#1bb394",width:2},label:{show:!0,position:"top",fontSize:12,color:"#1bb394",fontWeight:"bold",offset:[0,-10],formatter:function(B){return B.value}},data:p},{name:"单科(满分>100)",type:"line",smooth:!0,symbol:"circle",symbolSize:6,areaStyle:{color:"rgba(255, 99, 132, 0.10)",origin:"start"},lineStyle:{color:"#ff6384",width:2},label:{show:!0,position:"top",fontSize:12,color:"#ff6384",fontWeight:"bold",offset:[0,5],formatter:function(B){return B.value}},data:y}]}},Pt=async()=>{console.log("开始重新创建预览中的图表"),console.log("当前专业分析显示状态:",Z.value),console.log("专业分析组件引用:",$e.value),console.log("国家线数据:",G.value),console.log("表单数据:",{targetMajor:s.targetMajor,majorCode:s.majorCode,targetSchoolName:s.targetSchoolName});try{const i=await pt(()=>import("./index-YsyrxmwF.js"),[]);return console.log("echarts导入成功"),Z.value?(console.log("开始处理专业分析图表"),await At(i)):console.log("专业分析未显示，跳过专业分析图表处理"),console.log("开始处理报告内容图表"),await ut(i),console.log("所有图表重新创建完成"),!0}catch(i){return console.error("重新创建图表失败:",i),$.error("图表重新创建失败: "+i.message),!1}},At=async i=>{var n;console.log("开始重新创建专业分析图表");const t=(n=v.value)==null?void 0:n.querySelectorAll('.major-chart, [id*="preview-major-chart"]');if(console.log("预览中找到的专业分析图表容器数量:",(t==null?void 0:t.length)||0),!t||t.length===0){console.log("预览中没有找到专业分析图表容器");return}if(!$e.value){console.log("没有专业分析组件引用，使用备用方法"),await bt(i,t);return}for(let d=0;d<t.length;d++){const p=t[d];if(console.log(`重新创建第${d+1}个专业分析图表`),!p){console.warn(`第${d+1}个图表容器不存在`);continue}p.style.width="100%",p.style.height="280px",p.style.display="block",p.style.visibility="visible",await new Promise(h=>setTimeout(h,100));const y=p.getBoundingClientRect();if(console.log(`第${d+1}个图表容器尺寸:`,y),y.width===0||y.height===0){console.warn(`第${d+1}个图表容器尺寸为0，跳过`);continue}try{const h=d===0,k=$e.value.getChartOption(h);console.log(`第${d+1}个图表配置:`,k),p._echarts_instance_&&(p._echarts_instance_.dispose(),delete p._echarts_instance_);const x=i.init(p);x.setOption(k),p._echarts_instance_=x,console.log(`第${d+1}个专业分析图表重新创建完成`),await new Promise(U=>setTimeout(U,200))}catch(h){console.error(`重新创建第${d+1}个专业分析图表失败:`,h);try{const x=ct(d===0);p._echarts_instance_&&(p._echarts_instance_.dispose(),delete p._echarts_instance_);const U=i.init(p);U.setOption(x),p._echarts_instance_=U,console.log(`第${d+1}个专业分析图表使用备用方法创建完成`)}catch(k){console.error("备用方法也失败:",k)}}}},bt=async(i,t)=>{console.log("使用备用方法重新创建专业分析图表");for(let n=0;n<t.length;n++){const d=t[n];if(!d)continue;d.style.width="100%",d.style.height="280px",d.style.display="block",d.style.visibility="visible",await new Promise(y=>setTimeout(y,100));const p=d.getBoundingClientRect();if(!(p.width===0||p.height===0))try{const h=ct(n===0);d._echarts_instance_&&(d._echarts_instance_.dispose(),delete d._echarts_instance_);const k=i.init(d);k.setOption(h),d._echarts_instance_=k,console.log(`备用方法：第${n+1}个专业分析图表创建完成`),await new Promise(x=>setTimeout(x,200))}catch(y){console.error(`备用方法：第${n+1}个图表创建失败:`,y)}}},ut=async i=>{var d;console.log("开始重新创建报告内容图表");const t=document.querySelectorAll(".content-container [_echarts_instance_]");if(console.log("找到原始报告内容图表数量:",t.length),t.length===0){console.log("没有找到原始报告内容图表");return}const n=(d=v.value)==null?void 0:d.querySelectorAll('.content-chart, [id*="preview-content-chart"]');if(console.log("预览中找到的报告内容图表容器数量:",(n==null?void 0:n.length)||0),!n||n.length===0){console.log("预览中没有找到报告内容图表容器");return}for(let p=0;p<Math.min(t.length,n.length);p++){const y=t[p],h=n[p];if(y._echarts_instance_&&h){console.log(`重新创建第${p+1}个报告内容图表`),h.style.width="100%",h.style.height="400px",h.style.display="block",h.style.visibility="visible";const k=y._echarts_instance_.getOption(),x=i.init(h);x.setOption(k),h._echarts_instance_=x,console.log(`第${p+1}个报告内容图表重新创建完成`),await new Promise(U=>setTimeout(U,300))}}},wt=_(null),r=async()=>{if(!me.value){$.warning("请先生成报告内容");return}ye.value=!0,await at(),await new Promise(i=>setTimeout(i,100)),console.log("previewPdfRef.value:",wt.value),console.log(s.firstLevelDiscipline);try{await wt.value.initPdfData(me.value,s.firstLevelDiscipline),console.log("PDF数据初始化成功，报告ID:",me.value)}catch(i){console.error("PDF数据初始化失败:",i),$.error("加载报告数据失败: "+(i.message||"未知错误")),ye.value=!1}},l=async()=>{!me.value&&be.pdfUrl.value?window.open(be.pdfUrl.value,"_blank"):$.warning("请先预览报告生成PDF文件")},a=async()=>{if(!C.value){$.warning("没有可生成PDF的内容");return}V.value=!0;try{$.info("正在准备PDF内容，请稍候..."),console.log("开始测试简化PDF生成...");try{console.log("开始生成测试PDF");const{jsPDF:i}=await pt(async()=>{const{jsPDF:d}=await import("./PreviewPdf-BQ_I87f2.js").then(p=>p.j);return{jsPDF:d}},__vite__mapDeps([0,1,2,3,4,5,6,7,8,9])),t=new i("p","mm","a4");t.setFontSize(20),t.text("Test Report",20,30),t.setFontSize(14),t.text("This is a test PDF document",20,50),t.text("Used to verify PDF generation functionality",20,70),t.setFontSize(12),t.text("Student Name: "+(s.name||"Not filled"),20,100),t.text("Target Major: "+(s.targetMajor||"Not filled"),20,120),t.text("Major Code: "+(s.majorCode||"Not filled"),20,140),t.text("Score Estimation:",20,170),t.text("Politics: "+(s.politics||"0"),30,190),t.text("English: "+(s.englishType||"0"),30,210),t.text("Math: "+(s.mathType||"0"),30,230),t.text("Professional: "+(s.professional||"0"),30,250),t.addPage(),t.setFontSize(16),t.text("Page 2 Content",20,30),t.setFontSize(12),t.text("This is the second page test content",20,50),t.text("If this PDF displays normally, basic functionality works",20,70),t.text("Numbers: 0123456789",20,100),t.text("Symbols: !@#$%^&*()_+-=[]{}|;:,.<>?",20,120),t.text("Date: "+new Date().toLocaleDateString(),20,140);const n=t.output("blob");if(console.log("测试PDF生成成功，大小:",n.size,"bytes"),n&&n.size>1e3){console.log("测试PDF生成成功，现在生成完整样式PDF"),await f();return}}catch(i){console.error("测试PDF生成失败:",i)}await f()}catch(i){console.error("PDF生成失败:",i),$.error("PDF生成失败: "+i.message)}finally{V.value=!1}},f=async()=>{try{console.log("开始生成完整样式PDF..."),$.info("正在生成完整样式PDF，保留前端效果..."),console.log("开始确保图表正确渲染"),await Pt()||console.warn("图表渲染可能有问题，但继续生成PDF"),await new Promise(d=>setTimeout(d,2e3));const t=document.createElement("div");t.style.position="fixed",t.style.left="50%",t.style.transform="translateX(-50%)",t.style.top="0",t.style.width="1200px",t.style.height="auto",t.style.backgroundColor="white",t.style.zIndex="9999",t.style.overflow="visible",t.style.visibility="visible",t.style.display="block",t.innerHTML=C.value,t.querySelectorAll(".edit-btn, button[type='text'][size='small']").forEach(d=>d.remove()),document.body.appendChild(t);try{await new Promise(E=>setTimeout(E,1e3)),console.log("临时容器子元素数量:",t.children.length),console.log("临时容器滚动高度:",t.scrollHeight),console.log("临时容器内容长度:",t.innerHTML.length),t.querySelectorAll("*").forEach(E=>{E.style&&(E.style.visibility="visible",E.style.opacity="1",E.style.display==="none"&&(E.style.display="block"))}),t.querySelectorAll('[id*="preview-"], .echarts-box, .preview-chart').forEach(E=>{E.style.width="100%",E.style.height="400px",E.style.display="block",E.style.visibility="visible",E.style.opacity="1",E.style.backgroundColor="#fff"}),t.querySelectorAll(".school-tags").forEach(E=>{E.style.position="absolute",E.style.top="40px",E.style.left="0",E.style.right="0",E.style.display="flex",E.style.gap="4px",E.style.justifyContent="center",E.style.alignItems="center",E.style.width="100%",E.style.textAlign="center"}),await O(t),await H(t),await new Promise(E=>setTimeout(E,2e3)),console.log("开始处理网络图片，确保PDF兼容性..."),await O(t),console.log("图片处理完成，等待stabilization..."),await new Promise(E=>setTimeout(E,3e3)),t.querySelectorAll(".step-num-tag").forEach((E,ee)=>{const Ze=E.style.backgroundImage;Ze&&Ze.includes("data:image")?console.log(`step-num-tag ${ee+1} 背景图片已转换为base64`):console.warn(`step-num-tag ${ee+1} 背景图片未转换:`,Ze)}),console.log("开始强制设置内联样式..."),je(t),console.log("等待样式应用完成..."),await new Promise(E=>setTimeout(E,2e3)),console.log("开始使用html2canvas截图，容器尺寸:",t.scrollWidth,"x",t.scrollHeight),t.style.opacity="1",t.style.zIndex="9999",t.style.backgroundColor="#ffffff",await new Promise(E=>setTimeout(E,500));const x=await(await pt(()=>import("./PreviewPdf-BQ_I87f2.js").then(E=>E.h),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9]))).default(t,{scale:2,useCORS:!0,allowTaint:!0,backgroundColor:"#ffffff",width:t.scrollWidth||1200,height:t.scrollHeight||800,logging:!0,removeContainer:!1,foreignObjectRendering:!1,imageTimeout:3e4,proxy:void 0,onclone:E=>{console.log("开始克隆文档进行截图");const ee=E.querySelector("div");if(ee){ee.style.width="1200px",ee.style.display="block",ee.style.visibility="visible",ee.style.opacity="1",ee.style.backgroundColor="#ffffff",ee.querySelectorAll("*").forEach(P=>{P.style&&(P.style.visibility="visible",P.style.opacity="1",P.style.display==="none"&&(P.style.display="block"),P.tagName==="DIV"&&!P.style.backgroundColor&&(P.style.backgroundColor="transparent"))}),ee.querySelectorAll('[id*="preview-"], .echarts-box, .preview-chart').forEach(P=>{P.style.width="100%",P.style.height="400px",P.style.display="block",P.style.visibility="visible",P.style.opacity="1",P.style.backgroundColor="#fff"}),ee.querySelectorAll(".school-tags").forEach(P=>{P.style.position="absolute",P.style.top="40px",P.style.left="0",P.style.right="0",P.style.display="flex",P.style.gap="4px",P.style.justifyContent="center",P.style.alignItems="center",P.style.width="100%",P.style.textAlign="center"});const ot=ee.querySelectorAll('[class*="step-num-tag"]'),rt=t.querySelectorAll('[class*="step-num-tag"]');console.log(`克隆文档中找到 ${ot.length} 个step-num-tag元素`),console.log(`原始容器中找到 ${rt.length} 个step-num-tag元素`),ot.forEach((P,Pe)=>{if(P.style.width="265px",P.style.height="40px",P.style.backgroundSize="100% 100%",P.style.backgroundRepeat="no-repeat",P.style.backgroundPosition="center",P.style.display="flex",P.style.alignItems="center",P.style.marginBottom="20px",rt[Pe]&&rt[Pe].style.backgroundImage)P.style.backgroundImage=rt[Pe].style.backgroundImage,console.log(`设置step-num-tag ${Pe+1} 背景图片:`,P.style.backgroundImage.substring(0,100)+"...");else{console.warn(`step-num-tag ${Pe+1} 没有找到背景图片，查找所有已转换的背景图片`);const Et=t.querySelector('[class*="step-num-tag"]');Et&&Et.style.backgroundImage&&(P.style.backgroundImage=Et.style.backgroundImage,console.log("使用备用背景图片:",P.style.backgroundImage.substring(0,100)+"..."))}});const $t=ee.querySelectorAll("img"),Lt=t.querySelectorAll("img");console.log(`克隆文档中找到 ${$t.length} 个img元素`),console.log(`原始容器中找到 ${Lt.length} 个img元素`),$t.forEach((P,Pe)=>{P.style.display="block",P.style.visibility="visible",P.style.opacity="1",Lt[Pe]&&(P.src=Lt[Pe].src,console.log(`设置img ${Pe+1} src:`,P.src.substring(0,100)+"...")),P.src&&P.src.includes("yqzx-1300870289.cos.ap-nanjing.myqcloud.com")&&(P.crossOrigin="anonymous",P.referrerPolicy="no-referrer",console.log("为COS图片设置跨域属性:",P.src))}),ee.querySelectorAll('[class*="recommend-icon"] img').forEach((P,Pe)=>{P.style.width="27px",P.style.height="21px",P.style.display="inline-block",P.style.visibility="visible",P.style.opacity="1",console.log(`强制设置recommend-icon图片 ${Pe+1}:`,P.src.substring(0,100)+"...")}),console.log("克隆容器设置完成")}}});if(!x||x.width===0||x.height===0)throw new Error("Canvas生成失败或尺寸无效");console.log("Canvas尺寸:",x.width,"x",x.height);const U=x.toDataURL("image/jpeg",.95);if(console.log("图片数据长度:",U.length),!U||U==="data:,"||U.length<1e3)throw new Error("图片数据生成失败或图片为空");const q=U.substring(0,100);console.log("图片数据开头:",q);const B=new Image;await new Promise((E,ee)=>{B.onload=()=>{console.log("测试图片加载成功，尺寸:",B.width,"x",B.height),E()},B.onerror=()=>{console.error("测试图片加载失败"),ee(new Error("图片数据无效"))},B.src=U});const{jsPDF:ue}=await pt(async()=>{const{jsPDF:E}=await import("./PreviewPdf-BQ_I87f2.js").then(ee=>ee.j);return{jsPDF:E}},__vite__mapDeps([0,1,2,3,4,5,6,7,8,9])),u=new ue("l","mm","a3"),J=420,K=297,vt=25,mt=K-vt,lt=J,Qe=x.height*lt/x.width;console.log("PDF参数:",{pageWidth:J,pageHeight:K,imgWidth:lt,imgHeight:Qe,pageBottomMargin:vt});let S=1,tt=0;if(Qe<=mt)u.addImage(U,"JPEG",0,0,lt,Qe),u.setFontSize(10),u.setTextColor(128),u.text(`${S}`,J-20,K-10);else{let E=Qe,ee=0;for(;E>0;){const Ze=Math.min(E,mt),Ht=Math.round(ee/Qe*x.height),St=Math.round(Ze/Qe*x.height),ot=document.createElement("canvas"),rt=ot.getContext("2d");ot.width=x.width,ot.height=St,rt.drawImage(x,0,Ht,x.width,St,0,0,x.width,St);const $t=ot.toDataURL("image/jpeg",.95);S>1&&u.addPage(),u.addImage($t,"JPEG",0,0,lt,Ze),u.setFontSize(10),u.setTextColor(128),u.text(`${S}`,J-20,K-10),ee+=Ze,E-=Ze,S++,console.log(`生成第 ${S-1} 页，剩余高度: ${E}`)}}console.log("完整样式PDF生成完成，页数:",u.getNumberOfPages());const kt=u.output("blob");console.log("PDF文件大小:",kt.size,"bytes"),kt.size<5e4&&console.warn("PDF文件较小，可能内容有问题");const as=s.name||"未知学员",is=new Date().toISOString().slice(0,19).replace(/:/g,"-"),Dt=`${as}_考研分析报告_${is}.pdf`,Bt=URL.createObjectURL(kt),ns=new File([kt],Dt,{type:"application/pdf"});$.info("正在上传PDF到COS，请稍候...");const zt=await Ms(ns);typeof zt.Location=="string"&&(await Es({report_id:me.value,pdf_url:"https://"+zt.Location}),Ee.value="https://"+zt.Location,$.success("PDF上传成功"));const nt=document.createElement("a");nt.href=Bt,nt.download=Dt,nt.style.display="none",document.body.appendChild(nt),nt.click(),document.body.removeChild(nt),URL.revokeObjectURL(Bt),$.success(`完整样式PDF下载成功！文件名: ${Dt}`)}catch(d){console.error("完整PDF生成失败:",d),$.error("完整PDF生成失败: "+d.message)}finally{t.parentNode&&document.body.removeChild(t)}}catch(i){console.error("生成完整样式PDF失败:",i),$.error("生成完整样式PDF失败: "+i.message)}},H=async i=>{var t;console.log("在容器中重新创建图表");try{i.querySelectorAll(".edit-btn, button[type='text'][size='small']").forEach(y=>y.remove()),await O(i);const d=await pt(()=>import("./index-YsyrxmwF.js"),[]),p=i.querySelectorAll('[id*="preview-"], .echarts-box, .preview-chart');console.log("找到图表容器数量:",p.length);for(let y=0;y<p.length;y++){const h=p[y];h.style.width="100%",h.style.height="400px",h.style.display="block",h.style.visibility="visible",h.style.opacity="1",h.style.backgroundColor="#fff",await new Promise(k=>setTimeout(k,100));try{let k=null;const x=(t=v.value)==null?void 0:t.querySelectorAll("[_echarts_instance_]");if(x&&x[y]&&x[y]._echarts_instance_&&(k=x[y]._echarts_instance_.getOption(),console.log(`从预览图表${y+1}获取配置成功`)),!k)if(console.log(`使用备用方法为图表${y+1}生成配置`),h.id.includes("major-chart")){const U=h.id.includes("major-chart-0");k=ct(U)}else k={title:{text:"图表数据",left:"center",textStyle:{fontSize:16,color:"#333"}},tooltip:{},xAxis:{type:"category",data:["数据1","数据2","数据3"]},yAxis:{type:"value"},series:[{name:"数据",type:"line",data:[120,200,150],lineStyle:{color:"#1bb394"}}]};if(k){const U=d.init(h);U.setOption(k),h._echarts_instance_=U,console.log(`图表${y+1}创建成功`),await new Promise(q=>setTimeout(q,300)),U.resize()}}catch(k){console.error(`创建图表${y+1}失败:`,k)}}console.log("容器中所有图表重新创建完成")}catch(n){console.error("在容器中重新创建图表失败:",n)}};_(null),_(null);const G=_(null),Z=_(!1),O=async i=>{console.log("开始处理网络图片显示问题");const t=i.querySelectorAll("img"),n=[];for(let h of t)if(h.src&&(h.src.startsWith("http://")||h.src.startsWith("https://"))){const k=new Promise((x,U)=>{if(h.src.includes("yqzx-1300870289.cos.ap-nanjing.myqcloud.com")){console.log("COS图片直接使用，无需转换:",h.src);const q=new Image;q.crossOrigin="anonymous",q.onload=()=>{console.log("COS图片验证加载成功:",h.src),x()},q.onerror=()=>{console.warn("COS图片验证失败，尝试转换为base64:",h.src),p(h,x)},q.src=h.src}else console.log("非COS图片，转换为base64:",h.src),p(h,x)});n.push(k)}const d=i.querySelectorAll("*");for(let h of d){const x=window.getComputedStyle(h).backgroundImage;if(x&&x!=="none"&&x.includes("url(")){const U=x.match(/url\(["']?(.*?)["']?\)/);if(U&&U[1]&&(U[1].startsWith("http://")||U[1].startsWith("https://"))){const q=U[1];console.log("发现CSS背景图片:",q,"在元素:",h.className||h.tagName);const B=new Promise(ue=>{y(h,q,ue)});n.push(B)}}}function p(h,k){const x=document.createElement("canvas"),U=x.getContext("2d"),q=new Image;q.crossOrigin="anonymous",q.referrerPolicy="no-referrer";let B=0;const ue=3,u=()=>{q.onload=()=>{if(q.width===0||q.height===0){console.warn("图片尺寸为0，保持原地址:",h.src),k();return}x.width=q.width,x.height=q.height;try{U.drawImage(q,0,0);const K=x.toDataURL("image/jpeg",.95);K&&K.length>1e3?(h.src=K,console.log("图片转换成功:",h.src.substring(0,50)+"...","原URL:",q.src)):console.warn("base64数据过小，保持原地址:",h.src),k()}catch(K){console.warn("图片转换失败，保持原地址:",K),k()}},q.onerror=()=>{B++,B<ue?(console.warn(`图片加载失败，正在重试 (${B}/${ue}):`,h.src),setTimeout(()=>{u()},2e3*B)):(console.warn("图片加载失败，已达最大重试次数，保持原地址:",h.src),k())},setTimeout(()=>{q.complete===!1&&(console.warn("图片加载超时，强制结束:",h.src),q.src="",k())},15e3);const J=h.src;if(J.includes("yqzx-1300870289.cos.ap-nanjing.myqcloud.com"))q.src=J;else{const K=J.includes("?")?"&":"?";q.src=`${J}${K}t=${Date.now()}`}};u()}function y(h,k,x){const U=document.createElement("canvas"),q=U.getContext("2d"),B=new Image;B.crossOrigin="anonymous",B.referrerPolicy="no-referrer";let ue=0;const u=3,J=()=>{if(B.onload=()=>{if(B.width===0||B.height===0){console.warn("背景图片尺寸为0，保持原样式:",k),x();return}U.width=B.width,U.height=B.height;try{q.drawImage(B,0,0);const K=U.toDataURL("image/png",.95);K&&K.length>1e3?(h.style.backgroundImage=`url("${K}")`,console.log("背景图片转换成功:",k,"-> base64")):console.warn("背景图片base64数据过小，保持原样式:",k),x()}catch(K){console.warn("背景图片转换失败，保持原样式:",K),x()}},B.onerror=()=>{ue++,ue<u?(console.warn(`背景图片加载失败，正在重试 (${ue}/${u}):`,k),setTimeout(()=>{J()},2e3*ue)):(console.warn("背景图片加载失败，已达最大重试次数，保持原样式:",k),x())},setTimeout(()=>{B.complete===!1&&(console.warn("背景图片加载超时，强制结束:",k),B.src="",x())},15e3),k.includes("yqzx-1300870289.cos.ap-nanjing.myqcloud.com"))B.src=k;else{const K=k.includes("?")?"&":"?";B.src=`${k}${K}t=${Date.now()}`}};J()}console.log("开始处理",n.length,"个图片/背景图片"),await Promise.all(n),console.log("所有网络图片和背景图片处理完成")},je=i=>{console.log("开始强制设置内联样式...");const t=i.querySelectorAll('[class*="step-num-tag"], .step-num-tag');console.log(`找到 ${t.length} 个step-num-tag元素需要强制设置样式`),t.forEach((y,h)=>{if(y.style.width="265px",y.style.height="40px",y.style.backgroundSize="100% 100%",y.style.backgroundRepeat="no-repeat",y.style.backgroundPosition="center",y.style.display="flex !important",y.style.alignItems="center",y.style.marginBottom="20px",y.style.visibility="visible",y.style.opacity="1",!y.style.backgroundImage||!y.style.backgroundImage.includes("data:image")){const k=i.querySelector('[class*="step-num-tag"][style*="data:image"]');k&&k.style.backgroundImage&&(y.style.backgroundImage=k.style.backgroundImage,console.log(`为step-num-tag ${h+1} 设置备用背景图片`))}console.log(`step-num-tag ${h+1} 强制样式设置完成，背景图片:`,y.style.backgroundImage?"YES":"NO")});const n=i.querySelectorAll("img");console.log(`找到 ${n.length} 个img元素需要强制设置样式`),n.forEach((y,h)=>{y.style.display="block",y.style.visibility="visible",y.style.opacity="1",y.style.maxWidth="100%",y.style.height="auto",y.src&&y.src.includes("yqzx-1300870289.cos.ap-nanjing.myqcloud.com")&&(y.crossOrigin="anonymous",y.referrerPolicy="no-referrer"),console.log(`img ${h+1} 强制样式设置完成，src:`,y.src?"YES":"NO")});const d=i.querySelectorAll('[class*="recommend-icon"] img, .recommend-icon img');console.log(`找到 ${d.length} 个recommend-icon图片需要强制设置样式`),d.forEach((y,h)=>{y.style.width="27px",y.style.height="21px",y.style.display="inline-block",y.style.visibility="visible",y.style.opacity="1",console.log(`recommend-icon图片 ${h+1} 强制样式设置完成`)}),i.querySelectorAll("*").forEach(y=>{y.style&&y.style.display==="none"&&(y.style.display="block"),y.style&&y.style.visibility==="hidden"&&(y.style.visibility="visible"),y.style&&y.style.opacity==="0"&&(y.style.opacity="1")}),console.log("内联样式强制设置完成")},Ue=()=>{if(!s.undergraduateSchool){$.warning("请先选择本科院校");return}(!ke.value||D.value.length===0)&&(le.value=1,Ve(""))},o=()=>{ie.value&&!w.value&&(le.value+=1,Ve(fe.value))},Te=i=>{i&&console.log("本科专业下拉框打开")},le=_(1),ie=_(!1),ke=_(!1),fe=_("");return(i,t)=>{const n=qt,d=ps,p=us,y=Nt,h=Yt("Loading"),k=ms,x=Yt("Close"),U=gs,q=fs,B=ys,ue=os;return c(),g(te,null,[e("div",oi,[e("div",ai,[e("div",null,[t[39]||(t[39]=e("span",{class:"credits-text"},"已生成报告数：",-1)),e("span",ii,j(Se(ce).generated_report_times||0),1),t[40]||(t[40]=e("span",{class:"credits-count"},"人次",-1))]),e("div",{class:"button-group"},[e("div",{class:"action-btn",onClick:r},"预览报告"),e("div",{class:"action-btn",onClick:l},"导出报告"),t[41]||(t[41]=e("div",{class:"action-btn"},"发送",-1))])]),e("div",ni,[e("div",ri,[e("div",di,[t[57]||(t[57]=e("div",{class:"step-header"},[e("div",{class:"step-title"},"第一部分：个人基础信息")],-1)),e("div",ci,[t[56]||(t[56]=e("div",{class:"step-num-tag"},[e("span",null,"01"),e("div",{class:"tag-text"},"个人基础信息")],-1)),e("div",ui,[e("div",vi,[t[42]||(t[42]=e("div",{class:"item-label"},"学员姓名",-1)),m(n,{modelValue:s.name,"onUpdate:modelValue":t[0]||(t[0]=u=>s.name=u),placeholder:"请输入学员姓名"},null,8,["modelValue"])]),e("div",mi,[t[43]||(t[43]=e("div",{class:"item-label"},"性别",-1)),m(p,{modelValue:s.sex,"onUpdate:modelValue":t[1]||(t[1]=u=>s.sex=u),placeholder:"请选择性别",class:"full-width"},{default:M(()=>[m(d,{label:"男",value:"1"}),m(d,{label:"女",value:"2"}),m(d,{label:"其他",value:"3"})]),_:1},8,["modelValue"])]),e("div",gi,[t[44]||(t[44]=e("div",{class:"item-label"},"本科院校",-1)),m(p,{modelValue:s.undergraduateSchool,"onUpdate:modelValue":t[2]||(t[2]=u=>s.undergraduateSchool=u),placeholder:"输入关键字搜索院校",filterable:"",remote:"","reserve-keyword":"","remote-method":Be,loading:se.value,class:"full-width",onChange:Ye},{default:M(()=>[(c(!0),g(te,null,ve(Q.value,u=>(c(),ae(d,{key:u.value,label:u.label,value:u.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),e("div",pi,[t[47]||(t[47]=e("div",{class:"item-label"},"本科专业",-1)),m(p,{modelValue:s.undergraduateMajor,"onUpdate:modelValue":t[3]||(t[3]=u=>s.undergraduateMajor=u),placeholder:"输入关键字搜索专业",filterable:"",remote:"","reserve-keyword":"","remote-method":Ve,loading:w.value,class:"full-width",disabled:!s.undergraduateSchool,onFocus:Ue,"popper-class":"major-select-dropdown",onVisibleChange:Te},{default:M(()=>[(c(!0),g(te,null,ve(D.value,u=>(c(),ae(d,{key:u.value,label:u.label,value:u.value},null,8,["label","value"]))),128)),ie.value&&!w.value?(c(),g("div",{key:0,class:"load-more-option",onClick:o},[m(y,{style:{color:"#1bb394","padding-left":"20px"},type:"text",size:"small"},{default:M(()=>t[45]||(t[45]=[L("点击加载更多...")])),_:1})])):A("",!0),w.value&&le.value>1?(c(),g("div",yi,[m(k,null,{default:M(()=>[m(h)]),_:1}),t[46]||(t[46]=L(" 加载中... "))])):A("",!0)]),_:1},8,["modelValue","loading","disabled"])]),e("div",fi,[t[48]||(t[48]=e("div",{class:"item-label"},"学科门类",-1)),m(p,{modelValue:s.disciplineCategory,"onUpdate:modelValue":t[4]||(t[4]=u=>s.disciplineCategory=u),placeholder:"请选择学科门类",class:"full-width",onChange:Ge},{default:M(()=>[(c(!0),g(te,null,ve(oe.value,u=>(c(),ae(d,{key:u.id,label:u.label,value:u.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),e("div",_i,[t[49]||(t[49]=e("div",{class:"item-label"},"一级学科",-1)),m(p,{modelValue:s.firstLevelDiscipline,"onUpdate:modelValue":t[5]||(t[5]=u=>s.firstLevelDiscipline=u),placeholder:"请选择一级学科",class:"full-width",disabled:!s.disciplineCategory,onChange:Je},{default:M(()=>[(c(!0),g(te,null,ve(pe.value,u=>(c(),ae(d,{key:u.id,label:u.label,value:u.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),e("div",hi,[t[50]||(t[50]=e("div",{class:"item-label"},"目标专业",-1)),m(p,{modelValue:s.targetMajor,"onUpdate:modelValue":t[6]||(t[6]=u=>s.targetMajor=u),placeholder:"请选择目标专业",class:"full-width",disabled:!s.firstLevelDiscipline,onChange:Ke,multiple:""},{default:M(()=>[(c(!0),g(te,null,ve(ge.value,u=>(c(),ae(d,{key:u.id,label:u.label,value:u.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),e("div",bi,[t[51]||(t[51]=e("div",{class:"item-label"},"专业代码",-1)),m(n,{modelValue:s.majorCode,"onUpdate:modelValue":t[7]||(t[7]=u=>s.majorCode=u),placeholder:"请输入专业代码"},null,8,["modelValue"])]),e("div",wi,[t[52]||(t[52]=e("div",{class:"item-label"},"联系方式",-1)),m(n,{modelValue:s.phone,"onUpdate:modelValue":t[8]||(t[8]=u=>s.phone=u),placeholder:"请输入联系方式"},null,8,["modelValue"])]),e("div",ki,[t[53]||(t[53]=e("div",{class:"item-label"},"考研年份",-1)),m(p,{modelValue:s.examYear,"onUpdate:modelValue":t[9]||(t[9]=u=>s.examYear=u),placeholder:"请选择考研年份",class:"full-width"},{default:M(()=>[m(d,{label:"2027",value:"2027"}),m(d,{label:"2026",value:"2026"})]),_:1},8,["modelValue"])]),e("div",Si,[t[54]||(t[54]=e("div",{class:"item-label"},"跨专业",-1)),m(p,{modelValue:s.isMultiDisciplinary,"onUpdate:modelValue":t[10]||(t[10]=u=>s.isMultiDisciplinary=u),placeholder:"请选择跨专业",class:"full-width"},{default:M(()=>[m(d,{label:"是",value:"1"}),m(d,{label:"否",value:"2"})]),_:1},8,["modelValue"])]),e("div",$i,[t[55]||(t[55]=e("div",{class:"item-label"},"培养方式",-1)),m(p,{modelValue:s.educationalStyle,"onUpdate:modelValue":t[11]||(t[11]=u=>s.educationalStyle=u),placeholder:"请选择培养方式",class:"full-width"},{default:M(()=>[m(d,{label:"全日制",value:"0"}),m(d,{label:"非全日制",value:"1"})]),_:1},8,["modelValue"])])])])]),e("div",Ci,[e("div",Mi,[e("div",xi,[t[59]||(t[59]=e("div",{class:"step-num-tag"},[e("span",null,"02"),e("div",{class:"tag-text"},"本科成绩情况")],-1)),e("div",Vi,[m(y,{type:"primary",class:"action-button",onClick:R},{default:M(()=>t[58]||(t[58]=[L("创建")])),_:1})])]),e("div",ji,[e("div",Ti,[(c(!0),g(te,null,ve(Se(X),u=>(c(),g("div",{class:"score-item",key:u.id,draggable:!0,onDragstart:J=>b(J,u),onDragover:t[12]||(t[12]=vs(J=>Ne(J),["prevent"])),onDrop:J=>qe(J,u),onDragend:Ie},[e("div",Ai,j(u.title),1),e("div",Di,[m(n,{modelValue:u.score,"onUpdate:modelValue":J=>u.score=J,placeholder:"140"},null,8,["modelValue","onUpdate:modelValue"])]),m(k,{onClick:J=>He(u.id),class:"score-close",color:"#1BB394",size:"14"},{default:M(()=>[m(x)]),_:2},1032,["onClick"])],40,Pi))),128))])])])]),e("div",zi,[e("div",Li,[t[66]||(t[66]=e("div",{class:"step-num-tag"},[e("span",null,"03"),e("div",{class:"tag-text"},"英语基础")],-1)),e("div",Ei,[e("div",Ui,[e("div",Ri,[t[60]||(t[60]=e("div",{class:"english-label"},"高考英语成绩",-1)),m(n,{modelValue:s.englishScore,"onUpdate:modelValue":t[13]||(t[13]=u=>s.englishScore=u),placeholder:"请输入高考英语成绩",class:"full-width"},null,8,["modelValue"])]),e("div",Fi,[t[61]||(t[61]=e("div",{class:"english-label"},"大学四级",-1)),m(n,{modelValue:s.cet4,"onUpdate:modelValue":t[14]||(t[14]=u=>s.cet4=u),placeholder:"请输入大学四级成绩",class:"full-width"},null,8,["modelValue"])]),e("div",Ni,[t[62]||(t[62]=e("div",{class:"english-label"},"大学六级",-1)),m(n,{modelValue:s.cet6,"onUpdate:modelValue":t[15]||(t[15]=u=>s.cet6=u),placeholder:"请输入大学六级成绩",class:"full-width"},null,8,["modelValue"])]),e("div",qi,[t[63]||(t[63]=e("div",{class:"english-label"},"托福",-1)),m(n,{modelValue:s.tofelScore,"onUpdate:modelValue":t[16]||(t[16]=u=>s.tofelScore=u),placeholder:"请输入托福成绩",class:"full-width"},null,8,["modelValue"])])]),e("div",Oi,[e("div",Bi,[t[64]||(t[64]=e("div",{class:"english-label"},"雅思",-1)),m(n,{modelValue:s.ieltsScore,"onUpdate:modelValue":t[17]||(t[17]=u=>s.ieltsScore=u),placeholder:"请输入雅思成绩",class:"full-width"},null,8,["modelValue"])]),e("div",Hi,[t[65]||(t[65]=e("div",{class:"english-label"},"英语能力",-1)),m(p,{modelValue:s.englishLevel,"onUpdate:modelValue":t[18]||(t[18]=u=>s.englishLevel=u),placeholder:"请选择英语能力",class:"full-width"},{default:M(()=>[m(d,{label:"一般",value:"一般"}),m(d,{label:"良好",value:"良好"}),m(d,{label:"优秀",value:"优秀"})]),_:1},8,["modelValue"])])])])])])]),e("div",Wi,[e("div",Yi,[t[72]||(t[72]=e("div",{class:"step-num-tag"},[e("span",null,"04"),e("div",{class:"tag-text"},"目标院校梯度")],-1)),e("div",Gi,[e("div",Ji,[t[67]||(t[67]=e("div",{class:"item-label"},"地区倾向",-1)),m(p,{modelValue:s.region,"onUpdate:modelValue":t[19]||(t[19]=u=>s.region=u),placeholder:"请选择地区倾向",multiple:"",class:"full-width"},{default:M(()=>[m(d,{label:"A区",value:"A区"}),m(d,{label:"B区",value:"B区"})]),_:1},8,["modelValue"])]),e("div",Ki,[t[68]||(t[68]=e("div",{class:"item-label"},"省份选择",-1)),m(p,{modelValue:s.intendedSchools,"onUpdate:modelValue":t[20]||(t[20]=u=>s.intendedSchools=u),placeholder:"请选择省份",multiple:"",class:"full-width"},{default:M(()=>[(c(!0),g(te,null,ve(Xe.value,u=>(c(),ae(d,{key:u.value,label:u.label,value:u.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),e("div",Xi,[t[69]||(t[69]=e("div",{class:"item-label"},"梦校",-1)),m(p,{modelValue:s.targetSchool,"onUpdate:modelValue":t[21]||(t[21]=u=>s.targetSchool=u),filterable:"",remote:"","reserve-keyword":"",placeholder:"输入关键字搜索院校","remote-method":We,loading:he.value,class:"full-width",onChange:t[22]||(t[22]=u=>{if(u){const J=re.value.find(K=>K.value===u);J&&(s.targetSchoolName=J.label)}else s.targetSchoolName=""})},{default:M(()=>[(c(!0),g(te,null,ve(re.value,u=>(c(),ae(d,{key:u.value,label:u.label,value:u.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),e("div",Qi,[t[70]||(t[70]=e("div",{class:"item-label"},"院校层次",-1)),m(p,{modelValue:s.schoolLevel,"onUpdate:modelValue":t[23]||(t[23]=u=>s.schoolLevel=u),multiple:"",placeholder:"请选择院校层次",class:"full-width"},{default:M(()=>[m(d,{label:"985",value:"985"}),m(d,{label:"211",value:"211"}),m(d,{label:"双一流",value:"双一流"}),m(d,{label:"双非",value:"双非"})]),_:1},8,["modelValue"])]),e("div",Zi,[t[71]||(t[71]=e("div",{class:"item-label"},"专业课指定参考书",-1)),m(n,{modelValue:s.referenceBooks,"onUpdate:modelValue":t[24]||(t[24]=u=>s.referenceBooks=u),placeholder:"请输入专业课指定参考书"},null,8,["modelValue"])])])])]),e("div",Ii,[e("div",en,[t[76]||(t[76]=e("div",{class:"step-num-tag"},[e("span",null,"05"),e("div",{class:"tag-text"},"考研成绩预估")],-1)),e("div",tn,[t[73]||(t[73]=Ft('<div class="table-header" data-v-2841f64e><div class="th-cell cell-color" data-v-2841f64e>政治</div><div class="th-cell cell-color" data-v-2841f64e>英语</div><div class="th-cell cell-color" data-v-2841f64e>业务课一</div><div class="th-cell cell-color" data-v-2841f64e>业务课二</div><div class="th-cell cell-color" data-v-2841f64e>总分</div></div>',1)),e("div",sn,[e("div",ln,[m(n,{modelValue:s.politics,"onUpdate:modelValue":t[25]||(t[25]=u=>s.politics=u),class:"table-input",onInput:st},null,8,["modelValue"])]),e("div",on,[m(n,{modelValue:s.englishS,"onUpdate:modelValue":t[26]||(t[26]=u=>s.englishS=u),class:"table-input",onInput:st},null,8,["modelValue"])]),e("div",an,[m(n,{modelValue:s.englishType,"onUpdate:modelValue":t[27]||(t[27]=u=>s.englishType=u),class:"table-input",onInput:st},null,8,["modelValue"])]),e("div",nn,[m(n,{modelValue:s.mathType,"onUpdate:modelValue":t[28]||(t[28]=u=>s.mathType=u),class:"table-input",onInput:st},null,8,["modelValue"])]),e("div",rn,[m(n,{modelValue:s.totalScore,"onUpdate:modelValue":t[29]||(t[29]=u=>s.totalScore=u),class:"table-input",readonly:""},null,8,["modelValue"])])])]),e("div",dn,[t[74]||(t[74]=e("div",{class:"demands-label"},"个性化需求",-1)),m(n,{modelValue:s.personalNeeds,"onUpdate:modelValue":t[30]||(t[30]=u=>s.personalNeeds=u),type:"textarea",rows:1,class:"demands-input"},null,8,["modelValue"])]),e("div",cn,[t[75]||(t[75]=e("div",{class:"advice-label"},"薄弱模块",-1)),m(n,{modelValue:s.weakModules,"onUpdate:modelValue":t[31]||(t[31]=u=>s.weakModules=u),type:"textarea",rows:1,class:"advice-input"},null,8,["modelValue"])])])]),Se(Ce)?(c(),g("div",un,[Ae(e("div",{class:"ai-icon-wrapper",onMouseenter:Ct,onMouseleave:Mt,onClick:xt},[e("div",{class:dt(["ai-icon-layer outer",{"animate-outer":I.value,"hover-effect":et.value}])},null,2),e("div",{class:dt(["ai-icon-layer middle",{"animate-middle":I.value}])},null,2),e("div",{class:dt(["ai-icon-layer inner",{"animate-inner":I.value}])},null,2)],544),[[yt,!Se(xe)]])])):A("",!0),de.value?(c(),g("div",vn,[e("div",mn,[e("div",gn,[e("div",{class:dt(["ai-icon-layer outer",{"animate-outer":!0}])}),e("div",{class:dt(["ai-icon-layer middle",{"animate-middle":!0}])}),e("div",{class:dt(["ai-icon-layer inner",{"animate-inner":!0}])})])])])):A("",!0)]),m($s,{ref_key:"majorAnalysisRef",ref:$e,"national-line-data":G.value,visible:Z.value},null,8,["national-line-data","visible"]),m(Ho,{ref_key:"reportContentElement",ref:W},null,512),m(li,{ref_key:"weakModuleAnalysisRef",ref:it},null,512)]),m(U,{modelValue:ze.value,"onUpdate:modelValue":t[33]||(t[33]=u=>ze.value=u),title:"报告预览",width:"90%",top:"5vh","close-on-click-modal":!1,class:"preview-dialog"},{footer:M(()=>[e("div",_n,[m(y,{onClick:t[32]||(t[32]=u=>ze.value=!1)},{default:M(()=>t[78]||(t[78]=[L("关闭")])),_:1}),m(y,{type:"primary",onClick:a,loading:V.value},{default:M(()=>[L(j(V.value?"生成中...":"生成PDF"),1)]),_:1},8,["loading"])])]),default:M(()=>[e("div",{class:"preview-container",ref_key:"previewContainer",ref:v},[we.value?Ae((c(),g("div",pn,null,512)),[[ue,we.value]]):C.value?(c(),g("div",{key:1,innerHTML:C.value,class:"preview-content"},null,8,yn)):(c(),g("div",fn,t[77]||(t[77]=[e("p",null,"暂无预览内容",-1)])))],512)]),_:1},8,["modelValue"]),m(U,{title:"添加成绩项",modelValue:T.value,"onUpdate:modelValue":t[37]||(t[37]=u=>T.value=u),width:"30%","close-on-click-modal":!1},{footer:M(()=>[e("span",hn,[m(y,{onClick:t[36]||(t[36]=u=>T.value=!1)},{default:M(()=>t[79]||(t[79]=[L("取消")])),_:1}),m(y,{type:"primary",style:{"background-color":"#1bb394"},onClick:Oe},{default:M(()=>t[80]||(t[80]=[L("确认")])),_:1})])]),default:M(()=>[m(B,{model:Y,"label-width":"80px"},{default:M(()=>[m(q,{label:"科目名称"},{default:M(()=>[m(n,{modelValue:Y.title,"onUpdate:modelValue":t[34]||(t[34]=u=>Y.title=u),placeholder:"请输入科目名称"},null,8,["modelValue"])]),_:1}),m(q,{label:"成绩"},{default:M(()=>[m(n,{modelValue:Y.score,"onUpdate:modelValue":t[35]||(t[35]=u=>Y.score=u),placeholder:"请输入成绩"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),m(U,{title:"预览报告",modelValue:ye.value,"onUpdate:modelValue":t[38]||(t[38]=u=>ye.value=u),width:"860px","close-on-click-modal":!1},{default:M(()=>[m(Cs,{ref_key:"previewPdfRef",ref:wt},null,512)]),_:1},8,["modelValue"])],64)}}},zn=Ot(bn,[["__scopeId","data-v-2841f64e"]]);export{zn as default};
