import{_ as Be}from"./_plugin-vue_export-helper-BCEt7Ct6.js";/* empty css                   *//* empty css                     *//* empty css                  *//* empty css                        *//* empty css                 */import{g as Re,a as Qe,b as Je}from"./student-Db39rDv4.js";/* empty css                        */import{r as c,x as re,y as be,u as we,z as Me,A as Le,B as Ve,a as Te,c as E,o as h,e as r,w as s,C as ke,D as xe,f as Ne,G as ne,H as ye,b as n,I as je,J as Ke,K as Ye,L as Xe,l as L,M as He,q as ie,N as V,O as Ge,t as y,P as Ue,j as Fe,Q as Ze,F as qe,s as ze,R as Pe,E as We,k as j,S as _e}from"./index-Cu_gl4pu.js";import{S as $e}from"./TeacherSelector.vue_vue_type_style_index_0_scoped_0ca4774d_lang-CADRJ9Cy.js";import{s as et,a as tt,b as ot}from"./school-BRpLdP4V.js";import{g as at}from"./tag-IZtnXBn1.js";import"./province-Do8ZIeJL.js";const lt="/assets/left-slide-logo-BpcCFMkD.png",st="/assets/logo-BqQHnRN0.png",nt="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEQAAABECAYAAAA4E5OyAAAAAXNSR0IArs4c6QAABntJREFUeF7tmntQVFUcx7/n7pNlAQEJFMQXKiyPcvgjG2EVk8bxVTlmU+aKmTmOjdbkjDM6mVMzpVlOppYzpQJajThTWjmTL2QhRcd0wNzlpSJINAgL8lB2Ye89da9CgrB6L7vr4tz7D+zu75zf7/c539/v7Ll3CeSrBwEi8+hJQAbSSxEyEBmI6yYhK0RWiKwQURupXDJyycglI5eMKAJyyXi6ZChIXO730ZTpGK1gSCAlxKuNmiNgQUkrVGxV6aSMKhBCH5tCRp3aO8RfpfgELJ0NQkIp4EcArwKhoBwB7ACxUYLjTqdybXna6w1SoUgOPsG8ZwRHFHsJyPNSnXtiHKUooErFopLJC6ukzC8JSOy5rFDGgUMEJEWKU0+PocDFdrVjZuWkZXVifUkCEmfOepUhZB8AlViHXrGnYFmCFaVG07di/UkCEm/O/g0Es8Q686Y9BT1hNS5OF+tTEhBDfnY9AYaKdeZde9poMS4OFetTIpAsloAwYp15054CnNVoUoj1KQlIfH72gPZ6V0GuGvkMjCGRLvMYpQtEY4cdJ2zV+OzahX5tLUaT6PxED+C9exLIpxMmY85TYx5pYXMbbmBVSZ4MpIvAyYZqrC4xP9lAAhRqaBUPlr6GUWJLbAoS9KHoOh3sqr6EHVXFTzaQvrLj6/orw1SkhY7o8fGmq+exv7b0yQDy3JAIxOpD8ENtGRwc67JnvDE8FmvGJENJGLQ5O6FX3v0+uL7sNA7fvDa4gfCr/faIRCyPThQSPFJfiQ3lheikXJ+JRWsDcHDiLOiUKrSznTjeUI0Xw8cKtquseci13RjcQAz6EOxKeB4hKq2QSIvTgRWXc1Hc+uABdaQ2ELuT0hGh0Qm2O6uKEa7RYX7EOOH1kkvHcL65/+OKz2+7kVo99iSmY7jGv7sxUkphabNhUfHRHirh1fNlnBFT7/WNfFsN3i0xY3NsKtKHRgtA5l88gtLbjYNTIbwydhrSEHZvtYtabsJfocI4/2AhocN1V/HxlXOwcywUIMiIMmD1qIlgCIGl1Ya3/jqBVrYDWUkvIDkoXBgz4/zPqLG3DT4gkRo9vkucjiitXlBGfmMN1pWdgb9Sif1JMwRIHRyLj66cw6G6q5gXHoMPYp6FimHAfyVeVPQ7ilrrhcR/SZ6LMbog4f/JhQfQ7OwYXEDi9SHYEZ+GMPXdPnC6qRbvl+Sjje0UXqcGR+Lz2FT4K1Vo6XRgfXkh1o5NRpQ2AHbWic2Vf+LgPxXdSc8OGy30kSClGtuqisDS/k8RPtdD+B3im4Rp4P/yysiz3cCHFWdh67R3J6gkBCujn8ay6EThvXbWCa1CCb63ZNZYsfX6xX4V8LAPfAoI3zN2J6QjQKUW4i5o/BurrXno6GN75XvG9vipSAmOFHoGf1XcvoU3Lx1Dk9PxsLx9v2RidEHYZpiKkX6BQrD8DrGhohAN9ymjdxYRap2gJr7JVt5pxvLLJ1HruC0ZBj/QJxTCgODgxJkY7x8slIm5sQbvWc19KqN3tinBw7FpQgrWl52BualmQDB8BggfCL/aW2JT0fLfDrDxylnUd7Q/cnJJAUNhbbPB6aJZPupkPqGQrmADlWoByOO8vAekINsBirvd0ncv1mI0KcWGJ+mOmaEgu4ZQuL7PJzYSN9tToMFqNIWJnVYikH0HCKULxDrzqj3lfrVMyZgr1qckIHH5mbMYwvzkw2XjZAlnKk3N+NErQGLO7g/UOGgmCH1ZrENv2FPgaBtRvFadurBJrD9JCuGdROVs9QuKCM0ByGyxTj1qT3Fc29L+0oU5y+9I8SMZCO/McCpHTxXtKwjFPEIwnoIMIYBXH2DxD6QI0EwprQBhDt9hnV9fT1tySwoMfsyAgHQ5nfDH7gCGU+vR6VBD6yc1Fmnj7O3gNIQ/OreWpSxtlTbJ/6PcAmSgQfjSeBlIr9XwCJA4895xhCojOIZ6pJ8QylGOZevKpi0tB4Sbam673A4kriBrOgHJBKXDPPULAf53ZQCpI04ssUwzHXUbDXc11fsDijdn7wDBSncG6WKufRajyeROX25XiCE/6x0Cst2dQfY3F6VYY51i+sKdvtwOBDk5irhh9o2E4hVQqnFnsN1zEdJBwR0qSdWtA1ng+jmoyADcD0RkAL5mLgPxxrbra6suJh5ZIbJCXOtFVoisEFkhYnqqe+6HiPLo48ZyD5F7iNxDRBWpXDJyybgWzL88gTxjVaYN7gAAAABJRU5ErkJggg==",it="data:image/png;base64,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",rt="data:image/png;base64,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",ct="data:image/png;base64,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",ut="data:image/png;base64,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";function dt(te={}){const{throttleDelay:O=16,clickProtectionDelay:T=300,scrollThreshold:C=60,sectionPrefix:K="section-",debug:B=!1}=te,m=c(0),k=c(0),i=c(null),Q=c(!1),q=(o,d)=>{let g,f;return function(...J){f?(clearTimeout(g),g=setTimeout(()=>{Date.now()-f>=d&&(o.apply(this,J),f=Date.now())},d-(Date.now()-f))):(o.apply(this,J),f=Date.now())}},A=(...o)=>{B&&console.log("[ScrollNavigation]",...o)},x=q(()=>{if(!i.value)return;const o=[];let d=0;for(;;){const p=document.getElementById(`${K}${d}`);if(p)o.push({index:d,element:p}),d++;else break}if(o.length===0){A("未找到任何section元素");return}if(Date.now()-k.value<T){A("刚刚点击了导航项，保持activeSection不变:",m.value);return}const f=i.value.scrollTop,J=i.value.getBoundingClientRect().top;let b=0,X=1/0;if(o.forEach(p=>{const H=p.element.getBoundingClientRect().top,N=Math.abs(H-J);H<=J+C&&N<X&&(X=N,b=p.index)}),X===1/0)for(let p=o.length-1;p>=0;p--){const S=o[p];if(f>=S.element.offsetTop-C){b=S.index;break}}m.value!==b&&(A("更新activeSection为:",b,"从:",m.value),m.value=b)},O),Y=o=>{k.value=Date.now(),A("记录点击时间:",k.value),m.value=o,A("设置activeSection为:",o),setTimeout(()=>{const d=`${K}${o}`,g=document.getElementById(d);if(g&&i.value)i.value.scrollTop=g.offsetTop-20,A("滚动到section:",d,"位置:",g.offsetTop-20);else if(i.value){const f=document.querySelectorAll(".form-section");f[o]?(i.value.scrollTop=f[o].offsetTop-20,A("滚动到sections[index]:",o,"位置:",f[o].offsetTop-20)):A("未找到对应的section")}m.value=o,A("再次确认activeSection为:",o)},50)},G=o=>{i.value=o,i.value&&!Q.value&&(i.value.addEventListener("scroll",x),Q.value=!0,m.value=0,A("初始化滚动监听，重置activeSection为0"),setTimeout(()=>{if(i.value){i.value.scrollTop=0,m.value=0,A("延迟后再次确认activeSection为0");let d=0;for(;;){const g=document.getElementById(`${K}${d}`);if(g)A(`找到${K}${d}，位置:`,g.offsetTop),d++;else break}}},300))},D=()=>{i.value&&Q.value&&(i.value.removeEventListener("scroll",x),Q.value=!1,A("销毁滚动监听"))};return{activeSection:m,formContent:i,scrollToSection:Y,initScrollListener:G,destroyScrollListener:D,watchDialogVisible:o=>{re(o,d=>{d?be(()=>{const g=i.value||document.querySelector(".dialog-content");g&&G(g)}):D()})},setFormContentRef:o=>{i.value=o},resetActiveSection:()=>{m.value=0}}}const At={class:"layout-container"},gt={class:"logo-container"},mt={key:0,src:lt,alt:"Logo",class:"sidebar-logo"},pt={key:1,src:st,alt:"Logo",class:"sidebar-logo"},ft={key:0,class:"menu-icon",src:nt,alt:"Logo"},vt={key:1,class:"menu-icon",src:it,alt:"Logo"},ht={key:0,src:rt,class:"menu-icon"},St={key:1,src:ct,class:"menu-icon"},Et={class:"header-section"},It={class:"header-left"},Dt={key:0,class:"search-container"},Ot={class:"search-box"},Ct={class:"student-suggestion-item"},Bt={class:"student-name"},Rt={class:"student-info"},Qt={class:"action-buttons"},Jt={class:"user-info"},bt=["src"],wt={key:1,src:ut,alt:"用户头像",class:"user-avatar"},Mt={class:"welcome-text"},Lt={class:"dialog-container"},Vt={class:"dialog-sidebar"},Tt=["onClick"],kt={class:"sidebar-item-number"},xt={class:"sidebar-item-text"},Nt={class:"dialog-footer"},yt={__name:"index",setup(te){const O=Ve(),T=Ne(),C=we(),{userInfo:K}=Me(C),B=c(""),m=c(!1),k=c(null),i={},Q=(e,t)=>{if(typeof t!="function"){console.error("callback不是函数");return}if(!e){t([]);return}if(console.log("开始搜索学员:",e),i[e]){console.log("使用缓存结果:",i[e]),t(i[e]);return}m.value=!0,clearTimeout(k.value),k.value=setTimeout(()=>{Je({name:e,page:1,limit:10}).then(a=>{if(console.log("搜索学员结果:",a),console.log("搜索学员结果数据类型:",typeof a.data,Array.isArray(a.data)),console.log("搜索学员结果数据:",JSON.stringify(a.data,null,2)),a.code===0&&a.data){const l=Array.isArray(a.data)?a.data:a.data.list||[];console.log("处理后的学员列表:",l);const v=l.map(R=>({value:R.name,id:R.id,name:R.name,undergraduateSchoolName:R.undergraduateSchoolName||"未知院校",undergraduateMajorName:R.undergraduateMajorName||"未知专业",targetMajorName:R.targetMajorName}));console.log("处理后的搜索结果:",v),i[e]=v,t(v)}else console.log("API返回错误或没有数据，返回空结果"),t([])}).catch(a=>{console.error("搜索学员失败",a),t([])}).finally(()=>{m.value=!1})},300)},q=(e,t)=>{if(console.log("查询搜索建议:",e),!e){t([]);return}Q(e,t)},A=()=>{console.log("搜索按钮点击:",B.value),B.value&&Q(B.value,e=>{console.log("搜索结果:",e),e.length===1&&z(e[0])})},z=e=>{console.log("选择学员:",e),e&&e.id&&(O.path==="/report/generate"?(j.info("正在加载学员信息..."),Re(e.id).then(t=>{if(t.code===0&&t.data){localStorage.setItem("selectedStudent",JSON.stringify(t.data)),j.success("学员信息加载成功，正在填充表单...");const a=new CustomEvent("student-selected",{detail:t.data});window.dispatchEvent(a)}else j.error(t.msg||"获取学员信息失败"),T.push(`/student/detail/${e.id}`)}).catch(t=>{console.error("获取学员详情失败",t),j.error("获取学员信息失败，跳转到详情页"),T.push(`/student/detail/${e.id}`)})):T.push(`/student/detail/${e.id}`))},x=c(!1),Y=Le(()=>O.path),G=()=>{_e.confirm("确定退出登录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{C.logout(),T.push("/login")}).catch(()=>{})};re(()=>O.path,(e,t)=>{e!=="/report/generate"&&(B.value="")});const D=c(!1),P=c("add"),o=c(!1),d=c(null),g=["个人基础信息","本科成绩情况","英语基础","目标院校倾向","考研成绩预估"],{activeSection:f,formContent:J,scrollToSection:b,watchDialogVisible:X}=dt({debug:!1,throttleDelay:16,clickProtectionDelay:300,scrollThreshold:60});X(D);const p=c(!1),S=c([]),H=c(null),N={},U=c(!1),w=c([]),oe=c(null),W={},F=c(!1),M=c([]),ae=c(null),_={},$=c([]),u=Te({id:"",name:"",sex:"",phone:"",teacherPhone:"",undergraduateSchool:"",undergraduateMajor:"",disciplineCategory:null,firstLevelDiscipline:null,targetMajor:[],majorCode:"",isPostgraduate:!1,examYear:"",isMultiDisciplinary:"",mathScores:[{id:1,title:"高数(上)期末考试",score:""},{id:2,title:"高数(下)期末考试",score:""},{id:3,title:"积分论期末考试",score:""},{id:4,title:"线性代数期末考试",score:""}],specializedCourses:[{id:1,title:"专业课一",name:"",score:""},{id:2,title:"专业课二",name:"",score:""},{id:3,title:"专业课三",name:"",score:""}],englishScore:"",cetLevel:"",specialEnglishLevel:"",tofelIelts:"",hasEnglishTraining:"",targetRegion:"",targetProvinces:[],targetSchool:"",schoolLevel:"",politics:"",englishType:"",mathType:"",estimatedScores:[{id:1,title:"专业课一",name:"",score:""},{id:2,title:"专业课二",name:"",score:""},{id:3,title:"专业课三",name:"",score:""}],totalScore:"",personalNeeds:"",tags:[]}),ce={name:[{required:!0,message:"请输入学员姓名",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号码",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],undergraduateSchool:[{required:!0,message:"请选择本科院校",trigger:"change"}],undergraduateMajor:[{required:!0,message:"请选择本科专业",trigger:"change"}],targetMajor:[{required:!0,message:"请选择目标专业",trigger:"change"}]},ue=()=>{at().then(e=>{if(console.log("获取标签结果:",e),e.code===0&&e.data){const a=[...e.data.first||[],...e.data.second||[],...e.data.third||[]].map(l=>({value:l.id.toString(),label:l.name,level:l.level,parent_id:l.parent_id,class:de(l.level)}));$.value=a}}).catch(e=>{console.error("获取标签失败",e)})},de=e=>{if(e===1)return"tag-default";if(e===2){const t=["tag-orange","tag-purple","tag-green","tag-blue"];return t[Math.floor(Math.random()*t.length)]}else if(e===3){const t=["tag-orange","tag-purple","tag-green","tag-blue"];return t[Math.floor(Math.random()*t.length)]}return"tag-default"},Ae=()=>{P.value="add",Object.keys(u).forEach(e=>{e==="tags"||e==="targetProvinces"?u[e]=[]:e==="disciplineCategory"||e==="firstLevelDiscipline"?u[e]=null:e==="mathScores"?u[e]=[{id:1,title:"高数(上)期末考试",score:""},{id:2,title:"高数(下)期末考试",score:""},{id:3,title:"积分论期末考试",score:""},{id:4,title:"线性代数期末考试",score:""}]:e==="specializedCourses"?u[e]=[{id:1,title:"专业课一",name:"",score:""},{id:2,title:"专业课二",name:"",score:""},{id:3,title:"专业课三",name:"",score:""}]:e==="estimatedScores"?u[e]=[{id:1,title:"专业课一",name:"",score:""},{id:2,title:"专业课二",name:"",score:""},{id:3,title:"专业课三",name:"",score:""}]:u[e]=""}),S.value=[],w.value=[],M.value=[],$.value.length===0&&ue(),D.value=!0},ge=e=>{if(!e){S.value=[];return}if(p.value=!0,console.log("搜索本科院校:",e),N[e]){S.value=N[e],p.value=!1;return}clearTimeout(H.value),H.value=setTimeout(()=>{ot(e).then(t=>{if(console.log("搜索本科院校结果:",t),t.code===0&&t.data){const a=t.data.map(l=>({value:l.id.toString(),label:l.name}));S.value=a,N[e]=a}else S.value=[]}).catch(t=>{console.error("获取学校列表失败",t),S.value=[]}).finally(()=>{p.value=!1})},300)},le=e=>{if(!u.undergraduateSchool){j.warning("请先选择本科院校");return}U.value=!0,console.log("搜索本科专业:",u.undergraduateSchool,e);const t=`${u.undergraduateSchool}_${e}`;if(W[t]){w.value=W[t],U.value=!1;return}clearTimeout(oe.value),oe.value=setTimeout(()=>{tt(u.undergraduateSchool,e||"").then(a=>{if(console.log("搜索本科专业结果:",a),a.code===0&&a.data){const l=a.data.map(v=>({value:v.id.toString(),label:v.major_name||v.name}));w.value=l,W[t]=l}else w.value=[]}).catch(a=>{console.error("获取专业列表失败",a),w.value=[]}).finally(()=>{U.value=!1})},300)},me=e=>{if(!e){M.value=[];return}if(F.value=!0,console.log("搜索目标专业:",e),_[e]){M.value=_[e],F.value=!1;return}clearTimeout(ae.value),ae.value=setTimeout(()=>{et(e).then(t=>{if(console.log("搜索目标专业结果:",t),t.code===0&&t.data){const a=t.data.map(l=>{var v;return{value:((v=l.id)==null?void 0:v.toString())||l.name,label:l.name,code:l.code}});M.value=a,_[e]=a}else M.value=[]}).catch(t=>{console.error("获取目标专业列表失败",t),M.value=[]}).finally(()=>{F.value=!1})},300)},pe=()=>{u.undergraduateMajor="",w.value=[],u.undergraduateSchool&&le("")},fe=async()=>{if(d.value)try{await d.value.validate(),o.value=!0;const e={...u,mathScoresJson:JSON.stringify(u.mathScores),specializedCoursesJson:JSON.stringify(u.specializedCourses),estimatedScoresJson:JSON.stringify(u.estimatedScores),disciplineCategory:u.disciplineCategory,firstLevelDiscipline:u.firstLevelDiscipline};Qe(e).then(t=>{console.log("添加学员结果:",t),t.code===0&&(j.success("添加学员成功"),D.value=!1,O.path)}).catch(t=>{console.error("添加学员失败",t)}).finally(()=>{o.value=!1})}catch(e){console.error("表单验证失败",e),o.value=!1}};return(e,t)=>{const a=He,l=Xe,v=Ye,R=Ke,ve=je,he=ye,Se=ne("Search"),Ee=Ge,ee=Fe,Ie=ne("router-view"),De=Ze,se=ke,Oe=We,Ce=xe;return h(),E("div",At,[r(se,null,{default:s(()=>[r(he,{width:x.value?"64px":"220px",class:"aside"},{default:s(()=>[n("div",gt,[x.value?(h(),E("img",pt)):(h(),E("img",mt))]),r(ve,null,{default:s(()=>[r(R,{"default-active":Y.value,"text-color":"#333","active-text-color":"#16b788",router:!0,collapse:x.value,class:"custom-menu"},{default:s(()=>[r(v,{index:"1"},{title:s(()=>[r(a,null,{default:s(()=>[Y.value.includes("/report")?(h(),E("img",ft)):(h(),E("img",vt))]),_:1}),t[3]||(t[3]=n("span",{style:{"margin-left":"12px"}},"AI择校报告",-1))]),default:s(()=>[r(l,{index:"/report/generate",class:"custom-menu-item"},{default:s(()=>t[4]||(t[4]=[L("生成报告")])),_:1}),r(l,{index:"/report/list",class:"custom-menu-item"},{default:s(()=>t[5]||(t[5]=[L("报告列表")])),_:1})]),_:1}),r(v,{index:"2"},{title:s(()=>[Y.value.includes("/student")?(h(),E("img",ht)):(h(),E("img",St)),t[6]||(t[6]=n("span",{style:{"margin-left":"12px"}},"学员数据",-1))]),default:s(()=>[r(l,{index:"/student/list",class:"custom-menu-item"},{default:s(()=>t[7]||(t[7]=[L("学员列表")])),_:1}),r(l,{index:"/student/tags",class:"custom-menu-item"},{default:s(()=>t[8]||(t[8]=[L("标签管理")])),_:1})]),_:1})]),_:1},8,["default-active","collapse"])]),_:1})]),_:1},8,["width"]),r(se,{class:"right-container"},{default:s(()=>[n("div",Et,[n("div",It,[t[9]||(t[9]=n("div",{class:"logo"},[n("span",{class:"logo-text"},"AI择校报告")],-1)),V(O).path==="/report/generate"?(h(),E("div",Dt,[n("div",Ot,[r(Ee,{modelValue:B.value,"onUpdate:modelValue":t[0]||(t[0]=I=>B.value=I),"fetch-suggestions":q,placeholder:"请输入学员姓名",class:"search-input","trigger-on-focus":!0,onSelect:z,loading:m.value,"popper-class":"student-suggestions","value-key":"name",clearable:"",debounce:300},{suffix:s(()=>[r(a,{class:"search-icon",onClick:A},{default:s(()=>[r(Se)]),_:1})]),default:s(({item:I})=>[n("div",Ct,[n("div",Bt,y(I.name),1),n("div",Rt,[n("span",null,y(I.undergraduateSchoolName||"未知院校"),1),n("span",null,y(I.undergraduateMajorName||"未知专业"),1)])])]),_:1},8,["modelValue","loading"])])])):ie("",!0)]),n("div",Qt,[V(O).path==="/report/generate"?(h(),Ue(ee,{key:0,type:"primary",class:"create-btn",onClick:Ae},{default:s(()=>t[10]||(t[10]=[L("+ 添加学员")])),_:1})):ie("",!0),n("div",Jt,[V(C).userInfo.avatar?(h(),E("img",{key:0,src:V(C).userInfo.avatar,alt:"用户头像",class:"user-avatar"},null,8,bt)):(h(),E("img",wt)),n("span",Mt,"欢迎您 "+y(V(C).nickname)+"！",1),n("span",{class:"logout-text",onClick:G},"[退出登录]")])])]),r(De,{class:"main"},{default:s(()=>[r(Ie)]),_:1})]),_:1})]),_:1}),r(Ce,{modelValue:D.value,"onUpdate:modelValue":t[2]||(t[2]=I=>D.value=I),title:"添加学员",width:"90%",top:"5vh","close-on-click-modal":!1,class:"student-dialog"},{footer:s(()=>[n("span",Nt,[r(ee,{onClick:t[1]||(t[1]=I=>D.value=!1)},{default:s(()=>t[11]||(t[11]=[L("取消")])),_:1}),r(ee,{type:"primary",onClick:fe,loading:o.value,class:"action-button"},{default:s(()=>t[12]||(t[12]=[L("确定")])),_:1},8,["loading"])])]),default:s(()=>[n("div",Lt,[n("div",Vt,[(h(),E(qe,null,ze(g,(I,Z)=>n("div",{key:Z,class:Pe(["sidebar-item",{active:V(f)==Z}]),onClick:jt=>V(b)(Z)},[n("div",kt,y(Z+1),1),n("div",xt,y(I),1)],10,Tt)),64))]),n("div",{class:"dialog-content",ref_key:"formContent",ref:J},[r(Oe,{model:u,"label-width":"100px",rules:ce,ref_key:"studentFormRef",ref:d,class:"student-form"},{default:s(()=>[r($e,{"student-form":u,"dialog-type":P.value,"school-options":S.value,"major-options":w.value,"target-major-options":M.value,"tag-options":$.value,"school-search-loading":p.value,"major-search-loading":U.value,"target-major-search-loading":F.value,onSchoolChange:pe,onSearchSchool:ge,onSearchMajor:le,onSearchTargetMajor:me},null,8,["student-form","dialog-type","school-options","major-options","target-major-options","tag-options","school-search-loading","major-search-loading","target-major-search-loading"])]),_:1},8,["model"])],512)])]),_:1},8,["modelValue"])])}}},$t=Be(yt,[["__scopeId","data-v-cb084bec"]]);export{$t as default};
