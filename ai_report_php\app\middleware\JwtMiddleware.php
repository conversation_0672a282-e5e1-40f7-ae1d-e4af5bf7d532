<?php
namespace app\middleware;

use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use ReflectionClass;
use Webman\Http\Response;
use Webman\MiddlewareInterface;
use Webman\Http\Request;

class JwtMiddleware implements MiddlewareInterface
{
    public function process(Request $request, callable $handler): Response
    {

        $controller = new ReflectionClass($request->controller);
        $noNeedLogin = $controller->getDefaultProperties()['noNeedLogin'] ?? [];
         //不需要登陆
         if (in_array($request->action, $noNeedLogin) || $noNeedLogin == "*") {
            return $handler($request);
        }
        $token = $request->header('Authorization');
        if(empty($token)){
            $token = $request->get('token');
        }

        if (!$token) {
            return json(['code' => 401, 'msg' => '未授权访问']);
        }

        try {
            $token = str_replace('Bearer ', '', $token);
            $decoded = JWT::decode($token, new Key(config('app.jwt_key'), 'HS256'));
            $request->user = $decoded;
            return $handler($request);
        } catch (\Exception $e) {
            return json(['code' => 401, 'msg' => 'token无效或已过期']);
        }
    }
}