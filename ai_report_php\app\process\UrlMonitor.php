<?php
namespace app\process;

use Workerman\Worker;
use Workerman\Timer;
use support\Log;

class UrlMonitor
{    
    /**
     * 请求超时时间（秒）
     */
    private $timeout = 30;
    
    /**
     * 请求间隔时间（秒）
     * 3600 = 1小时
     */
    private $interval = 3600;
    
    /**
     * 进程启动时执行
     */
    public function onWorkerStart(Worker $worker)
    {        
        // 设置定时器，每小时执行一次
        $id = Timer::add($this->interval, function() {
            $res = $this->executeMonitoring();
        });
        
        // 启动时立即执行一次
        $this->executeMonitoring();
        
        // 记录进程启动日志
        Log::info('进程启动请求爬虫状态', [
            'pid' => $worker->id,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * 执行监控任务
     */
    private function executeMonitoring()
    {
       $res =  http_get(config('app.check_spider_login_url'));
       Log::info('获取爬虫登录状态', [
        'res' => $res
        ]);
        return $res;
    }

}
