import{_ as Ze}from"./_plugin-vue_export-helper-BCEt7Ct6.js";import{g as jt,b as ht,a as Ut}from"./school-BRpLdP4V.js";/* empty css                     *//* empty css                   *//* empty css                  */import{c as At,d as Et}from"./student-Db39rDv4.js";/* empty css                 */import{m as Vt,r as p,n as Ct,aa as Rt,p as xe,U as St,c as v,o as r,b as e,d as Mt,F as B,s as q,t as $,q as T,N as me,e as u,j as Qe,w as f,h as Ie,P as R,y as Pe,k as w,ab as Pt,l as x,A as xt,W as Ke,X as Re,a as $t,B as zt,Y as Dt,Z as Lt,_ as Ft,ac as wt,R as Xe,D as Nt,$ as Bt,v as qt,E as Ot,g as Yt}from"./index-Cu_gl4pu.js";import{v as Wt,d as Ht,w as Gt,f as Jt,u as Kt,i as Xt,j as Zt,a as Qt,k as It,x as el}from"./report-CU5jWwkq.js";import{p as tl}from"./province-Do8ZIeJL.js";import"./index-YsyrxmwF.js";import{_ as ke}from"./tag-Ch_qj8li.js";import{M as ll,P as sl}from"./PreviewPdf-BQ_I87f2.js";const al={class:"content-container"},ol={key:1},il={class:"step-section"},nl={class:"step-content"},dl={class:"school-table-container"},rl={class:"school-table-body"},ul={class:"body-cell"},cl={class:"body-cell school-name"},vl={class:"school-tags"},ml={key:0,class:"tag tag-985"},pl={key:1,class:"tag tag-211"},yl={key:2,class:"tag tag-double"},gl={class:"body-cell"},_l={class:"body-cell"},fl={class:"body-cell"},bl={class:"body-cell"},kl={class:"body-cell"},hl={class:"body-cell"},$l={class:"body-cell"},wl={class:"step-section"},Vl={class:"step-num-tag"},Cl={class:"school-detail-card"},Sl={class:"school-header"},Ml={class:"school-logo"},xl=["src"],Tl={class:"school-info"},jl={class:"school-title"},Ul={class:"school-location"},Al={class:"school-tags-row"},El={key:0,class:"tag tag-double"},Rl={key:1,class:"tag tag-985"},Pl={key:2,class:"tag tag-211"},zl={class:"school-detail-section"},Dl={class:"detail-item"},Ll={class:"item-content"},Fl={class:"content-header"},Nl={key:0,class:"action-buttons"},Bl={key:0},ql={key:1,class:"edit-container"},Ol={class:"edit-actions"},Yl={class:"detail-item"},Wl={class:"item-content"},Hl={class:"content-header"},Gl={key:0},Jl={key:1,class:"edit-container"},Kl={class:"edit-actions"},Xl={class:"detail-item"},Zl={class:"item-content"},Ql={class:"content-header"},Il={key:0},es={key:1,class:"edit-container"},ts={class:"edit-actions"},ls={class:"school-detail-section"},ss={class:"detail-item"},as={class:"item-content"},os={class:"content-header"},is={key:0},ns={key:1,class:"edit-container"},ds={class:"edit-actions"},rs={class:"detail-item"},us={class:"item-content"},cs={class:"content-header"},vs={key:0},ms={key:1,class:"edit-container"},ps={class:"edit-actions"},ys={key:0,class:"detail-item"},gs={class:"item-content"},_s={class:"content-header"},fs={key:0},bs={key:1,class:"edit-container"},ks={class:"edit-actions"},hs={class:"step-content-zs"},$s={key:0,class:"admission-title"},ws={class:"admission-table"},Vs={class:"admission-table"},Cs={class:"reexam-container"},Ss={class:"reexam-card"},Ms={class:"reexam-header"},xs={class:"reexam-content"},Ts={key:0},js={key:1,class:"edit-container"},Us={class:"edit-actions"},As={class:"admission-table"},Es={class:"reexam-container"},Rs={class:"reexam-card"},Ps={class:"reexam-header"},zs={class:"reexam-content"},Ds={key:0},Ls={key:1,class:"edit-container"},Fs={class:"edit-actions"},Ns={class:"reexam-card"},Bs={class:"reexam-header"},qs={class:"reexam-content"},Os={key:0},Ys={key:1,class:"edit-container"},Ws={class:"edit-actions"},Hs={class:"recommend-school-container"},Gs={class:"recommend-school-card"},Js={class:"recommend-school-header"},Ks={class:"recommend-school-content"},Xs=Vt({__name:"content_edit",emits:["updateOverlay"],setup(et,{expose:je,emit:Ue}){const P=p({subject_code:"",subject_name:"",years:[],a_total:[],a_single_100:[],a_single_over100:[],b_total:[],b_single_100:[],b_single_over100:[]});Ct(()=>{});const H=p(!1),se=()=>{console.log("显示content组件"),H.value=!0,Pe(()=>{console.log("nextTick后初始化图表")})},G=()=>{H.value=!1},O=()=>{H.value=!H.value,H.value&&Pe(()=>{})};let k=p({});const j=p({}),V=p({}),F=p({}),z=p({});p(!1);const pe=p(""),Me=p(!1),ye=p(180),te=p(1),re=p(0),Y=p(""),ue=[["基本信息读取中","基本信息分析中"],["专业数据获取中","专业数据分析中"],["院校信息匹配中","院校数据分析中"],["生成分析报告中","报告内容优化中"]],he=["数据获取完成","分析报告生成完成","正在渲染报告内容","即将为您展示结果"];let ce=null,s=null,ae=0;const Ae=()=>{Me.value=!0,ye.value=180,te.value=1,re.value=0,ae=0,Y.value=ue[0][0],ce=setInterval(()=>{if(ye.value>0){ye.value--;const a=Math.round((180-ye.value)/180*100);a>re.value&&(re.value=a)}else W()},1e3),s=setInterval(()=>{const a=te.value-1,l=ue[a];l&&ae<l.length-1&&(ae++,Y.value=l[ae])},15e3),setInterval(()=>{if(te.value<4){te.value++,ae=0;const a=te.value-1;ue[a]&&(Y.value=ue[a][0])}},45e3)},W=()=>{console.log("停止loading效果"),Me.value=!1,ce&&(ce=null),s&&(s=null),Pe(()=>{console.log("loading停止后检查是否需要初始化图表"),console.log("isVisible:",H.value),console.log("nationalLineData:",P.value),H.value&&P.value.years.length>0&&console.log("loading停止后初始化图表")})},ve=p({}),ge=p({}),_e=p({}),$e=p({}),oe=p({}),Q=p({}),J=p({});p({});const b=p({}),i=p({}),C=p({}),S=p({}),U=p({}),m=p({}),D=p({}),we=p({});p({});const ie=p({}),M=Ue,K=p(!0),N=async(a,l,d)=>{var Ee;if(!pe.value)throw new Error("报告ID不存在");const h=(Ee=k.value.recommend_list)==null?void 0:Ee.find(Fe=>Fe.school_id===a),L=(h==null?void 0:h.is_high_recommend)||0;await Ht({report_id:pe.value,school_id:parseInt(a),field_name:l,field_value:d,is_high_recommend:L})},I=()=>{M("updateOverlay",!1)},ee=()=>{console.log("显示完成状态动画"),ce&&(clearInterval(ce),ce=null),s&&(clearInterval(s),s=null),re.value=100,te.value=4;let a=0;Y.value=he[0],s=setInterval(()=>{a=(a+1)%he.length,Y.value=he[a]},800)},fe=async a=>{pe.value=a;try{const l=await Wt(a);k.value={...l.data,high_recommend_list:Array.isArray(l.data.high_recommend_list)?l.data.high_recommend_list:[l.data.high_recommend_list]},I()}catch{w.error("加载报告数据失败，请稍后再试。"),setTimeout(()=>{Pt.push("/report/list")},2e3)}},Ve=a=>{var d;j.value[a]=!0;const l=(d=k.value.recommend_list)==null?void 0:d.find(h=>h.school_id===a);l&&(F.value[a]=l.difficulty_analysis||"")},Ce=a=>{var d;V.value[a]=!0;const l=(d=k.value.recommend_list)==null?void 0:d.find(h=>h.school_id===a);l&&(z.value[a]=l.suggest||"")},X=async a=>{var l;try{const d=(l=k.value.recommend_list)==null?void 0:l.find(h=>h.school_id===a);d&&(d.difficulty_analysis=F.value[a]),await N(a,"competition_difficulty",F.value[a]),j.value[a]=!1,w.success("竞争难度分析保存成功")}catch(d){console.error("保存竞争难度分析失败:",d),w.error("保存失败，请重试")}},le=async a=>{var l;try{const d=(l=k.value.recommend_list)==null?void 0:l.find(h=>h.school_id===a);d&&(d.suggest=z.value[a]),await N(a,"suggestions",z.value[a]),V.value[a]=!1,w.success("备考建议保存成功")}catch(d){console.error("保存备考建议失败:",d),w.error("保存失败，请重试")}},be=async()=>{var d;if(!pe.value)throw new Error("报告ID不存在");const a=((d=k.value.recommend_list)==null?void 0:d.map(h=>({report_id:pe.value,school_id:parseInt(h.school_id),competition_difficulty:h.difficulty_analysis||"",suggestions:h.suggest||""})))||[],l={report_id:pe.value,recommendations:a,pdf_url:""};await Gt(l)},A=a=>{var d,h;ve.value[a]=!0;const l=(d=k.value.recommend_list)==null?void 0:d.find(L=>L.school_id===a);l&&(i.value[a]=((h=l.basic_info)==null?void 0:h.score_formula)||"")},Be=async a=>{var l;try{const d=(l=k.value.recommend_list)==null?void 0:l.find(h=>h.school_id===a);d&&(d.basic_info||(d.basic_info={}),d.basic_info.score_formula=i.value[a]),await N(a,"score_formula",i.value[a]),ve.value[a]=!1,w.success("总成绩计算公式保存成功")}catch(d){console.error("保存总成绩计算公式失败:",d),w.error("保存失败，请重试")}},qe=a=>{ve.value[a]=!1,delete i.value[a]},ze=a=>{var d,h;ge.value[a]=!0;const l=(d=k.value.recommend_list)==null?void 0:d.find(L=>L.school_id===a);l&&(C.value[a]=((h=l.basic_info)==null?void 0:h.study_years)||"")},De=async a=>{var l;try{const d=(l=k.value.recommend_list)==null?void 0:l.find(h=>h.school_id===a);d&&(d.basic_info||(d.basic_info={}),d.basic_info.study_years=C.value[a]),await be(),ge.value[a]=!1,w.success("学制保存成功")}catch(d){console.error("保存学制失败:",d),w.error("保存失败，请重试")}},Oe=a=>{ge.value[a]=!1,delete C.value[a]},Ye=a=>{var d,h;_e.value[a]=!0;const l=(d=k.value.recommend_list)==null?void 0:d.find(L=>L.school_id===a);l&&(S.value[a]=((h=l.basic_info)==null?void 0:h.tuition_fee)||"")},Le=async a=>{var l;try{const d=(l=k.value.recommend_list)==null?void 0:l.find(h=>h.school_id===a);d&&(d.basic_info||(d.basic_info={}),d.basic_info.tuition_fee=S.value[a]),await N(a,"tuition_fee",S.value[a]),_e.value[a]=!1,w.success("学费保存成功")}catch(d){console.error("保存学费失败:",d),w.error("保存失败，请重试")}},We=a=>{_e.value[a]=!1,delete S.value[a]},c=a=>{var d,h;$e.value[a]=!0;const l=(d=k.value.recommend_list)==null?void 0:d.find(L=>L.school_id===a);l&&(U.value[a]=((h=l.basic_info)==null?void 0:h.exam_range)||"")},t=async a=>{var l;try{const d=(l=k.value.recommend_list)==null?void 0:l.find(h=>h.school_id===a);d&&(d.basic_info||(d.basic_info={}),d.basic_info.exam_range=U.value[a]),await N(a,"exam_subjects",U.value[a]),$e.value[a]=!1,w.success("初试考试科目保存成功")}catch(d){console.error("保存初试考试科目失败:",d),w.error("保存失败，请重试")}},_=a=>{$e.value[a]=!1,delete U.value[a]},y=a=>{var d,h;oe.value[a]=!0;const l=(d=k.value.recommend_list)==null?void 0:d.find(L=>L.school_id===a);l&&(m.value[a]=((h=l.basic_info)==null?void 0:h.reference_books)||"")},E=async a=>{var l;try{const d=(l=k.value.recommend_list)==null?void 0:l.find(h=>h.school_id===a);d&&(d.basic_info||(d.basic_info={}),d.basic_info.reference_books=m.value[a]),await N(a,"reference_books",m.value[a]),oe.value[a]=!1,w.success("考试专业课参考书保存成功")}catch(d){console.error("保存考试专业课参考书失败:",d),w.error("保存失败，请重试")}},Z=a=>{oe.value[a]=!1,delete m.value[a]},Se=async a=>{var l;try{const d=(l=k.value.recommend_list)==null?void 0:l.find(h=>h.school_id===a);d&&(d.basic_info||(d.basic_info={}),d.basic_info.retest_score_requirement=ie.value[a]),await N(a,"retest_score_requirement",ie.value[a]),b.value[a]=!1,w.success("复试考核内容保存成功")}catch(d){console.error("保存复试考核内容失败:",d),w.error("保存失败，请重试")}},ne=a=>{b.value[a]=!1,delete ie.value[a]},He=a=>{var d,h;Q.value[a]=!0;const l=(d=k.value.recommend_list)==null?void 0:d.find(L=>L.school_id===a);l&&(D.value[a]=((h=l.basic_info)==null?void 0:h.retest_content)||"")},Ge=async a=>{var l;try{const d=(l=k.value.recommend_list)==null?void 0:l.find(h=>h.school_id===a);d&&(d.basic_info||(d.basic_info={}),d.basic_info.retest_content=D.value[a]),await N(a,"retest_content",D.value[a]),Q.value[a]=!1,w.success("复试考试内容保存成功")}catch(d){console.error("保存复试考试内容失败:",d),w.error("保存失败，请重试")}},n=a=>{j.value[a]=!1,delete F.value[a]},de=a=>{V.value[a]=!1,delete z.value[a]},Je=a=>{Q.value[a]=!1,delete D.value[a]},Tt=a=>{var l,d;J.value[a]=!0,(d=(l=k.value.high_recommend_list)==null?void 0:l[0])!=null&&d.reason&&(we.value[a]=k.value.high_recommend_list[0].reason)};return Rt(()=>{W()}),je({show:se,hide:G,toggle:O,isVisible:H,setReportId:fe,startLoading:Ae,stopLoading:W,showCompletionAnimation:ee,testLoadingTexts:()=>{console.log("开始测试加载文字切换逻辑"),console.log("阶段文字配置:",ue);for(let a=1;a<=4;a++){console.log(`
=== 阶段 ${a} ===`);const l=a-1,d=ue[l];d&&d.forEach((h,L)=>{console.log(`  文字 ${L+1}: ${h}`)})}},isLoading:Me}),(a,l)=>{var L,Ee,Fe,tt,lt,st;const d=Qe,h=Ie;return xe((r(),v("div",al,[(r(),v("div",ol,[e("div",il,[l[8]||(l[8]=e("div",{class:"step-header"},"第三部分：院校总览",-1)),e("div",nl,[e("div",dl,[l[7]||(l[7]=Mt('<div class="school-table-header" data-v-b62c4b03><div class="header-cell" data-v-b62c4b03>序号</div><div class="header-cell" data-v-b62c4b03>院校名称</div><div class="header-cell" data-v-b62c4b03>所在地区</div><div class="header-cell" data-v-b62c4b03>所在省市</div><div class="header-cell" data-v-b62c4b03>学院</div><div class="header-cell" data-v-b62c4b03>专业</div><div class="header-cell" data-v-b62c4b03>专业代码</div><div class="header-cell" data-v-b62c4b03>总低分</div><div class="header-cell" data-v-b62c4b03>总低分分差</div></div>',1)),e("div",rl,[(r(!0),v(B,null,q(((L=me(k))==null?void 0:L.school_list)||[],(o,Ne)=>(r(),v("div",{class:"table-row",key:Ne},[e("div",ul,$(o.id),1),e("div",cl,[e("div",null,$(o.school_name),1),e("div",vl,[o.tag_985?(r(),v("span",ml,"985")):T("",!0),o.tag_211?(r(),v("span",pl,"211")):T("",!0),o.tag_double?(r(),v("span",yl,"双一流")):T("",!0)])]),e("div",gl,$(o.region),1),e("div",_l,$(o.city),1),e("div",fl,$(o.college),1),e("div",bl,$(o.major_name),1),e("div",kl,$(o.major_code),1),e("div",hl,$(o.min_score),1),e("div",$l,$(o.score_diff),1)]))),128))])])])]),e("div",null,[e("div",wl,[l[63]||(l[63]=e("div",{class:"step-header"},"第四部分：院校分析",-1)),(r(!0),v(B,null,q(((Ee=me(k))==null?void 0:Ee.recommend_list)||[],(o,Ne)=>{var at,ot,it,nt,dt,rt,ut,ct,vt,mt,pt,yt,gt,_t,ft,bt,kt;return r(),v("div",{class:"step-content",key:Ne},[e("div",Vl,[e("span",null,$(Ne+1),1),l[9]||(l[9]=e("div",{class:"tag-text"},"院校推荐",-1))]),e("div",Cl,[e("div",Sl,[e("div",Ml,[e("img",{src:"https://"+((at=o==null?void 0:o.school_info)==null?void 0:at.logo)||"@/assets/images/school.png",alt:"学校logo"},null,8,xl)]),e("div",Tl,[e("div",jl,[e("h2",null,$((o==null?void 0:o.school_name)||""),1),e("span",Ul,$(((ot=o==null?void 0:o.school_info)==null?void 0:ot.address)||""),1)]),e("div",Al,[(it=o==null?void 0:o.school_info)!=null&&it.is_dual_class?(r(),v("span",El,"双一流")):T("",!0),(nt=o==null?void 0:o.school_info)!=null&&nt.tag_985?(r(),v("span",Rl,"985")):T("",!0),(dt=o==null?void 0:o.school_info)!=null&&dt.tag_211?(r(),v("span",Pl,"211")):T("",!0)])])]),l[61]||(l[61]=e("h3",{class:"section-title"},"院校情况",-1)),e("div",zl,[e("div",Dl,[l[14]||(l[14]=e("div",{class:"item-icon"},[e("img",{src:ke,alt:"标签图标"})],-1)),e("div",Ll,[e("div",Fl,[l[11]||(l[11]=e("h4",null,"总成绩计算公式",-1)),!ve.value[o.school_id]&&K.value?(r(),v("div",Nl,[u(d,{type:"text",size:"small",onClick:g=>A(o.school_id),class:"edit-btn"},{default:f(()=>l[10]||(l[10]=[x(" 编辑 ")])),_:2},1032,["onClick"])])):T("",!0)]),ve.value[o.school_id]?(r(),v("div",ql,[u(h,{modelValue:i.value[o.school_id],"onUpdate:modelValue":g=>i.value[o.school_id]=g,type:"textarea",rows:3,placeholder:"请输入总成绩计算公式",class:"edit-textarea"},null,8,["modelValue","onUpdate:modelValue"]),e("div",Ol,[u(d,{size:"small",onClick:g=>Be(o.school_id),class:"save-btn"},{default:f(()=>l[12]||(l[12]=[x("保存")])),_:2},1032,["onClick"]),u(d,{size:"small",onClick:g=>qe(o.school_id)},{default:f(()=>l[13]||(l[13]=[x("取消")])),_:2},1032,["onClick"])])])):(r(),v("div",Bl,[e("p",null,$((rt=o==null?void 0:o.basic_info)==null?void 0:rt.score_formula),1)]))])]),e("div",Yl,[l[19]||(l[19]=e("div",{class:"item-icon"},[e("img",{src:ke,alt:"标签图标"})],-1)),e("div",Wl,[e("div",Hl,[l[16]||(l[16]=e("h4",null,"学制",-1)),!ge.value[o.school_id]&&K.value?(r(),R(d,{key:0,type:"text",size:"small",onClick:g=>ze(o.school_id),class:"edit-btn"},{default:f(()=>l[15]||(l[15]=[x(" 编辑 ")])),_:2},1032,["onClick"])):T("",!0)]),ge.value[o.school_id]?(r(),v("div",Jl,[u(h,{modelValue:C.value[o.school_id],"onUpdate:modelValue":g=>C.value[o.school_id]=g,type:"textarea",rows:2,placeholder:"请输入学制",class:"edit-textarea"},null,8,["modelValue","onUpdate:modelValue"]),e("div",Kl,[u(d,{size:"small",onClick:g=>De(o.school_id),class:"save-btn"},{default:f(()=>l[17]||(l[17]=[x("保存")])),_:2},1032,["onClick"]),u(d,{size:"small",onClick:g=>Oe(o.school_id)},{default:f(()=>l[18]||(l[18]=[x("取消")])),_:2},1032,["onClick"])])])):(r(),v("div",Gl,[e("p",null,$((ut=o==null?void 0:o.basic_info)==null?void 0:ut.study_years),1)]))])]),e("div",Xl,[l[24]||(l[24]=e("div",{class:"item-icon"},[e("img",{src:ke,alt:"标签图标"})],-1)),e("div",Zl,[e("div",Ql,[l[21]||(l[21]=e("h4",null,"学费",-1)),!_e.value[o.school_id]&&K.value?(r(),R(d,{key:0,type:"text",size:"small",onClick:g=>Ye(o.school_id),class:"edit-btn"},{default:f(()=>l[20]||(l[20]=[x(" 编辑 ")])),_:2},1032,["onClick"])):T("",!0)]),_e.value[o.school_id]?(r(),v("div",es,[u(h,{modelValue:S.value[o.school_id],"onUpdate:modelValue":g=>S.value[o.school_id]=g,type:"textarea",rows:2,placeholder:"请输入学费",class:"edit-textarea"},null,8,["modelValue","onUpdate:modelValue"]),e("div",ts,[u(d,{size:"small",onClick:g=>Le(o.school_id),class:"save-btn"},{default:f(()=>l[22]||(l[22]=[x("保存")])),_:2},1032,["onClick"]),u(d,{size:"small",onClick:g=>We(o.school_id)},{default:f(()=>l[23]||(l[23]=[x("取消")])),_:2},1032,["onClick"])])])):(r(),v("div",Il,[e("p",null,$((ct=o==null?void 0:o.basic_info)==null?void 0:ct.tuition_fee),1)]))])])]),l[62]||(l[62]=e("h3",{class:"section-title"},"初试模块",-1)),e("div",ls,[e("div",ss,[l[29]||(l[29]=e("div",{class:"item-icon"},[e("img",{src:ke,alt:"标签图标"})],-1)),e("div",as,[e("div",os,[l[26]||(l[26]=e("h4",null,"初试考试科目",-1)),!$e.value[o.school_id]&&K.value?(r(),R(d,{key:0,type:"text",size:"small",onClick:g=>c(o.school_id),class:"edit-btn"},{default:f(()=>l[25]||(l[25]=[x(" 编辑 ")])),_:2},1032,["onClick"])):T("",!0)]),$e.value[o.school_id]?(r(),v("div",ns,[u(h,{modelValue:U.value[o.school_id],"onUpdate:modelValue":g=>U.value[o.school_id]=g,type:"textarea",rows:3,placeholder:"请输入初试考试科目",class:"edit-textarea"},null,8,["modelValue","onUpdate:modelValue"]),e("div",ds,[u(d,{size:"small",onClick:g=>t(o.school_id),class:"save-btn"},{default:f(()=>l[27]||(l[27]=[x("保存")])),_:2},1032,["onClick"]),u(d,{size:"small",onClick:g=>_(o.school_id)},{default:f(()=>l[28]||(l[28]=[x("取消")])),_:2},1032,["onClick"])])])):(r(),v("div",is,[e("p",null,$((vt=o==null?void 0:o.basic_info)==null?void 0:vt.exam_range),1)]))])]),e("div",rs,[l[34]||(l[34]=e("div",{class:"item-icon"},[e("img",{src:ke,alt:"标签图标"})],-1)),e("div",us,[e("div",cs,[l[31]||(l[31]=e("h4",null,"考试专业课参考书",-1)),!oe.value[o.school_id]&&K.value?(r(),R(d,{key:0,type:"text",size:"small",onClick:g=>y(o.school_id),class:"edit-btn"},{default:f(()=>l[30]||(l[30]=[x(" 编辑 ")])),_:2},1032,["onClick"])):T("",!0)]),oe.value[o.school_id]?(r(),v("div",ms,[u(h,{modelValue:m.value[o.school_id],"onUpdate:modelValue":g=>m.value[o.school_id]=g,type:"textarea",rows:4,placeholder:"请输入考试专业课参考书",class:"edit-textarea"},null,8,["modelValue","onUpdate:modelValue"]),e("div",ps,[u(d,{size:"small",onClick:g=>E(o.school_id),class:"save-btn"},{default:f(()=>l[32]||(l[32]=[x("保存")])),_:2},1032,["onClick"]),u(d,{size:"small",onClick:g=>Z(o.school_id)},{default:f(()=>l[33]||(l[33]=[x("取消")])),_:2},1032,["onClick"])])])):(r(),v("div",vs,[e("p",null,$((mt=o==null?void 0:o.basic_info)==null?void 0:mt.reference_books),1)]))])]),(pt=o==null?void 0:o.basic_info)!=null&&pt.retest_score_requirement?(r(),v("div",ys,[l[38]||(l[38]=e("div",{class:"item-icon"},[e("img",{src:ke,alt:"标签图标"})],-1)),e("div",gs,[e("div",_s,[l[35]||(l[35]=e("h4",null,"复试考核内容",-1)),e("p",null,$((yt=o==null?void 0:o.basic_info)==null?void 0:yt.retest_score_requirement),1)]),b.value[o.school_id]?(r(),v("div",bs,[u(h,{modelValue:ie.value[o.school_id],"onUpdate:modelValue":g=>ie.value[o.school_id]=g,type:"textarea",rows:3,placeholder:"请输入复试考核内容",class:"edit-textarea"},null,8,["modelValue","onUpdate:modelValue"]),e("div",ks,[u(d,{size:"small",onClick:g=>Se(o.school_id),class:"save-btn"},{default:f(()=>l[36]||(l[36]=[x("保存")])),_:2},1032,["onClick"]),u(d,{size:"small",onClick:g=>ne(o.school_id)},{default:f(()=>l[37]||(l[37]=[x("取消")])),_:2},1032,["onClick"])])])):(r(),v("div",fs,[e("p",null,$((gt=o==null?void 0:o.basic_info)==null?void 0:gt.retest_score_requirement),1)]))])])):T("",!0)]),e("div",hs,[o!=null&&o.admission_data&&o.admission_data.length>0?(r(),v("div",$s,"招生情况 （"+$((_t=o.admission_data[0])==null?void 0:_t.year)+"年）",1)):T("",!0),e("table",ws,[l[39]||(l[39]=e("thead",null,[e("tr",null,[e("th",null,"年份"),e("th",null,"招生计划"),e("th",null,"一志愿考试"),e("th",null,"录取数"),e("th",null,"录取比"),e("th",null,"调剂人数"),e("th",null,"最高分"),e("th",null,"最低分"),e("th",null,"平均分")])],-1)),e("tbody",null,[(r(!0),v(B,null,q(o.admission_data,(g,Te)=>(r(),v("tr",{key:Te},[e("td",null,$(g.year),1),e("td",null,$(g.planCount),1),e("td",null,$(g.examCount),1),e("td",null,$(g.admitCount),1),e("td",null,$(g.ratio),1),e("td",null,$(g.studentCount),1),e("td",null,$(g.highestScore),1),e("td",null,$(g.lowestScore),1),e("td",null,$(g.averageScore),1)]))),128))])]),l[57]||(l[57]=e("div",{class:"admission-title"},"一志愿考试名单",-1)),e("table",Vs,[l[40]||(l[40]=e("thead",null,[e("tr",null,[e("th",null,"编号"),e("th",null,"学生姓名"),e("th",null,"政治"),e("th",null,"英语"),e("th",null,"专业课一"),e("th",null,"专业课二"),e("th",null,"初试成绩"),e("th",null,"是否一志愿")])],-1)),e("tbody",null,[(r(!0),v(B,null,q(((ft=o==null?void 0:o.current_year_retest_list)==null?void 0:ft.list)||[],(g,Te)=>(r(),v("tr",{key:Te},[e("td",null,$(Te+1),1),e("td",null,$(g.name),1),e("td",null,$(g.politics_score),1),e("td",null,$(g.english_score),1),e("td",null,$(g.major1_score),1),e("td",null,$(g.major2_score),1),e("td",null,$(g.initial_score),1),e("td",null,$(g.admission_status),1)]))),128))])]),l[58]||(l[58]=e("div",{class:"admission-title"},"复试模块",-1)),e("div",Cs,[e("div",Ss,[e("div",Ms,[l[42]||(l[42]=e("img",{src:ke,alt:"标签图标"},null,-1)),l[43]||(l[43]=e("div",{class:"reexam-title"},"复试考试内容",-1)),!Q.value[o.school_id]&&K.value?(r(),R(d,{key:0,type:"text",size:"small",onClick:g=>He(o.school_id),class:"edit-btn"},{default:f(()=>l[41]||(l[41]=[x(" 编辑 ")])),_:2},1032,["onClick"])):T("",!0)]),e("div",xs,[Q.value[o.school_id]?(r(),v("div",js,[u(h,{modelValue:D.value[o.school_id],"onUpdate:modelValue":g=>D.value[o.school_id]=g,type:"textarea",rows:4,placeholder:"请输入复试考试内容",class:"edit-textarea"},null,8,["modelValue","onUpdate:modelValue"]),e("div",Us,[u(d,{size:"small",onClick:g=>Ge(o.school_id),class:"save-btn"},{default:f(()=>l[44]||(l[44]=[x("保存")])),_:2},1032,["onClick"]),u(d,{size:"small",onClick:g=>Je(o.school_id)},{default:f(()=>l[45]||(l[45]=[x("取消")])),_:2},1032,["onClick"])])])):(r(),v("div",Ts,$((bt=o==null?void 0:o.basic_info)==null?void 0:bt.retest_content),1))])])]),l[59]||(l[59]=e("div",{class:"admission-title"},"拟录取名单",-1)),e("table",As,[l[46]||(l[46]=e("thead",null,[e("tr",null,[e("th",null,"编号"),e("th",null,"学生姓名"),e("th",null,"初试成绩"),e("th",null,"复试成绩"),e("th",null,"两项总成绩"),e("th",null,"一志愿学校")])],-1)),e("tbody",null,[(r(!0),v(B,null,q(((kt=o==null?void 0:o.current_year_admission_list)==null?void 0:kt.list)||[],(g,Te)=>(r(),v("tr",{key:Te},[e("td",null,$(Te+1),1),e("td",null,$(g.name),1),e("td",null,$(g.initial_score),1),e("td",null,$(g.retest_score),1),e("td",null,$(g.total_score),1),e("td",null,$(g.first_choice_school),1)]))),128))])]),l[60]||(l[60]=e("div",{class:"admission-title"},"综合建议",-1)),e("div",Es,[e("div",Rs,[e("div",Ps,[l[48]||(l[48]=e("img",{src:ke,alt:"标签图标"},null,-1)),l[49]||(l[49]=e("div",{class:"reexam-title"},"竞争难度分析",-1)),j.value[o.school_id]?T("",!0):(r(),R(d,{key:0,type:"text",size:"small",onClick:g=>Ve(o.school_id),class:"edit-btn"},{default:f(()=>l[47]||(l[47]=[x(" 编辑 ")])),_:2},1032,["onClick"]))]),e("div",zs,[j.value[o.school_id]?(r(),v("div",Ls,[u(h,{modelValue:F.value[o.school_id],"onUpdate:modelValue":g=>F.value[o.school_id]=g,type:"textarea",rows:4,placeholder:"请输入竞争难度分析",class:"edit-textarea"},null,8,["modelValue","onUpdate:modelValue"]),e("div",Fs,[u(d,{size:"small",onClick:g=>X(o.school_id)},{default:f(()=>l[50]||(l[50]=[x("保存")])),_:2},1032,["onClick"]),u(d,{size:"small",onClick:g=>n(o.school_id)},{default:f(()=>l[51]||(l[51]=[x("取消")])),_:2},1032,["onClick"])])])):(r(),v("div",Ds,$(o.difficulty_analysis),1))])]),e("div",Ns,[e("div",Bs,[l[53]||(l[53]=e("img",{src:ke,alt:"标签图标"},null,-1)),l[54]||(l[54]=e("div",{class:"reexam-title"},"备考目标建议",-1)),V.value[o.school_id]?T("",!0):(r(),R(d,{key:0,type:"text",size:"small",onClick:g=>Ce(o.school_id),class:"edit-btn"},{default:f(()=>l[52]||(l[52]=[x(" 编辑 ")])),_:2},1032,["onClick"]))]),e("div",qs,[V.value[o.school_id]?(r(),v("div",Ys,[u(h,{modelValue:z.value[o.school_id],"onUpdate:modelValue":g=>z.value[o.school_id]=g,type:"textarea",rows:4,placeholder:"请输入备考目标建议",class:"edit-textarea"},null,8,["modelValue","onUpdate:modelValue"]),e("div",Ws,[u(d,{size:"small",onClick:g=>le(o.school_id)},{default:f(()=>l[55]||(l[55]=[x("保存")])),_:2},1032,["onClick"]),u(d,{size:"small",onClick:g=>de(o.school_id)},{default:f(()=>l[56]||(l[56]=[x("取消")])),_:2},1032,["onClick"])])])):(r(),v("div",Os,$(o.suggest),1))])])])])])])}),128))])]),l[67]||(l[67]=e("div",{class:"step-num-tag"},[e("span",null,"03"),e("div",{class:"tag-text"},"推荐综合性价比高的院校")],-1)),e("div",Hs,[e("div",Gs,[e("div",Js,[l[65]||(l[65]=e("div",{class:"recommend-icon"},[e("img",{src:ke,alt:"标签图标"})],-1)),l[66]||(l[66]=e("div",{class:"recommend-title"},"推荐原因",-1)),!J.value.high_recommend&&K.value?(r(),R(d,{key:0,type:"text",size:"small",onClick:l[0]||(l[0]=o=>Tt("high_recommend")),class:"edit-btn"},{default:f(()=>l[64]||(l[64]=[x(" 编辑 ")])),_:1})):T("",!0)]),e("div",Ks,$(Array.isArray((Fe=me(k))==null?void 0:Fe.high_recommend_list)&&((tt=me(k))==null?void 0:tt.high_recommend_list.length)>0&&((st=(lt=me(k))==null?void 0:lt.high_recommend_list[0])==null?void 0:st.reason)||"暂无推荐信息"),1)])])]))],512)),[[St,H.value]])}}}),Zs=Ze(Xs,[["__scopeId","data-v-b62c4b03"]]),Qs={class:"weak-module-analysis"},Is={key:0,class:"weak-module-container"},ea={class:"detail-section"},ta={class:"subtitle-container"},la={key:1,class:"detail-text"},sa=["textContent"],aa={key:0,class:"typing-cursor"},oa={class:"edit-button-container"},ia={key:1,class:"edit-actions"},na={key:0,class:"analysis-section"},da={key:1,class:"analysis-text"},ra=["textContent"],ua={key:0,class:"typing-cursor"},ca={key:1,class:"solutions-section"},va={class:"solutions-content"},ma={key:1,class:"solution-text"},pa=["textContent"],ya={key:0,class:"typing-cursor"},ga={class:"target-score-section"},_a={class:"target-score-header"},fa={class:"target-score-actions"},ba={class:"target-score-table"},ka={class:"score-row data-row"},ha={class:"score-cell bottom-cell"},$a={class:"score-cell bottom-cell"},wa={class:"score-cell bottom-cell"},Va={class:"score-cell bottom-cell"},Ca={class:"score-cell bottom-cell"},Sa={key:1,class:"study-planning-section"},Ma={class:"step-num-tag"},xa={class:"tag-text"},Ta={key:0,class:"stage-title"},ja=["textContent"],Ua={key:0,class:"typing-cursor"},Aa={key:1,class:"stage-content"},Ea={key:0,class:"module-title"},Ra={key:1},Pa=["textContent"],za={key:0,class:"typing-cursor"},Da={class:"edit-button-container"},La={key:1,class:"edit-actions"},Fa={key:1,class:"study-item"},Na={key:1,class:"study-item-content"},Ba=["textContent"],qa={key:0,class:"typing-cursor"},Oa={key:2,class:"study-item"},Ya={key:1,class:"study-item-content"},Wa=["textContent"],Ha={key:0,class:"typing-cursor"},Ga={key:3,class:"study-item"},Ja={key:1,class:"study-item-content"},Ka=["textContent"],Xa={key:0,class:"typing-cursor"},Za={key:4,class:"study-item"},Qa={key:1,class:"study-item-content"},Ia=["textContent"],eo={key:0,class:"typing-cursor"},to={key:2,class:"comprehensive-advice-section"},lo={class:"advice-content"},so={class:"advice-content-container"},ao={key:1,class:"advice-text"},oo=["textContent"],io={key:0,class:"typing-cursor"},no={class:"edit-button-container"},ro={key:1,class:"edit-actions"},uo=Vt({__name:"WeakModuleAnalysisEdit",setup(et,{expose:je}){let Ue=p(!1);const P=p({english:0,politics:0,business1:0,business2:0}),H=xt(()=>P.value.politics+P.value.english+P.value.business1+P.value.business2),se=p({weakModuleAnalysis:[],studyPlanning:{title:"初试各科学习规划",stages:[]},comprehensiveAdvice:""}),G=p([]),O=p(!1),k=p({weakModules:[],studyModules:{},comprehensiveAdvice:!1}),j=p({weakModules:[],studyModules:{},comprehensiveAdvice:""}),V=p({weakModules:[],studyPlanning:{stages:[]},comprehensiveAdvice:{text:"",isTyping:!1}}),F=p({title:"初试各科学习规划",stages:[]}),z=(b,i,C=80)=>{i.text=b},pe=async b=>{G.value=b,V.value.weakModules=b.map(()=>({subject:{text:"",isTyping:!1},problemAnalysis:{text:"",isTyping:!1},solutions:{text:"",isTyping:!1}}));for(let i=0;i<b.length;i++){const C=b[i],S=V.value.weakModules[i];console.log(`渲染薄弱模块 ${i+1}: ${C.subject}`),await z(C.subject,S.subject,80),await z(C.problemAnalysis,S.problemAnalysis,40),await z(C.solutions,S.solutions,40)}},Me=async b=>{console.log("renderStudyPlanning 开始，数据:",b),F.value=b,V.value.studyPlanning.stages=b.stages.map(i=>({title:{text:"",isTyping:!1},modules:i.modules.map(()=>({name:{text:"",isTyping:!1},studyContent:{text:"",isTyping:!1},studyMethod:{text:"",isTyping:!1},studyMaterials:{text:"",isTyping:!1},studyReminder:{text:"",isTyping:!1}}))})),console.log("打字机状态初始化完成，阶段数量:",V.value.studyPlanning.stages.length);for(let i=0;i<b.stages.length;i++){const C=b.stages[i],S=V.value.studyPlanning.stages[i];console.log(`开始渲染阶段 ${i+1}: ${C.title}`),await z(C.title,S.title,80);for(let U=0;U<C.modules.length;U++){const m=C.modules[U],D=S.modules[U];console.log(`  渲染模块 ${U+1}: ${m.name}`),await z(m.name,D.name,80),await z(m.studyContent,D.studyContent,25),await z(m.studyMethod,D.studyMethod,25),await z(m.studyMaterials,D.studyMaterials,25),await z(m.studyReminder,D.studyReminder,25)}}console.log("renderStudyPlanning 完成")},ye=async b=>{await z(b,V.value.comprehensiveAdvice,20)},te=async b=>{try{const i=await Jt({report_id:b});if(i.code===0&&i.data){const C=i.data;await $e(b),se.value=C,console.log("开始渲染学习计划数据:",C),C.weakModuleAnalysis&&C.weakModuleAnalysis.length>0&&(console.log("渲染薄弱模块分析，模块数量:",C.weakModuleAnalysis.length),await pe(C.weakModuleAnalysis)),console.log("渲染学习规划，阶段数量:",C.studyPlanning.stages.length),await Me(C.studyPlanning),console.log("渲染综合建议"),await ye(C.comprehensiveAdvice)}else console.error("获取学习计划失败:",i.msg),w.error(i.msg||"获取学习计划失败")}catch(i){console.error("获取学习计划异常:",i),w.error("获取学习计划失败")}},re=()=>{G.value=[],F.value={title:"初试各科学习规划",stages:[]},V.value={weakModules:[],studyPlanning:{stages:[]},comprehensiveAdvice:{text:"",isTyping:!1}}},Y=async b=>{try{O.value=!0,Ue.value=!0,console.log(b),await te(b)}catch(i){console.error("获取学习计划异常:",i),w.error("获取学习计划失败")}finally{O.value=!1}},ue=b=>{k.value.weakModules[b]=!0;const i=G.value[b];j.value.weakModules[b]={id:i.id,subject:i.subject,problemAnalysis:i.problemAnalysis,solutions:i.solutions}},he=async b=>{try{O.value=!0;const i=j.value.weakModules[b];if(!i.id){w.error("缺少数据ID，无法保存");return}const C=await Kt({id:i.id,subject:i.subject,problem_analysis:i.problemAnalysis,solutions:i.solutions});C.code===0?(G.value[b]={...i},V.value.weakModules[b]={subject:{text:i.subject,isTyping:!1},problemAnalysis:{text:i.problemAnalysis,isTyping:!1},solutions:{text:i.solutions,isTyping:!1}},k.value.weakModules[b]=!1,w.success("保存成功")):w.error(C.msg||"保存失败")}catch(i){console.error("保存薄弱模块失败:",i),w.error("保存失败")}finally{O.value=!1}},ce=b=>{k.value.weakModules[b]=!1,delete j.value.weakModules[b]},s=(b,i)=>{const C=`${b}-${i}`;k.value.studyModules[C]=!0;const S=F.value.stages[b].modules[i];j.value.studyModules[C]={id:S.id,dbId:S.dbId,name:S.name,studyContent:S.studyContent,studyMethod:S.studyMethod,studyMaterials:S.studyMaterials,studyReminder:S.studyReminder}},ae=async(b,i)=>{try{O.value=!0;const C=`${b}-${i}`,S=j.value.studyModules[C];if(!S.dbId){w.error("缺少数据ID，无法保存");return}const U=await Xt({id:S.dbId,name:S.name,study_content:S.studyContent,study_method:S.studyMethod,study_materials:S.studyMaterials,study_reminder:S.studyReminder});if(U.code===0){F.value.stages[b].modules[i]={...S};const m=V.value.studyPlanning.stages[b].modules[i];m.name={text:S.name,isTyping:!1},m.studyContent={text:S.studyContent,isTyping:!1},m.studyMethod={text:S.studyMethod,isTyping:!1},m.studyMaterials={text:S.studyMaterials,isTyping:!1},m.studyReminder={text:S.studyReminder,isTyping:!1},k.value.studyModules[C]=!1,w.success("保存成功")}else w.error(U.msg||"保存失败")}catch(C){console.error("保存学习模块失败:",C),w.error("保存失败")}finally{O.value=!1}},Ae=(b,i)=>{const C=`${b}-${i}`;k.value.studyModules[C]=!1,delete j.value.studyModules[C]},W=()=>{k.value.comprehensiveAdvice=!0,j.value.comprehensiveAdvice=se.value.comprehensiveAdvice},ve=async()=>{try{if(O.value=!0,!se.value.comprehensiveAdviceId){w.error("缺少数据ID，无法保存");return}const b=await Zt({id:se.value.comprehensiveAdviceId,advice_content:j.value.comprehensiveAdvice});b.code===0?(se.value.comprehensiveAdvice=j.value.comprehensiveAdvice,V.value.comprehensiveAdvice={text:j.value.comprehensiveAdvice,isTyping:!1},k.value.comprehensiveAdvice=!1,w.success("保存成功")):w.error(b.msg||"保存失败")}catch(b){console.error("保存综合建议失败:",b),w.error("保存失败")}finally{O.value=!1}},ge=()=>{k.value.comprehensiveAdvice=!1,j.value.comprehensiveAdvice=""},_e=p({politics:0,english:0,business1:0,business2:0,total:0}),$e=async b=>{try{Q.value=b,console.log("------------"),console.log(b);const i=await Qt(b);i.code===0&&i.data?(P.value=i.data,console.log("开始渲染学习计划数据:",_e.value)):(console.error("获取学习计划失败:",i.msg),w.error(i.msg||"获取学习计划失败"))}catch(i){console.error("获取学习计划异常:",i),w.error("获取学习计划失败")}},oe=p(!1),Q=p(""),J=async()=>{try{if(!Q.value){w.error("缺少报告ID，无法保存");return}oe.value=!0;const b=await It({report_id:Q.value,politics:P.value.politics||0,english:P.value.english||0,business1:P.value.business1||0,business2:P.value.business2||0,total:H.value||0});b.code===0?w.success("目标分数保存成功"):w.error(b.msg||"保存失败")}catch(b){console.error("保存目标分数失败:",b),w.error("保存失败")}finally{oe.value=!1}};return je({fetchStudyPlan:Y,fetchAndRenderStudyPlan:te,resetStates:re}),(b,i)=>{const C=Ie,S=Qe;return xe((r(),v("div",Qs,[i[27]||(i[27]=e("img",{width:"320",src:"https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/study_sche.png"},null,-1)),i[28]||(i[28]=e("div",{class:"step-num-tag"},[e("span",null,"01"),e("div",{class:"tag-text"},"薄弱模块分析")],-1)),G.value.length>0?(r(),v("div",Is,[(r(!0),v(B,null,q(G.value,(U,m)=>{var D,we,ie,M,K,N,I,ee,fe,Ve,Ce,X,le,be;return r(),v("div",{key:m},[e("div",ea,[i[9]||(i[9]=e("div",{class:"subtitle"},[e("img",{width:"26",src:"https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png",alt:"标签图标"}),e("span",{class:"subtitle-text"},"科目")],-1)),e("div",ta,[k.value.weakModules[m]?(r(),R(C,{key:0,modelValue:j.value.weakModules[m].subject,"onUpdate:modelValue":A=>j.value.weakModules[m].subject=A,placeholder:"请输入科目名称",class:"edit-input analysis-text"},null,8,["modelValue","onUpdate:modelValue"])):(r(),v("p",la,[e("span",{textContent:$((U==null?void 0:U.subject)||"")},null,8,sa),(we=(D=V.value.weakModules[m])==null?void 0:D.subject)!=null&&we.isTyping?(r(),v("span",aa,"|")):T("",!0)])),e("div",oa,[k.value.weakModules[m]?(r(),v("div",ia,[u(S,{size:"small",onClick:A=>he(m),loading:O.value,class:"save-btn"},{default:f(()=>i[7]||(i[7]=[x(" 保存 ")])),_:2},1032,["onClick","loading"]),u(S,{size:"small",onClick:A=>ce(m)},{default:f(()=>i[8]||(i[8]=[x(" 取消 ")])),_:2},1032,["onClick"])])):(r(),R(S,{key:0,type:"text",size:"small",onClick:A=>ue(m),icon:me(Ke),class:"edit-btn"},{default:f(()=>i[6]||(i[6]=[x(" 编辑 ")])),_:2},1032,["onClick","icon"]))])])]),(M=(ie=V.value.weakModules[m])==null?void 0:ie.problemAnalysis)!=null&&M.text||k.value.weakModules[m]?(r(),v("div",na,[i[10]||(i[10]=e("div",{class:"subtitle"},[e("img",{width:"26",src:"https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png",alt:"标签图标"}),e("span",{class:"subtitle-text"},"问题分析")],-1)),k.value.weakModules[m]?(r(),R(C,{key:0,modelValue:j.value.weakModules[m].problemAnalysis,"onUpdate:modelValue":A=>j.value.weakModules[m].problemAnalysis=A,type:"textarea",rows:4,placeholder:"请输入问题分析",class:"edit-textarea analysis-text"},null,8,["modelValue","onUpdate:modelValue"])):(r(),v("p",da,[e("span",{textContent:$(((N=(K=V.value.weakModules[m])==null?void 0:K.problemAnalysis)==null?void 0:N.text)||"")},null,8,ra),(ee=(I=V.value.weakModules[m])==null?void 0:I.problemAnalysis)!=null&&ee.isTyping?(r(),v("span",ua,"|")):T("",!0)]))])):T("",!0),(Ve=(fe=V.value.weakModules[m])==null?void 0:fe.solutions)!=null&&Ve.text||k.value.weakModules[m]?(r(),v("div",ca,[i[11]||(i[11]=e("div",{class:"subtitle"},[e("img",{width:"26",src:"https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png",alt:"标签图标"}),e("span",{class:"subtitle-text"},"解决方案")],-1)),e("div",va,[k.value.weakModules[m]?(r(),R(C,{key:0,modelValue:j.value.weakModules[m].solutions,"onUpdate:modelValue":A=>j.value.weakModules[m].solutions=A,type:"textarea",rows:6,placeholder:"请输入解决方案",class:"edit-textarea"},null,8,["modelValue","onUpdate:modelValue"])):(r(),v("p",ma,[e("span",{textContent:$(((X=(Ce=V.value.weakModules[m])==null?void 0:Ce.solutions)==null?void 0:X.text)||"")},null,8,pa),(be=(le=V.value.weakModules[m])==null?void 0:le.solutions)!=null&&be.isTyping?(r(),v("span",ya,"|")):T("",!0)]))])])):T("",!0)])}),128))])):T("",!0),e("div",ga,[e("div",_a,[i[13]||(i[13]=e("div",{class:"target-score-title-container"},[e("img",{width:"26",src:"https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png",alt:"标签图标"}),e("span",{class:"target-score-title"},"目标分数")],-1)),e("div",fa,[u(S,{size:"small",onClick:J,loading:oe.value,class:"save-btn"},{default:f(()=>i[12]||(i[12]=[x(" 保存 ")])),_:1},8,["loading"])])]),e("div",ba,[i[14]||(i[14]=Mt('<div class="score-row header-row" data-v-240d7fe8><div class="score-cell" data-v-240d7fe8>政治</div><div class="score-cell" data-v-240d7fe8>英语</div><div class="score-cell" data-v-240d7fe8>业务课一</div><div class="score-cell" data-v-240d7fe8>业务课二</div><div class="score-cell" data-v-240d7fe8>总分</div></div>',1)),e("div",ka,[e("div",ha,[xe(e("input",{class:"score-input","onUpdate:modelValue":i[0]||(i[0]=U=>P.value.politics=U)},null,512),[[Re,P.value.politics,void 0,{number:!0}]])]),e("div",$a,[xe(e("input",{class:"score-input","onUpdate:modelValue":i[1]||(i[1]=U=>P.value.english=U)},null,512),[[Re,P.value.english,void 0,{number:!0}]])]),e("div",wa,[xe(e("input",{class:"score-input","onUpdate:modelValue":i[2]||(i[2]=U=>P.value.business1=U)},null,512),[[Re,P.value.business1,void 0,{number:!0}]])]),e("div",Va,[xe(e("input",{class:"score-input","onUpdate:modelValue":i[3]||(i[3]=U=>P.value.business2=U)},null,512),[[Re,P.value.business2,void 0,{number:!0}]])]),e("div",Ca,[xe(e("input",{class:"score-input","onUpdate:modelValue":i[4]||(i[4]=U=>H.value=U),disabled:""},null,512),[[Re,H.value,void 0,{number:!0}]])])])])]),F.value.stages.length>0?(r(),v("div",Sa,[e("div",Ma,[i[15]||(i[15]=e("span",null,"03",-1)),e("div",xa,$(F.value.title),1)]),(r(!0),v(B,null,q(F.value.stages,(U,m)=>{var D,we;return r(),v("div",{key:U.id,class:"stage-section"},[(D=V.value.studyPlanning.stages[m])!=null&&D.title.text?(r(),v("div",Ta,[e("span",{textContent:$(V.value.studyPlanning.stages[m].title.text)},null,8,ja),V.value.studyPlanning.stages[m].title.isTyping?(r(),v("span",Ua,"|")):T("",!0)])):T("",!0),(we=V.value.studyPlanning.stages[m])!=null&&we.title.text?(r(),v("div",Aa,[(r(!0),v(B,null,q(U.modules,(ie,M)=>{var K,N,I,ee,fe,Ve,Ce,X,le,be;return r(),v("div",{key:ie.id,class:"module-container"},[(N=(K=V.value.studyPlanning.stages[m])==null?void 0:K.modules[M])!=null&&N.name.text||k.value.studyModules[`${m}-${M}`]?(r(),v("div",Ea,[k.value.studyModules[`${m}-${M}`]?(r(),R(C,{key:0,modelValue:j.value.studyModules[`${m}-${M}`].name,"onUpdate:modelValue":A=>j.value.studyModules[`${m}-${M}`].name=A,placeholder:"请输入模块名称",class:"edit-input"},null,8,["modelValue","onUpdate:modelValue"])):(r(),v("div",Ra,[e("span",{class:"module-title-text",textContent:$(V.value.studyPlanning.stages[m].modules[M].name.text)},null,8,Pa),V.value.studyPlanning.stages[m].modules[M].name.isTyping?(r(),v("span",za,"|")):T("",!0)])),e("div",Da,[k.value.studyModules[`${m}-${M}`]?(r(),v("div",La,[u(S,{size:"small",onClick:A=>ae(m,M),loading:O.value,class:"save-btn"},{default:f(()=>i[17]||(i[17]=[x(" 保存 ")])),_:2},1032,["onClick","loading"]),u(S,{size:"small",onClick:A=>Ae(m,M)},{default:f(()=>i[18]||(i[18]=[x(" 取消 ")])),_:2},1032,["onClick"])])):(r(),R(S,{key:0,type:"text",size:"small",onClick:A=>s(m,M),icon:me(Ke),class:"edit-btn"},{default:f(()=>i[16]||(i[16]=[x(" 编辑 ")])),_:2},1032,["onClick","icon"]))])])):T("",!0),(ee=(I=V.value.studyPlanning.stages[m])==null?void 0:I.modules[M])!=null&&ee.studyContent.text||k.value.studyModules[`${m}-${M}`]?(r(),v("div",Fa,[i[19]||(i[19]=e("div",{class:"study-item-title"},[e("img",{width:"26",src:"https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png",alt:"菱形图标"}),e("span",{class:"study-item-title-text"},"学习内容")],-1)),k.value.studyModules[`${m}-${M}`]?(r(),R(C,{key:0,modelValue:j.value.studyModules[`${m}-${M}`].studyContent,"onUpdate:modelValue":A=>j.value.studyModules[`${m}-${M}`].studyContent=A,type:"textarea",rows:4,placeholder:"请输入学习内容",class:"edit-textarea"},null,8,["modelValue","onUpdate:modelValue"])):(r(),v("p",Na,[e("span",{textContent:$(V.value.studyPlanning.stages[m].modules[M].studyContent.text)},null,8,Ba),V.value.studyPlanning.stages[m].modules[M].studyContent.isTyping?(r(),v("span",qa,"|")):T("",!0)]))])):T("",!0),(Ve=(fe=V.value.studyPlanning.stages[m])==null?void 0:fe.modules[M])!=null&&Ve.studyMethod.text||k.value.studyModules[`${m}-${M}`]?(r(),v("div",Oa,[i[20]||(i[20]=e("div",{class:"study-item-title"},[e("img",{width:"26",src:"https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png",alt:"菱形图标"}),e("span",{class:"study-item-title-text"},"学习方法")],-1)),k.value.studyModules[`${m}-${M}`]?(r(),R(C,{key:0,modelValue:j.value.studyModules[`${m}-${M}`].studyMethod,"onUpdate:modelValue":A=>j.value.studyModules[`${m}-${M}`].studyMethod=A,type:"textarea",rows:4,placeholder:"请输入学习方法",class:"edit-textarea"},null,8,["modelValue","onUpdate:modelValue"])):(r(),v("p",Ya,[e("span",{textContent:$(V.value.studyPlanning.stages[m].modules[M].studyMethod.text)},null,8,Wa),V.value.studyPlanning.stages[m].modules[M].studyMethod.isTyping?(r(),v("span",Ha,"|")):T("",!0)]))])):T("",!0),(X=(Ce=V.value.studyPlanning.stages[m])==null?void 0:Ce.modules[M])!=null&&X.studyMaterials.text||k.value.studyModules[`${m}-${M}`]?(r(),v("div",Ga,[i[21]||(i[21]=e("div",{class:"study-item-title"},[e("img",{width:"26",src:"https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png",alt:"菱形图标"}),e("span",{class:"study-item-title-text"},"资料推荐")],-1)),k.value.studyModules[`${m}-${M}`]?(r(),R(C,{key:0,modelValue:j.value.studyModules[`${m}-${M}`].studyMaterials,"onUpdate:modelValue":A=>j.value.studyModules[`${m}-${M}`].studyMaterials=A,type:"textarea",rows:4,placeholder:"请输入资料推荐",class:"edit-textarea"},null,8,["modelValue","onUpdate:modelValue"])):(r(),v("p",Ja,[e("span",{textContent:$(V.value.studyPlanning.stages[m].modules[M].studyMaterials.text)},null,8,Ka),V.value.studyPlanning.stages[m].modules[M].studyMaterials.isTyping?(r(),v("span",Xa,"|")):T("",!0)]))])):T("",!0),(be=(le=V.value.studyPlanning.stages[m])==null?void 0:le.modules[M])!=null&&be.studyReminder.text||k.value.studyModules[`${m}-${M}`]?(r(),v("div",Za,[i[22]||(i[22]=e("div",{class:"study-item-title"},[e("img",{width:"26",src:"https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png",alt:"菱形图标"}),e("span",{class:"study-item-title-text"},"要点提醒")],-1)),k.value.studyModules[`${m}-${M}`]?(r(),R(C,{key:0,modelValue:j.value.studyModules[`${m}-${M}`].studyReminder,"onUpdate:modelValue":A=>j.value.studyModules[`${m}-${M}`].studyReminder=A,type:"textarea",rows:4,placeholder:"请输入要点提醒",class:"edit-textarea"},null,8,["modelValue","onUpdate:modelValue"])):(r(),v("p",Qa,[e("span",{textContent:$(V.value.studyPlanning.stages[m].modules[M].studyReminder.text)},null,8,Ia),V.value.studyPlanning.stages[m].modules[M].studyReminder.isTyping?(r(),v("span",eo,"|")):T("",!0)]))])):T("",!0)])}),128))])):T("",!0)])}),128))])):T("",!0),V.value.comprehensiveAdvice.text||k.value.comprehensiveAdvice?(r(),v("div",to,[i[26]||(i[26]=e("div",{class:"step-num-tag"},[e("span",null,"04"),e("div",{class:"tag-text"},"综合建议")],-1)),e("div",lo,[e("div",so,[k.value.comprehensiveAdvice?(r(),R(C,{key:0,modelValue:j.value.comprehensiveAdvice,"onUpdate:modelValue":i[5]||(i[5]=U=>j.value.comprehensiveAdvice=U),type:"textarea",rows:8,placeholder:"请输入综合建议",class:"edit-textarea"},null,8,["modelValue"])):(r(),v("p",ao,[e("span",{textContent:$(V.value.comprehensiveAdvice.text)},null,8,oo),V.value.comprehensiveAdvice.isTyping?(r(),v("span",io,"|")):T("",!0)]))]),e("div",no,[k.value.comprehensiveAdvice?(r(),v("div",ro,[u(S,{size:"small",onClick:ve,loading:O.value,class:"save-btn"},{default:f(()=>i[24]||(i[24]=[x(" 保存 ")])),_:1},8,["loading"]),u(S,{size:"small",onClick:ge},{default:f(()=>i[25]||(i[25]=[x(" 取消 ")])),_:1})])):(r(),R(S,{key:0,type:"text",size:"small",onClick:W,icon:me(Ke),class:"edit-btn"},{default:f(()=>i[23]||(i[23]=[x(" 编辑 ")])),_:1},8,["icon"]))])])])):T("",!0)],512)),[[St,!0]])}}}),co=Ze(uo,[["__scopeId","data-v-240d7fe8"]]),vo={class:"generate-container"},mo={class:"report-container"},po={class:"steps"},yo={class:"step-section"},go={class:"step-content"},_o={class:"form-grid"},fo={class:"form-item"},bo={class:"form-item"},ko={class:"form-item"},ho={class:"form-item"},$o={class:"form-item"},wo={class:"form-item"},Vo={class:"form-item"},Co={class:"form-item"},So={class:"form-item"},Mo={class:"form-item"},xo={class:"form-item"},To={class:"form-item"},jo={class:"step-section"},Uo={class:"step-content"},Ao={class:"step-container"},Eo={class:"score-grid"},Ro={class:"score-row"},Po=["onDragstart","onDrop"],zo={class:"score-label"},Do={class:"score-input-container"},Lo={class:"step-section"},Fo={class:"step-content"},No={class:"english-grid"},Bo={class:"english-row"},qo={class:"english-item"},Oo={class:"english-item"},Yo={class:"english-item"},Wo={class:"english-item"},Ho={class:"english-row"},Go={class:"english-item"},Jo={class:"english-item"},Ko={class:"step-section"},Xo={class:"step-content"},Zo={class:"school-grid"},Qo={class:"form-item"},Io={class:"form-item"},ei={class:"form-item"},ti={class:"form-item"},li={class:"form-item wide-item"},si={class:"step-section"},ai={class:"step-content"},oi={class:"score-table"},ii={class:"table-header"},ni={class:"th-cell"},di={class:"th-cell self-score"},ri={class:"table-row-score"},ui={class:"td-cell"},ci={class:"td-cell"},vi={class:"td-cell"},mi={class:"td-cell"},pi={class:"td-cell"},yi={class:"personal-demands"},gi={class:"expertise-advice"},_i={class:"ai-overlay-content"},fi={class:"ai-large-icon-wrapper"},bi={key:0,class:"preview-loading","element-loading-text":"正在生成预览..."},ki=["innerHTML"],hi={key:2,class:"preview-empty"},$i={class:"dialog-footer"},wi={class:"dialog-footer"},Vi={__name:"edit_report",setup(et){const je=zt(),Ue=p(null),P=p(null);let H=p(!0);p(!1);const se=p(!1),G=p([]),O=p(null),k={},j=p(!1),V=p([]),F=p(null),z={};p(!1);const pe=p([]);p(null);const Me=p([]),ye=p([]);p([]);const te=p(""),re=p(!1),Y=p([]),ue=p(null),he={},ce=p(!1),s=$t({name:"",sex:"1",undergraduateSchool:"",undergraduateMajor:"",targetMajor:"",majorCode:"",phone:"",examYear:"2027",isMultiDisciplinary:"1",educationalStyle:"1",gaokaoScore:"",mathScore1:"",mathScore2:"",statScore:"",linearScore:"",majorScore1:"",majorScore2:"",majorScore3:"",englishScore:"",cet4:"",cet6:"",tofelScore:"",ieltsScore:"",englishLevel:"",region:[],intendedSchools:[],targetSchool:"",targetSchoolName:"",schoolLevel:"",referenceBooks:"",politics:"",englishType:"",mathType:"",professional:"",totalScore:"",personalNeeds:"",weakModules:""}),ae=p(!1),Ae=c=>{if(!c||c.length<2)return"";const t=c.substring(0,2);return{"01":"哲学","02":"经济学","03":"法学","04":"教育学","05":"文学","06":"历史学","07":"理学","08":"工学","09":"农学",10:"医学",11:"军事学",12:"管理学",13:"艺术学"}[t]||""};let W=p([]);p(!1);const ve=p(!1),ge=p(!1),_e=p(""),$e=p(null),oe=p(null),Q=p(!1),J=$t({title:"",score:""}),b=c=>{X.value=c},i=p(null),C=(c,t)=>{i.value=t,c.dataTransfer.effectAllowed="move",c.target.style.opacity="0.5"},S=c=>{c.preventDefault(),c.dataTransfer.dropEffect="move";const t=c.currentTarget;t&&i.value&&t!==i.value&&t.classList.add("drag-over")},U=(c,t)=>{if(c.preventDefault(),document.querySelectorAll(".score-item").forEach(ne=>{ne.classList.remove("drag-over")}),!i.value||i.value.id===t.id)return;const y=W.value.findIndex(ne=>ne.id===i.value.id),E=W.value.findIndex(ne=>ne.id===t.id);if(y<0||E<0)return;const Z=[...W.value],[Se]=Z.splice(y,1);Z.splice(E,0,Se),W.value=Z,i.value=null,document.querySelectorAll(".score-item").forEach(ne=>{ne.style.opacity="1"}),w.success("排序已更新")},m=c=>{c.target.style.opacity="1",document.querySelectorAll(".score-item").forEach(_=>{_.classList.remove("drag-over")})},D=()=>{if(!J.title||!J.score){w.warning("科目名称和成绩不能为空");return}const t=Math.max(...W.value.map(_=>_.id),0)+1;W.value.push({id:t,title:J.title,score:Number(J.score)||J.score}),Q.value=!1,w.success("添加成功")},we=c=>{if(c!==""){if(se.value=!0,k[c]){G.value=k[c],se.value=!1;return}clearTimeout(O.value),O.value=setTimeout(()=>{ht(c).then(t=>{if(t.code===0&&t.data){const _=t.data.map(y=>({value:y.id.toString(),label:y.name}));G.value=_,k[c]=_}else G.value=[]}).catch(()=>{G.value=[]}).finally(()=>{se.value=!1})},300)}else G.value=[]},ie=c=>{if(!s.undergraduateSchool){w.warning("请先选择本科院校");return}if(c!==""){j.value=!0;const t=`${s.undergraduateSchool}_${c}`;if(z[t]){V.value=z[t],j.value=!1;return}clearTimeout(F.value),F.value=setTimeout(()=>{Ut(s.undergraduateSchool,c).then(_=>{if(_.code===0&&_.data){const y=_.data.map(E=>({value:E.id.toString(),label:E.major_name}));V.value=y,z[t]=y}else V.value=[]}).catch(()=>{V.value=[]}).finally(()=>{j.value=!1})},300)}else V.value=[]},M=c=>{if(!c){Y.value=[];return}if(re.value=!0,console.log("搜索梦校:",c),he[c]){Y.value=he[c],re.value=!1;return}clearTimeout(ue.value),ue.value=setTimeout(()=>{ht(c).then(t=>{if(console.log("搜索梦校结果:",t),t.code===0&&t.data){const _=t.data.map(y=>({value:y.id.toString(),label:y.name}));if(Y.value=_,he[c]=_,s.targetSchool){const y=_.find(E=>E.value===s.targetSchool);y&&(s.targetSchoolName=y.label)}}else Y.value=[]}).catch(t=>{console.error("获取梦校列表失败",t),Y.value=[]}).finally(()=>{re.value=!1})},300)},K=()=>{s.major="",s.targetMajor="",V.value=[],pe.value=[]},N=[{value:"0",label:"英语一"},{value:"1",label:"英语二"}];let I=p(""),ee=p("");const fe=[{value:"0",label:"数学一"},{value:"1",label:"数学二"},{value:"2",label:"数学三"},{value:"3",label:"199管理类联考"},{value:"4",label:"396经济类联考"}],Ve=xt(()=>tl),Ce=p(!1);p(!1);const X=p(!1),le=()=>{const c=parseFloat(s.politics)||0,t=parseFloat(s.englishType)||0,_=parseFloat(s.mathType)||0,y=parseFloat(s.professional)||0;s.totalScore=(c+t+_+y).toString()},be=async()=>{if(X.value=!X.value,X.value){Ce.value=!0,window.reportData={targetMajorName:s.targetMajor,majorCode:s.majorCode,firstLevelSubject:Ae(s.majorCode)};try{const c=A.value;if(X.value=!0,P.value.show(),c)if(console.log("设置报告ID并加载AI推荐:",c),Ue.value.fetchStudyPlan(c),await P.value.setReportId(c),H.value=!1,s.majorCode){console.log("开始获取国家线数据..."),console.log(s.majorCode);try{let t=s.majorCode.split(","),_=t[0].substring(0,2);s.disciplineCategory=parseInt(_),te.value=t[0].substring(0,4);const y=await jt(te.value);ze(s.disciplineCategory),y.code===0&&y.data?(oe.value=y.data,ce.value=!0):(console.error("获取国家线数据失败:",y.msg),w.warning("获取国家线数据失败: "+(y.msg||"未知错误")))}catch(t){console.error("获取国家线数据异常:",t),w.error("获取国家线数据异常，将使用默认数据")}}else console.warn("缺少专业代码，无法获取国家线数据"),w.warning("缺少专业代码，无法获取国家线数据")}catch(c){console.error("处理过程中发生错误:",c),w.error("处理失败: "+(c.message||"未知错误")),X.value=!1,P.value.show()}}},A=p(""),Be=p("");Ct(()=>{A.value=je.params.id,Be.value=je.params.student_id,be(),De(),Pe(()=>{}),qe()});const qe=async()=>{try{const c=await At();c.code===0?Me.value=c.data:w.error(c.msg||"获取学科门类失败")}catch(c){console.error("获取学科门类失败",c),w.error("获取学科门类失败，请稍后重试")}},ze=async c=>{if(console.log("学科门类变更:",c),c)try{const t=await Et(c);if(t.code===0){ye.value=t.data;let _=ye.value.filter(y=>{if(y.value==te.value)return y});s.firstLevelDiscipline=_[0].id,console.log(_)}else w.error(t.msg||"获取一级学科失败")}catch(t){console.error("获取一级学科失败",t),w.error("获取一级学科失败，请稍后重试")}};Dt(()=>{window.removeEventListener("student-selected",De)});const De=async()=>{try{console.log("开始加载学员基本信息，报告ID:",A.value);const c=await el(A.value);if(console.log("学员基本信息API响应:",c),c.code===0&&c.data){const{student_info:t,report_info:_}=c.data;t&&(s.name=t.name||"",s.sex=t.sexText||"",s.undergraduateSchool=t.undergraduateSchoolName||"",s.undergraduateMajor=t.undergraduateMajorName||"",s.targetMajor=_.target_major||"",s.majorCode=t.majorCode||"",s.phone=t.phone||"",s.examYear=t.examYear||"",s.isMultiDisciplinary=t.isCrossMajorText||"",s.educationalStyle=t.educationalStyleText||"",s.englishScore=t.englishScore||"",s.cet4=t.cet4||"",s.cet6=t.cet6||"",s.tofelScore=t.tofelScore||"",s.ieltsScore=t.ieltsScore||"",s.undergraduateTranscript=t.undergraduateTranscript||[],s.englishLevel=t.englishAbility||""),_&&(s.politics=_.politics_score||"",s.englishS=_.english_score||"",s.englishType=_.english_type||"",s.mathType=_.math_type||"",s.professional=_.professional_score||"",s.totalScore=_.total_score||"",s.personalNeeds=_.personal_needs||"",s.weakModules=_.weak_modules||"",s.region=_.region_preference.split(",")||"",s.intendedSchools=_.province_selection.split(",")||"",s.targetSchoolName=_.dream_school||"",s.schoolLevel=_.school_level.split(",")||"",s.referenceBooks=_.reference_books||""),Oe(s),console.log("学员基本信息加载成功，已更新reportForm")}else console.warn("学员基本信息加载失败:",c.msg)}catch(c){w.error("获取报告失败"),console.error("加载学员基本信息失败:",c)}},Oe=c=>{if(console.log("填充学员数据到表单:",c),s.sex=c.sex===1?"1":c.sex===2?"2":"",s.undergraduateSchool=c.undergraduateSchoolName||c.undergraduateSchool||"",s.undergraduateMajor=c.undergraduateMajorName||c.undergraduateMajor||"",s.targetMajor=c.targetMajorName||c.targetMajor||"",c.examYear&&(s.examYear=c.examYear.toString()),s.isMultiDisciplinary=c.isCrossMajor?"1":"2",s.educationalStyle=c.educationalStyle?"1":"0",console.log("---------"),console.log(s),console.log("设置省份选择:",s.intendedSchools),s.targetSchool&&s.targetSchoolName&&(Y.value=[{value:s.targetSchool,label:s.targetSchoolName}]),s.schoolLevel=c.schoolLevel||"",s.referenceBooks=c.referenceBooks||"",c.undergraduateTranscript)try{let y=c.undergraduateTranscript;typeof y=="string"&&(y=JSON.parse(y)),Array.isArray(y)&&y.length>0&&(W.value=y.map((E,Z)=>({id:Z+1,title:E.title||E.name||`课程${Z+1}`,score:E.score||""})))}catch(y){console.error("解析undergraduateTranscript失败:",y)}W.value.length===0&&(W.value=[{id:1,title:"高数(上)",score:""},{id:2,title:"高数(下)",score:""},{id:3,title:"概率论",score:""},{id:4,title:"线性代数",score:""}]),s.politics=c.politics||"";const t=N.find(y=>y.label==c.englishType);I.value=t?t.value:"0",s.englishType=c.englishS||"";const _=fe.find(y=>y.label==c.mathType);if(ee.value=_?_.value:"0",s.mathType=c.mathScore||"",s.professional=c.professionalScore||"",s.totalScore=c.totalScore||"",le(),console.log("填充考研预估成绩:",{政治:s.politics,英语类型:I.value,英语分数:s.englishType,数学类型:ee.value,数学分数:s.mathType,专业课分数:s.professional,总分:s.totalScore}),s.politics&&s.englishType&&s.mathType&&s.professional){const y=parseFloat(s.politics)||0,E=parseFloat(s.englishType)||0,Z=parseFloat(s.mathType)||0,Se=parseFloat(s.professional)||0;s.totalScore=(y+E+Z+Se).toString()}s.personalNeeds=c.personalNeeds||"",s.weakModules=c.weakModules||""},Ye=async()=>{!A.value&&pdfStore.pdfUrl.value?window.open(pdfStore.pdfUrl.value,"_blank"):w.warning("请先预览报告生成PDF文件")},Le=p(null),We=async()=>{if(console.log(A.value),!A.value){w.warning("请先生成报告内容");return}ae.value=!0,await Pe(),await new Promise(c=>setTimeout(c,100));try{await Le.value.initPdfData(A.value,s.firstLevelDiscipline),console.log("PDF数据初始化成功，报告ID:",A.value)}catch(c){console.error("PDF数据初始化失败:",c),w.error("加载报告数据失败: "+(c.message||"未知错误")),ae.value=!1}};return(c,t)=>{const _=Ie,y=Bt,E=Lt,Z=Qe,Se=Nt,ne=Yt,He=Ot,Ge=qt;return r(),v("div",vo,[e("div",mo,[e("div",{class:"credits-info"},[t[42]||(t[42]=e("div",null,[e("span",{class:"credits-text"},"报告数："),e("span",{class:"credits-count"},"200"),e("span",{class:"credits-count"},"人次")],-1)),e("div",{class:"button-group"},[e("div",{class:"action-btn",onClick:We},"预览报告"),e("div",{class:"action-btn",onClick:Ye},"导出报告"),t[41]||(t[41]=e("div",{class:"action-btn"},"发送",-1))])]),e("div",po,[e("div",yo,[t[56]||(t[56]=e("div",{class:"step-header"},[e("div",{class:"step-title"},"第一部分：个人基础信息")],-1)),e("div",go,[t[55]||(t[55]=e("div",{class:"step-num-tag"},[e("span",null,"01"),e("div",{class:"tag-text"},"个人基础信息")],-1)),e("div",_o,[e("div",fo,[t[43]||(t[43]=e("div",{class:"item-label"},"学员姓名",-1)),u(_,{disabled:"",modelValue:s.name,"onUpdate:modelValue":t[0]||(t[0]=n=>s.name=n),placeholder:"请输入学员姓名"},null,8,["modelValue"])]),e("div",bo,[t[44]||(t[44]=e("div",{class:"item-label"},"性别",-1)),u(E,{disabled:"",modelValue:s.sex,"onUpdate:modelValue":t[1]||(t[1]=n=>s.sex=n),placeholder:"请选择性别",class:"full-width"},{default:f(()=>[u(y,{label:"男",value:"1"}),u(y,{label:"女",value:"2"}),u(y,{label:"其他",value:"3"})]),_:1},8,["modelValue"])]),e("div",ko,[t[45]||(t[45]=e("div",{class:"item-label"},"本科院校",-1)),u(E,{disabled:"",modelValue:s.undergraduateSchool,"onUpdate:modelValue":t[2]||(t[2]=n=>s.undergraduateSchool=n),placeholder:"输入关键字搜索院校",filterable:"",remote:"","reserve-keyword":"","remote-method":we,loading:se.value,class:"full-width",onChange:K},{default:f(()=>[(r(!0),v(B,null,q(G.value,n=>(r(),R(y,{key:n.value,label:n.label,value:n.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),e("div",ho,[t[46]||(t[46]=e("div",{class:"item-label"},"本科专业",-1)),u(E,{disabled:"",modelValue:s.undergraduateMajor,"onUpdate:modelValue":t[3]||(t[3]=n=>s.undergraduateMajor=n),placeholder:"输入关键字搜索专业",filterable:"",remote:"","reserve-keyword":"","remote-method":ie,loading:j.value,class:"full-width"},{default:f(()=>[(r(!0),v(B,null,q(V.value,n=>(r(),R(y,{key:n.value,label:n.label,value:n.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading","disabled"])]),e("div",$o,[t[47]||(t[47]=e("div",{class:"item-label"},"学科门类",-1)),u(E,{modelValue:s.disciplineCategory,"onUpdate:modelValue":t[4]||(t[4]=n=>s.disciplineCategory=n),placeholder:"请选择学科门类",class:"full-width",onChange:ze,disabled:"true"},{default:f(()=>[(r(!0),v(B,null,q(Me.value,n=>(r(),R(y,{key:n.id,label:n.label,value:n.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),e("div",wo,[t[48]||(t[48]=e("div",{class:"item-label"},"一级学科",-1)),u(E,{modelValue:s.firstLevelDiscipline,"onUpdate:modelValue":t[5]||(t[5]=n=>s.firstLevelDiscipline=n),placeholder:"请选择一级学科",class:"full-width",disabled:"true"},{default:f(()=>[(r(!0),v(B,null,q(ye.value,n=>(r(),R(y,{key:n.id,label:n.label,value:n.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),e("div",Vo,[t[49]||(t[49]=e("div",{class:"item-label"},"目标专业",-1)),u(_,{disabled:"",modelValue:s.targetMajor,"onUpdate:modelValue":t[6]||(t[6]=n=>s.targetMajor=n),placeholder:"请输入专业代码"},null,8,["modelValue"])]),e("div",Co,[t[50]||(t[50]=e("div",{class:"item-label"},"专业代码",-1)),u(_,{disabled:"",modelValue:s.majorCode,"onUpdate:modelValue":t[7]||(t[7]=n=>s.majorCode=n),placeholder:"请输入专业代码"},null,8,["modelValue"])]),e("div",So,[t[51]||(t[51]=e("div",{class:"item-label"},"联系方式",-1)),u(_,{disabled:"",modelValue:s.phone,"onUpdate:modelValue":t[8]||(t[8]=n=>s.phone=n),placeholder:"请输入联系方式"},null,8,["modelValue"])]),e("div",Mo,[t[52]||(t[52]=e("div",{class:"item-label"},"考研年份",-1)),u(E,{disabled:"",modelValue:s.examYear,"onUpdate:modelValue":t[9]||(t[9]=n=>s.examYear=n),placeholder:"请选择考研年份",class:"full-width"},{default:f(()=>[u(y,{label:"2027",value:"2027"}),u(y,{label:"2026",value:"2026"})]),_:1},8,["modelValue"])]),e("div",xo,[t[53]||(t[53]=e("div",{class:"item-label"},"跨专业",-1)),u(E,{disabled:"",modelValue:s.isMultiDisciplinary,"onUpdate:modelValue":t[10]||(t[10]=n=>s.isMultiDisciplinary=n),placeholder:"请选择跨专业",class:"full-width"},{default:f(()=>[u(y,{label:"是",value:"1"}),u(y,{label:"否",value:"2"})]),_:1},8,["modelValue"])]),e("div",To,[t[54]||(t[54]=e("div",{class:"item-label"},"培养方式",-1)),u(E,{disabled:"",modelValue:s.educationalStyle,"onUpdate:modelValue":t[11]||(t[11]=n=>s.educationalStyle=n),placeholder:"请选择培养方式",class:"full-width"},{default:f(()=>[u(y,{label:"全日制",value:"0"}),u(y,{label:"非全日制",value:"1"})]),_:1},8,["modelValue"])])])])]),e("div",jo,[e("div",Uo,[e("div",Ao,[t[58]||(t[58]=e("div",{class:"step-num-tag"},[e("span",null,"02"),e("div",{class:"tag-text"},"本科成绩情况")],-1)),T("",!0)]),e("div",Eo,[e("div",Ro,[(r(!0),v(B,null,q(me(W),n=>(r(),v("div",{class:"score-item",key:n.id,draggable:!0,onDragstart:de=>C(de,n),onDragover:t[12]||(t[12]=Ft(de=>S(de),["prevent"])),onDrop:de=>U(de,n),onDragend:m},[e("div",zo,$(n.title),1),e("div",Do,[u(_,{readonly:"",modelValue:n.score,"onUpdate:modelValue":de=>n.score=de,placeholder:"140"},null,8,["modelValue","onUpdate:modelValue"])])],40,Po))),128))])])])]),e("div",Lo,[e("div",Fo,[t[65]||(t[65]=e("div",{class:"step-num-tag"},[e("span",null,"03"),e("div",{class:"tag-text"},"英语基础")],-1)),e("div",No,[e("div",Bo,[e("div",qo,[t[59]||(t[59]=e("div",{class:"english-label"},"高考英语成绩",-1)),u(_,{readonly:"",modelValue:s.englishScore,"onUpdate:modelValue":t[13]||(t[13]=n=>s.englishScore=n),placeholder:"请输入高考英语成绩",class:"full-width"},null,8,["modelValue"])]),e("div",Oo,[t[60]||(t[60]=e("div",{class:"english-label"},"大学四级",-1)),u(_,{readonly:"",modelValue:s.cet4,"onUpdate:modelValue":t[14]||(t[14]=n=>s.cet4=n),placeholder:"请输入大学四级成绩",class:"full-width"},null,8,["modelValue"])]),e("div",Yo,[t[61]||(t[61]=e("div",{class:"english-label"},"大学六级",-1)),u(_,{readonly:"",modelValue:s.cet6,"onUpdate:modelValue":t[15]||(t[15]=n=>s.cet6=n),placeholder:"请输入大学六级成绩",class:"full-width"},null,8,["modelValue"])]),e("div",Wo,[t[62]||(t[62]=e("div",{class:"english-label"},"托福",-1)),u(_,{readonly:"",modelValue:s.tofelScore,"onUpdate:modelValue":t[16]||(t[16]=n=>s.tofelScore=n),placeholder:"请输入托福成绩",class:"full-width"},null,8,["modelValue"])])]),e("div",Ho,[e("div",Go,[t[63]||(t[63]=e("div",{class:"english-label"},"雅思",-1)),u(_,{readonly:"",modelValue:s.ieltsScore,"onUpdate:modelValue":t[17]||(t[17]=n=>s.ieltsScore=n),placeholder:"请输入雅思成绩",class:"full-width"},null,8,["modelValue"])]),e("div",Jo,[t[64]||(t[64]=e("div",{class:"english-label"},"英语能力",-1)),u(E,{disabled:"",modelValue:s.englishLevel,"onUpdate:modelValue":t[18]||(t[18]=n=>s.englishLevel=n),placeholder:"请选择英语能力",class:"full-width"},{default:f(()=>[u(y,{label:"一般",value:"average"}),u(y,{label:"良好",value:"good"}),u(y,{label:"优秀",value:"excellent"})]),_:1},8,["modelValue"])])])])])]),e("div",Ko,[e("div",Xo,[t[71]||(t[71]=e("div",{class:"step-num-tag"},[e("span",null,"04"),e("div",{class:"tag-text"},"目标院校梯度")],-1)),e("div",Zo,[e("div",Qo,[t[66]||(t[66]=e("div",{class:"item-label"},"地区倾向",-1)),u(E,{disabled:"",modelValue:s.region,"onUpdate:modelValue":t[19]||(t[19]=n=>s.region=n),placeholder:"请选择地区倾向",multiple:"",class:"full-width"},{default:f(()=>[u(y,{label:"A区",value:"A区"}),u(y,{label:"B区",value:"B区"})]),_:1},8,["modelValue"])]),e("div",Io,[t[67]||(t[67]=e("div",{class:"item-label"},"省份选择",-1)),u(E,{disabled:"",modelValue:s.intendedSchools,"onUpdate:modelValue":t[20]||(t[20]=n=>s.intendedSchools=n),placeholder:"请选择省份",multiple:"",class:"full-width"},{default:f(()=>[(r(!0),v(B,null,q(Ve.value,n=>(r(),R(y,{key:n.value,label:n.label,value:n.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),e("div",ei,[t[68]||(t[68]=e("div",{class:"item-label"},"梦校",-1)),u(E,{disabled:"",modelValue:s.targetSchoolName,"onUpdate:modelValue":t[21]||(t[21]=n=>s.targetSchoolName=n),filterable:"",remote:"","reserve-keyword":"",placeholder:"输入关键字搜索院校","remote-method":M,loading:re.value,class:"full-width",onChange:t[22]||(t[22]=n=>{if(n){const de=Y.value.find(Je=>Je.value===n);de&&(s.targetSchoolName=de.label)}else s.targetSchoolName=""})},{default:f(()=>[(r(!0),v(B,null,q(Y.value,n=>(r(),R(y,{key:n.value,label:n.label,value:n.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),e("div",ti,[t[69]||(t[69]=e("div",{class:"item-label"},"院校层次",-1)),u(E,{disabled:"",modelValue:s.schoolLevel,"onUpdate:modelValue":t[23]||(t[23]=n=>s.schoolLevel=n),placeholder:"请选择院校层次",multiple:"",class:"full-width"},{default:f(()=>[u(y,{label:"985",value:"985"}),u(y,{label:"211",value:"211"}),u(y,{label:"双一流",value:"双一流"}),u(y,{label:"双非",value:"双非"})]),_:1},8,["modelValue"])]),e("div",li,[t[70]||(t[70]=e("div",{class:"item-label"},"专业课指定参考书",-1)),u(_,{readonly:"",modelValue:s.referenceBooks,"onUpdate:modelValue":t[24]||(t[24]=n=>s.referenceBooks=n),placeholder:"请输入专业课指定参考书"},null,8,["modelValue"])])])])]),e("div",si,[e("div",ai,[t[77]||(t[77]=e("div",{class:"step-num-tag"},[e("span",null,"05"),e("div",{class:"tag-text"},"考研成绩预估")],-1)),e("div",oi,[e("div",ii,[t[72]||(t[72]=e("div",{class:"th-cell"},"政治",-1)),e("div",ni,[u(E,{disabled:"",class:"sel-no-border center-select",modelValue:me(I),"onUpdate:modelValue":t[25]||(t[25]=n=>wt(I)?I.value=n:I=n),placeholder:"请选择英语",size:"large","popper-class":"center-select-dropdown"},{default:f(()=>[(r(),v(B,null,q(N,n=>u(y,{key:n.value,label:n.label,value:n.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),e("div",di,[u(E,{disabled:"",modelValue:me(ee),"onUpdate:modelValue":t[26]||(t[26]=n=>wt(ee)?ee.value=n:ee=n),placeholder:"请选择数学",size:"large",class:"sel-no-border center-select","popper-class":"center-select-dropdown"},{default:f(()=>[(r(),v(B,null,q(fe,n=>u(y,{key:n.value,label:n.label,value:n.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),t[73]||(t[73]=e("div",{class:"th-cell"},"专业课",-1)),t[74]||(t[74]=e("div",{class:"th-cell"},"总分",-1))]),e("div",ri,[e("div",ui,[u(_,{readonly:"",modelValue:s.politics,"onUpdate:modelValue":t[27]||(t[27]=n=>s.politics=n),class:"table-input",onInput:le},null,8,["modelValue"])]),e("div",ci,[u(_,{modelValue:s.englishType,"onUpdate:modelValue":t[28]||(t[28]=n=>s.englishType=n),class:"table-input",onInput:le},null,8,["modelValue"])]),e("div",vi,[u(_,{readonly:"",modelValue:s.mathType,"onUpdate:modelValue":t[29]||(t[29]=n=>s.mathType=n),class:"table-input",onInput:le},null,8,["modelValue"])]),e("div",mi,[u(_,{readonly:"",modelValue:s.professional,"onUpdate:modelValue":t[30]||(t[30]=n=>s.professional=n),class:"table-input",onInput:le},null,8,["modelValue"])]),e("div",pi,[u(_,{readonly:"",modelValue:s.totalScore,"onUpdate:modelValue":t[31]||(t[31]=n=>s.totalScore=n),class:"table-input"},null,8,["modelValue"])])])]),e("div",yi,[t[75]||(t[75]=e("div",{class:"demands-label"},"个性化需求",-1)),u(_,{readonly:"",modelValue:s.personalNeeds,"onUpdate:modelValue":t[32]||(t[32]=n=>s.personalNeeds=n),type:"textarea",rows:1,placeholder:"请输入个性化需求",class:"demands-input"},null,8,["modelValue"])]),e("div",gi,[t[76]||(t[76]=e("div",{class:"advice-label"},"薄弱模块",-1)),u(_,{readonly:"",modelValue:s.weakModules,"onUpdate:modelValue":t[33]||(t[33]=n=>s.weakModules=n),type:"textarea",rows:1,placeholder:"请输入薄弱模块",class:"advice-input"},null,8,["modelValue"])])])]),X.value?(r(),v("div",{key:0,class:"ai-overlay",onClick:be},[e("div",_i,[e("div",fi,[e("div",{class:Xe(["ai-icon-layer outer",{"animate-outer":!0}])}),e("div",{class:Xe(["ai-icon-layer middle",{"animate-middle":!0}])}),e("div",{class:Xe(["ai-icon-layer inner",{"animate-inner":!0}])})])])])):T("",!0)]),u(ll,{ref:"majorAnalysisRef","national-line-data":oe.value,"report-data":{targetMajorName:s.targetMajor,majorCode:s.majorCode,firstLevelSubject:Ae(s.majorCode)},visible:ce.value},null,8,["national-line-data","report-data","visible"]),u(Zs,{ref_key:"reportContentElement",ref:P,onUpdateOverlay:b},null,512),u(co,{ref_key:"weakModuleAnalysisRef",ref:Ue},null,512)]),u(Se,{modelValue:ve.value,"onUpdate:modelValue":t[35]||(t[35]=n=>ve.value=n),title:"报告预览",width:"90%",top:"5vh","close-on-click-modal":!1,class:"preview-dialog"},{footer:f(()=>[e("div",$i,[u(Z,{onClick:t[34]||(t[34]=n=>ve.value=!1)},{default:f(()=>t[79]||(t[79]=[x("关闭")])),_:1}),u(Z,{type:"primary",onClick:c.generatePDFFromPreview},{default:f(()=>t[80]||(t[80]=[x("生成PDF")])),_:1},8,["onClick"])])]),default:f(()=>[e("div",{class:"preview-container",ref_key:"previewContainer",ref:$e},[ge.value?xe((r(),v("div",bi,null,512)),[[Ge,ge.value]]):_e.value?(r(),v("div",{key:1,innerHTML:_e.value,class:"preview-content"},null,8,ki)):(r(),v("div",hi,t[78]||(t[78]=[e("p",null,"暂无预览内容",-1)])))],512)]),_:1},8,["modelValue"]),u(Se,{title:"预览报告",modelValue:ae.value,"onUpdate:modelValue":t[36]||(t[36]=n=>ae.value=n),width:"860px","close-on-click-modal":!1},{default:f(()=>[u(sl,{ref_key:"previewPdfRef",ref:Le},null,512)]),_:1},8,["modelValue"]),u(Se,{title:"添加成绩项",modelValue:Q.value,"onUpdate:modelValue":t[40]||(t[40]=n=>Q.value=n),width:"30%","close-on-click-modal":!1},{footer:f(()=>[e("span",wi,[u(Z,{onClick:t[39]||(t[39]=n=>Q.value=!1)},{default:f(()=>t[81]||(t[81]=[x("取消")])),_:1}),u(Z,{type:"primary",style:{"background-color":"#1bb394"},onClick:D},{default:f(()=>t[82]||(t[82]=[x("确认")])),_:1})])]),default:f(()=>[u(He,{model:J,"label-width":"80px"},{default:f(()=>[u(ne,{label:"科目名称"},{default:f(()=>[u(_,{modelValue:J.title,"onUpdate:modelValue":t[37]||(t[37]=n=>J.title=n),placeholder:"请输入科目名称"},null,8,["modelValue"])]),_:1}),u(ne,{label:"成绩"},{default:f(()=>[u(_,{modelValue:J.score,"onUpdate:modelValue":t[38]||(t[38]=n=>J.score=n),placeholder:"请输入成绩"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},Fi=Ze(Vi,[["__scopeId","data-v-ea77a972"]]);export{Fi as default};
