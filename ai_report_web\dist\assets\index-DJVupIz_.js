import{_ as x}from"./_plugin-vue_export-helper-BCEt7Ct6.js";/* empty css                     *//* empty css                  *//* empty css                 */import{u as V,r as m,a as b,c as n,o as d,b as a,d as k,e as s,w as l,E,f as C,g as U,h as B,i as F,j as H,k as g}from"./index-Cu_gl4pu.js";const I={class:"login-container"},M={class:"login-wrapper"},R={class:"login-card"},S={key:0},z={key:1},N={__name:"index",setup(Z){const v=C(),_=V(),c=m(null),o=m(!1),t=b({username:"",password:""}),w={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"长度在 3 到 20 个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"长度在 6 到 20 个字符",trigger:"blur"}]},u=async()=>{o.value||await c.value.validate(async f=>{if(f)try{o.value=!0;const e=await _.login(t);g({message:e.msg||"登录成功",type:"success"}),v.push({path:"/"})}catch(e){g.error(e.message||"登录失败")}finally{o.value=!1}})};return(f,e)=>{const p=B,r=U,h=H,y=E;return d(),n("div",I,[e[4]||(e[4]=a("div",{class:"background-decoration"},[a("div",{class:"circle circle-1"}),a("div",{class:"circle circle-2"}),a("div",{class:"circle circle-3"})],-1)),a("div",M,[a("div",R,[e[2]||(e[2]=k('<div class="header-section" data-v-c44aff72><div class="logo" data-v-c44aff72><svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-c44aff72><circle cx="24" cy="24" r="20" fill="url(#gradient)" data-v-c44aff72></circle><path d="M24 8C30.6274 8 36 13.3726 36 20C36 26.6274 30.6274 32 24 32C17.3726 32 12 26.6274 12 20C12 13.3726 17.3726 8 24 8Z" fill="white" opacity="0.3" data-v-c44aff72></path><path d="M20 18H28V22H20V18Z" fill="white" data-v-c44aff72></path><path d="M16 24H32V28H16V24Z" fill="white" data-v-c44aff72></path><defs data-v-c44aff72><linearGradient id="gradient" x1="4" y1="4" x2="44" y2="44" gradientUnits="userSpaceOnUse" data-v-c44aff72><stop stop-color="#1bb394" data-v-c44aff72></stop><stop offset="1" stop-color="#16a085" data-v-c44aff72></stop></linearGradient></defs></svg></div><div class="title" data-v-c44aff72>AI择校报告管理系统</div><div class="subtitle" data-v-c44aff72>智能化择校决策支持平台</div></div>',1)),s(y,{ref_key:"loginFormRef",ref:c,model:t,rules:w,class:"login-form"},{default:l(()=>[s(r,{prop:"username"},{default:l(()=>[s(p,{modelValue:t.username,"onUpdate:modelValue":e[0]||(e[0]=i=>t.username=i),placeholder:"请输入用户名","prefix-icon":"User",size:"large",class:"custom-input"},null,8,["modelValue"])]),_:1}),s(r,{prop:"password"},{default:l(()=>[s(p,{modelValue:t.password,"onUpdate:modelValue":e[1]||(e[1]=i=>t.password=i),type:"password",placeholder:"请输入密码","prefix-icon":"Lock",size:"large",class:"custom-input","show-password":"",onKeyup:F(u,["enter"])},null,8,["modelValue"])]),_:1}),s(r,null,{default:l(()=>[s(h,{loading:o.value,type:"primary",size:"large",class:"login-button",onClick:u},{default:l(()=>[o.value?(d(),n("span",z,"登录中...")):(d(),n("span",S,"登录系统"))]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"]),e[3]||(e[3]=a("div",{class:"footer-info"},[a("p",null,"安全登录 · 数据加密")],-1))])])])}}},A=x(N,[["__scopeId","data-v-c44aff72"]]);export{A as default};
