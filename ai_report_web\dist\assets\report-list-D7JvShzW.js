import{_ as me}from"./_plugin-vue_export-helper-BCEt7Ct6.js";import{a as pe,b as fe}from"./school-BRpLdP4V.js";/* empty css                   *//* empty css                        */import{b as _e}from"./student-Db39rDv4.js";/* empty css                 *//* empty css                  *//* empty css                        */import{_ as A}from"./tag-Ch_qj8li.js";import{m as ge,n as be,o as he,p as Ce}from"./report-CU5jWwkq.js";import{a as ke,r as s,n as ye,c as b,o as v,b as l,e as i,d as we,t as je,O as Ve,w as d,Z as Se,j as xe,p as Me,v as Ee,P as w,a1 as Le,a2 as $e,D as ze,k as j,f as De,G as Ne,F as D,s as N,$ as Re,q as R,l as y,M as Te,a3 as Be}from"./index-Cu_gl4pu.js";const Fe={class:"student-list-container"},Ie={class:"stats-cards"},Pe={class:"stat-card left-card"},Ue={class:"stat-content"},Oe={class:"stat-value"},Qe={class:"search-section"},Ye={class:"search-form"},Ae={class:"form-item"},Ge={class:"form-item"},He={class:"form-item"},Ke={key:1,class:"loading-option"},Ze={class:"form-item grade_class"},Je={class:"form-item",style:{"margin-left":"100px"}},We={class:"student-list-section"},Xe=["onClick"],qe=["onClick"],ea=["onClick"],aa={class:"pagination-container"},la={id:"canvas"},ta=["src"],oa={class:"dialog-footer"},sa={__name:"report-list",setup(na){const G=De(),n=ke({name:"",school_name:"",major_name:"",class:"",page:1,limit:7}),T=s([]),V=s(!1),B=s(0),H=s(10),S=s(1),F=s(0),x=s(""),K=async()=>{try{const a=await he();a.code===0&&a.data&&(F.value=a.data.count)}catch(a){console.error("获取报告数量失败:",a),j.error("获取报告数量失败")}};s(null);const C=s(!1),Z=async a=>{if(C.value=!0,a.idcode!=""&&a.idcode!=null)x.value="https://oa.yanqux.com/qrcode/build?text=https://report.yanqukaoyan.com/sreport/"+a.idcode;else{const{code:e,data:r}=await Ce({report_id:a.id});console.log("生成二维码成功:",r),e==0?x.value="https://oa.yanqux.com/qrcode/build?text=https://report.yanqukaoyan.com/sreport/"+r:j.error(r.msg)}},J=()=>{C.value=!1},W=(a,e)=>{if(a.length<1){e([]);return}_e({name:a,page:1,limit:50}).then(r=>{const o=r.data.map(c=>({value:c.name})).filter(c=>c.value.includes(a));e(o)})},X=a=>{n.name=a.value},p=s([]),M=s(!1),k=s({}),q=async a=>{if(a.length<1){p.value=[];return}if(k.value[a]){p.value=k.value[a];return}M.value=!0;try{const e=await fe(a);if(console.log("搜索学校结果:",e),e&&e.code===0&&e.data){const r=e.data.map(o=>({value:o.id.toString(),label:o.name}));p.value=r,k.value[a]=r}else{const r=[{value:"1",label:"中国科学技术大学"},{value:"2",label:"北京大学"},{value:"3",label:"清华大学"},{value:"4",label:"复旦大学"},{value:"5",label:"南京大学"}].filter(o=>o.label.includes(a));p.value=r,k.value[a]=r}}catch(e){console.error("获取学校数据失败:",e);const r=[{value:"1",label:"中国科学技术大学"},{value:"2",label:"北京大学"},{value:"3",label:"清华大学"}].filter(o=>o.label.includes(a));p.value=r,k.value[a]=r}finally{M.value=!1}},u=s([]);s(!1);const E=s({}),m=s(1),f=s(!1),I=s(!1),_=s(!1),P=s(null),L=s(""),ee=()=>{if(!n.school_name){j.warning("请先选择本科院校");return}(!I.value||u.value.length===0)&&(m.value=1,$(""))},ae=()=>{f.value&&!_.value&&(m.value+=1,$(L.value))},$=a=>{if(!n.school_name){j.warning("请先选择本科院校");return}a!==L.value&&(m.value=1,u.value=[],f.value=!1,L.value=a),_.value=!0;const e=`${n.school_name}_${a||""}_${m.value}`;if(E[e]){const o=E[e];m.value===1?u.value=o.data:u.value=[...u.value,...o.data],f.value=o.hasMore,_.value=!1;return}clearTimeout(P.value);const r=p.value.find(o=>o.label===n.school_name).value;P.value=setTimeout(()=>{pe(r,a||"",m.value).then(o=>{if(o.code===0&&o.data){const c=o.data.map(g=>({value:g.id.toString(),label:g.major_name}));m.value===1?u.value=c:u.value=[...u.value,...c],f.value=o.pagination?o.pagination.has_more:!1,E[e]={data:c,hasMore:f.value}}else m.value===1&&(u.value=[]),f.value=!1}).catch(()=>{m.value===1&&(u.value=[]),f.value=!1}).finally(()=>{_.value=!1,I.value=!0})},300)},U=s("table"),le=()=>{U.value=U.value==="table"?"grid":"table"},z=async()=>{V.value=!0,await be(n).then(a=>{T.value=a.data.data,B.value=a.data.total,V.value=!1})},O=()=>{S.value=1,z()},te=()=>{n.page=1,n.name="",n.school_name="",n.major_name="",n.class="",p.value=[],u.value=[],O()},oe=(a,e)=>{console.log(a),G.push({name:"editReport",params:{id:a,student_id:e}})};ye(()=>{z(),se(),K()});const Q=s([]),se=()=>{ge().then(a=>{console.log(a),Q.value=a.data})},ne=a=>{S.value=a,n.page=a,z()};return(a,e)=>{const r=Ve,o=Re,c=Se,g=xe,re=Ne("Loading"),ie=Te,h=Be,ue=Le,de=$e,ce=ze,ve=Ee;return v(),b("div",Fe,[l("div",Ie,[l("div",Pe,[l("div",Ue,[e[6]||(e[6]=l("span",{class:"stat-label"},"生成报告数：",-1)),l("span",Oe,je(F.value)+"人次",1)])]),e[7]||(e[7]=we('<div class="stat-card right-card" data-v-34bd55fe><div class="stat-content" data-v-34bd55fe><span class="stat-label" data-v-34bd55fe>容量：</span><span class="stat-value" data-v-34bd55fe>1000人次</span></div></div><div class="tip-card" data-v-34bd55fe></div>',2))]),l("div",Qe,[e[17]||(e[17]=l("div",{class:"section-header"},[l("img",{src:A,alt:"标签图标"}),l("span",{class:"section-title"},"学员搜索")],-1)),l("div",Ye,[l("div",Ae,[e[8]||(e[8]=l("span",{class:"item-label"},"姓名",-1)),i(r,{modelValue:n.name,"onUpdate:modelValue":e[0]||(e[0]=t=>n.name=t),"fetch-suggestions":W,placeholder:"张三",clearable:"","trigger-on-focus":!1,onSelect:X,class:"centered-placeholder"},null,8,["modelValue"])]),l("div",Ge,[e[9]||(e[9]=l("span",{class:"item-label"},"本科院校",-1)),i(c,{modelValue:n.school_name,"onUpdate:modelValue":e[1]||(e[1]=t=>n.school_name=t),placeholder:"请输入本科院校",clearable:"",filterable:"",remote:"","remote-method":q,loading:M.value},{default:d(()=>[(v(!0),b(D,null,N(p.value,t=>(v(),w(o,{key:t.value,label:t.label,value:t.label},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),l("div",He,[e[12]||(e[12]=l("span",{class:"item-label"},"本科专业",-1)),i(c,{modelValue:n.major_name,"onUpdate:modelValue":e[2]||(e[2]=t=>n.major_name=t),placeholder:"输入关键字搜索专业",filterable:"",remote:"","reserve-keyword":"","remote-method":$,loading:_.value,class:"full-width",disabled:!n.school_name,onFocus:ee,"popper-class":"major-select-dropdown"},{default:d(()=>[(v(!0),b(D,null,N(u.value,t=>(v(),w(o,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128)),f.value&&!_.value?(v(),b("div",{key:0,class:"load-more-option",onClick:ae},[i(g,{style:{color:"#1bb394","padding-left":"20px"},type:"text",size:"small"},{default:d(()=>e[10]||(e[10]=[y("点击加载更多...")])),_:1})])):R("",!0),_.value&&m.value>1?(v(),b("div",Ke,[i(ie,null,{default:d(()=>[i(re)]),_:1}),e[11]||(e[11]=y(" 加载中... "))])):R("",!0)]),_:1},8,["modelValue","loading","disabled"])]),l("div",Ze,[e[13]||(e[13]=l("span",{class:"item-label"},"年级",-1)),i(c,{modelValue:n.class,"onUpdate:modelValue":e[3]||(e[3]=t=>n.class=t),placeholder:"2023",clearable:""},{default:d(()=>[(v(!0),b(D,null,N(Q.value,t=>(v(),w(o,{key:t.value,label:t.label,value:t.label},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),l("div",Je,[i(g,{type:"primary",class:"search-button",onClick:O},{default:d(()=>e[14]||(e[14]=[y("查询")])),_:1}),i(g,{plain:"",class:"reset-button",onClick:te},{default:d(()=>e[15]||(e[15]=[y("重置")])),_:1})]),l("span",{class:"grid-display",onClick:le},e[16]||(e[16]=[l("i",{class:"el-icon-menu"},null,-1)]))])]),l("div",We,[e[18]||(e[18]=l("div",{class:"section-header"},[l("img",{src:A,alt:"标签图标"}),l("span",{class:"section-title"},"学员列表")],-1)),Me((v(),w(ue,{data:T.value,style:{width:"90%"},class:"custom-table"},{default:d(()=>[i(h,{prop:"id",label:"序号",width:"160",align:"center"}),i(h,{prop:"name",label:"姓名",width:"180",align:"center"}),i(h,{prop:"year",label:"年份",width:"180",align:"center"}),i(h,{prop:"undergraduate",label:"本科院校",width:"240",align:"center"}),i(h,{prop:"major",label:"本科专业",align:"center",width:"240"}),i(h,{label:"操作",align:"center"},{default:d(t=>[t.row.pdf_url?(v(),b("span",{key:0,onClick:Y=>a.handlePreview(t.row.pdf_url),class:"action-button up-button",size:"small"},"下载",8,Xe)):R("",!0),l("span",{class:"action-button edit-button",onClick:Y=>oe(t.row.id,t.row.student_id),size:"small"},"修改",8,qe),l("span",{class:"action-button delete-button",onClick:Y=>Z(t.row),size:"small"},"二维码",8,ea)]),_:1})]),_:1},8,["data"])),[[ve,V.value]]),l("div",aa,[i(de,{background:"",layout:" total, prev, pager, next, jumper",total:B.value,"page-size":H.value,"current-page":S.value,onCurrentChange:ne},null,8,["total","page-size","current-page"])])]),i(ce,{modelValue:C.value,"onUpdate:modelValue":e[5]||(e[5]=t=>C.value=t),title:"择校报告二维码",width:"500","before-close":J},{footer:d(()=>[l("div",oa,[i(g,{onClick:e[4]||(e[4]=t=>C.value=!1)},{default:d(()=>e[19]||(e[19]=[y("关闭")])),_:1})])]),default:d(()=>[l("div",la,[l("img",{src:x.value,alt:""},null,8,ta)])]),_:1},8,["modelValue"])])}}},ba=me(sa,[["__scopeId","data-v-34bd55fe"]]);export{ba as default};
