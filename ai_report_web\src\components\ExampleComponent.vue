<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/store/modules/user'

// 定义 props 类型
interface Props {
  title: string
  showHeader?: boolean
  items?: string[]
}

// 使用 defineProps 并指定类型
const props = defineProps<Props>()

// 使用 defineEmits 并指定类型
const emit = defineEmits<{
  (e: 'update', value: string): void
  (e: 'delete', id: number): void
}>()

// 响应式数据
const count = ref<number>(0)
const message = ref<string>('')

// 计算属性
const doubleCount = computed<number>(() => count.value * 2)

// 方法
const increment = (): void => {
  count.value++
  emit('update', `Count updated to ${count.value}`)
}

// 使用 Pinia store
const userStore = useUserStore()
const nickname = computed<string>(() => userStore.nickname)

// 生命周期钩子
onMounted(() => {
  message.value = `Component mounted with title: ${props.title}`
  console.log('Component mounted')
})
</script>

<template>
  <div class="example-component">
    <h2 v-if="showHeader">{{ title }}</h2>
    <p>Count: {{ count }} (Double: {{ doubleCount }})</p>
    <button @click="increment">Increment</button>
    
    <div v-if="items && items.length > 0">
      <h3>Items:</h3>
      <ul>
        <li v-for="(item, index) in items" :key="index">{{ item }}</li>
      </ul>
    </div>
    
    <p v-if="nickname">Welcome, {{ nickname }}!</p>
    
    <p>{{ message }}</p>
  </div>
</template>

<style scoped>
.example-component {
  padding: 20px;
  border: 1px solid #eee;
  border-radius: 8px;
  margin: 20px 0;
}
</style>
