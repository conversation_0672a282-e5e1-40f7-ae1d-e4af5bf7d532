<?php
use Webman\Route;

// 学校和专业相关接口
Route::group("/api", function () {
    // 学校搜索
    Route::get('/school/search', [app\controller\SchoolController::class, 'search']);
    Route::get('/school/major/search', [app\controller\SchoolController::class, 'searchSchoolMajor']);
    
    // 专业搜索
    Route::get('/major/search', [app\controller\SchoolController::class, 'searchMajor']);
    
    // 考试年份
    Route::get('/exam/years', [app\controller\SchoolController::class, 'getExamYears']);
})->middleware([app\middleware\JwtMiddleware::class]); 