<?php

namespace app\common\utils;

use QCloud\COSSTS\Sts;
use Exception;

class CosUploader
{

    // 获取单一文件上传权限的临时密钥
    public static function getKeyAndCredentials($filename)
    {

        try {
            // 上传文件可控制类型、大小，按需开启
            $permission = config("cos.permission");
            $condition = array();


            // 客户端传进原始文件名，这里根据文件后缀生成随机 Key
            $ext = pathinfo($filename, PATHINFO_EXTENSION);


            // 1. 限制上传文件后缀
            if ($permission['limitExt']) {
                if ($ext === '' || array_key_exists($ext, $permission['extWhiteList'])) {
                    throw new Exception('上传文件类型不允许');
                }
            }


            // 2. 限制上传文件 content-type
            if ($permission['limitContentType']) {
                // 只允许上传 content-type 为图片类型
                $condition['string_like'] = array('cos:content-type' => 'image/*');
            }


            // 3. 限制上传文件大小
            if ($permission['limitContentLength']) {
                $condition['numeric_less_than_equal'] = array('cos:content-length' => config('cos.max_length'));
            }


            $cosKey = self::generateCosKey($ext);
            $config = array(
                'url' => 'https://sts.tencentcloudapi.com/', // url和domain保持一致
                'domain' => 'sts.tencentcloudapi.com', // 域名，非必须，默认为 sts.tencentcloudapi.com
                'proxy' => '',
                'secretId' => config('cos.secret_id'), // 固定密钥,若为明文密钥，请直接以'xxx'形式填入，不要填写到getenv()函数中
                'secretKey' => config('cos.secret_key'), // 固定密钥,若为明文密钥，请直接以'xxx'形式填入，不要填写到getenv()函数中
                'bucket' => config('cos.bucket'), // 换成你的 bucket
                'region' => config('cos.region'), // 换成 bucket 所在园区
                'durationSeconds' => 1800, // 密钥有效期
                'allowPrefix' => array($cosKey), // 只分配当前 key 的路径权限
                // 密钥的权限列表。简单上传和分片需要以下的权限，其他权限列表请看 https://cloud.tencent.com/document/product/436/31923
                'allowActions' => array(
                    // 简单上传 
                    'name/cos:PutObject',
                    // 分片上传
                    'name/cos:InitiateMultipartUpload',
                    'name/cos:ListMultipartUploads',
                    'name/cos:ListParts',
                    'name/cos:UploadPart',
                    'name/cos:CompleteMultipartUpload'
                ),
                "resource"=>'*',            
            );


            if (!empty($condition)) {
                $config['condition'] = $condition;
            }


            $sts = new Sts();
            $tempKeys = $sts->getTempKeys($config);
            $resTemp = array(
                'TmpSecretId' => $tempKeys['credentials']['tmpSecretId'],
                'TmpSecretKey' => $tempKeys['credentials']['tmpSecretKey'],
                'SessionToken' => $tempKeys['credentials']['sessionToken'],
                'StartTime' => time(),
                'ExpiredTime' => $tempKeys['expiredTime'],
                'Bucket' => config('cos.bucket'),
                'Region' => config('cos.region'),
                'Key' => $cosKey,
            );
        } catch (Exception $e) {
            throw $e;
        }
        return $resTemp;
    }

    private static function generateCosKey($ext)
    {
        $ymd = date('Ymd');
        $r = substr('000000' . rand(), -6);
        $cosKey = 'file/' . $ymd . '/' . $ymd . '_' . $r;
        if ($ext) {
            $cosKey = $cosKey . '.' . $ext;
        }
        return $cosKey;
    }
}
