package controllers

import (
	"bufio"
	"bytes"
	"encoding/json"
	"fmt"
	"go-api/config"
	"go-api/model"
	"io"
	"log"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type User struct {
}

func (*User) AiStream(ctx *gin.Context) {
	reportId := ctx.Query("report_id")
	if reportId == "" {
		ctx.JSON(400, gin.H{"msg": "缺少报告ID"})
		return
	}

	reportIdInt, err := strconv.Atoi(reportId)
	if err != nil {
		ctx.JSON(400, gin.H{"msg": "报告ID格式错误"})
		return
	}

	// 获取上下文
	context, err := (&model.BaSchoolReport{}).GetContextByID(uint(reportIdInt))
	if err != nil || context == "" {
		ctx.JSON(400, gin.H{"msg": "请先生成报告"})
		return
	}

	// 设置SSE响应头
	ctx.Header("Content-Type", "text/event-stream")
	ctx.Header("Cache-Control", "no-cache")
	ctx.Header("Connection", "keep-alive")
	ctx.Header("X-Accel-Buffering", "no")

	// 发送开始信号
	fmt.Fprintf(ctx.Writer, "data: start\n\n")
	ctx.Writer.Flush()

	// 调用千问API进行流式处理
	err = streamQianwenApi(context, func(chunk string) {
		if chunk == "done" {
			ctx.SSEvent("end", "done")
			ctx.Writer.Flush()
			return
		}
		//log.Printf("流式输出: %s", chunk)
		// 发送数据块
		//fmt.Fprintf(ctx.Writer, "data: %s\n\n", chunk)
		ctx.SSEvent("message", chunk)

		//ctx.Writer.WriteString(chunk)
		ctx.Writer.Flush()
	})

	if err != nil {
		fmt.Fprintf(ctx.Writer, "event: error\ndata: %s\n\n", fmt.Sprintf("生成失败: %s", err.Error()))
		ctx.Writer.Flush()
	}
}

// QianwenRequest 千问API请求结构
type QianwenRequest struct {
	Input      QianwenInput      `json:"input"`
	Parameters QianwenParameters `json:"parameters"`
}

type QianwenInput struct {
	Prompt string `json:"prompt"`
}

type QianwenParameters struct {
	IncrementalOutput bool `json:"incremental_output"`
}

// QianwenResponse 千问API响应结构
type QianwenResponse struct {
	Output QianwenOutput `json:"output"`
}

type QianwenOutput struct {
	Text string `json:"text"`
}

// streamQianwenApi 调用千问API进行流式处理
func streamQianwenApi(context string, callback func(string)) error {
	// 获取配置信息
	appId := config.GlobalConfig.AliQwRecommend.Appid
	apiKey := config.GlobalConfig.AliQwRecommend.Key

	if appId == "" || apiKey == "" {
		return fmt.Errorf("千问API配置信息不完整")
	}

	url := fmt.Sprintf("https://dashscope.aliyuncs.com/api/v1/apps/%s/completion", appId)

	// 构建请求数据
	requestData := QianwenRequest{
		Input: QianwenInput{
			Prompt: context,
		},
		Parameters: QianwenParameters{
			IncrementalOutput: true,
		},
	}

	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return fmt.Errorf("JSON编码失败: %v", err)
	}

	log.Printf("千问API请求 - URL: %s", url)
	log.Printf("千问API请求 - 数据: %s", string(jsonData))

	// 创建HTTP请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+apiKey)
	req.Header.Set("X-DashScope-SSE", "enable")

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 600 * time.Second,
	}

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查状态码
	if resp.StatusCode != 200 {
		return fmt.Errorf("API请求失败，状态码: %d", resp.StatusCode)
	}

	log.Printf("千问API HTTP状态码: %d", resp.StatusCode)
	// 处理流式响应
	return processStreamResponse(resp.Body, callback)
}

// processStreamResponse 处理流式响应
func processStreamResponse(body io.Reader, callback func(string)) error {
	scanner := bufio.NewScanner(body)
	sendContent := ""

	// 编译正则表达式
	regexAO := regexp.MustCompile(`[A-O]`)
	regexAODot := regexp.MustCompile(`[A-O]\.`)
	regexL := regexp.MustCompile(`L\.`)

	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" {
			continue
		}

		// 处理SSE格式的data行
		if strings.HasPrefix(line, "data:") {
			jsonData := strings.TrimSpace(line[5:]) // 去掉 "data:" 前缀

			var response QianwenResponse
			if err := json.Unmarshal([]byte(jsonData), &response); err != nil {
				log.Printf("JSON解析失败: %v", err)
				continue
			}

			// 提取text字段
			if response.Output.Text != "" {
				text := response.Output.Text
				sendContent += text

				// 应用PHP中的验证逻辑
				// 不允许单独出现A-O字母，必须是字母加点号格式
				if regexAO.MatchString(sendContent) && !regexAODot.MatchString(sendContent) {
					// 发现了A-O字母但还没有完整的"字母+点号"，继续扩展
					continue
				}

				// 含有L.时必须保证后续还有10个字符才能输出
				if regexL.MatchString(sendContent) && len(sendContent) < 10 {
					continue
				}

				//log.Printf("流式输出: %s", sendContent)

				// 发送数据块
				callback(sendContent)
				sendContent = ""
			}
		}
	}

	if err := scanner.Err(); err != nil {
		return fmt.Errorf("读取响应失败: %v", err)
	}

	// 发送结束信号
	log.Println("千问API调用完成，发送结束信号")
	callback("done")

	return nil
}
