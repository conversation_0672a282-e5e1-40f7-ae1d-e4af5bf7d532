import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'
import { upload } from './upload'
import { updatePdfUrl } from '@/api/report'
import { ElMessage } from 'element-plus'
import { usePdfStore } from '@/store/modules/pdf'

interface PreserveStylePdfOptions {
  filename?: string
  quality?: number
  scale?: number
  useCORS?: boolean
  allowTaint?: boolean
  backgroundColor?: string
  reportId?: number | string // 添加报告ID参数
  autoUpload?: boolean // 是否自动上传到COS
}

/**
 * 保持原有样式的PDF生成器
 * 不改变任何原有样式，按实际内容高度生成PDF
 */
export class PreserveStylePdfGenerator {
  private static readonly A4_WIDTH = 210 // A4宽度(mm)
  private static readonly A4_HEIGHT = 297 // A4高度(mm)
  private static readonly MM_TO_PX = 3.78 // mm转px的比例

  /**
   * 生成PDF - 保持原有样式和布局
   */
  static async generatePdf(element: HTMLElement, options: PreserveStylePdfOptions = {}): Promise<void> {
    const {
      filename = `report-${Date.now()}.pdf`,
      quality = 0.95,
      scale = 2,
      useCORS = true,
      allowTaint = true,
      backgroundColor = '#ffffff',
      reportId,
      autoUpload = false
    } = options

    try {
      console.log('开始生成PDF，保持原有样式...')
      
      // 等待所有内容加载完成
      await this.waitForChartsAndImages(element)

      // 获取元素的真实尺寸（完全不改变任何样式）
      const elementRect = element.getBoundingClientRect()
      const totalHeight = element.scrollHeight
      const elementWidth = element.scrollWidth

      console.log('元素真实尺寸:', {
        totalHeight,
        elementWidth,
        boundingRect: elementRect
      })

      // 创建PDF文档
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      })

      // 计算PDF页面参数
      const pageWidth = this.A4_WIDTH
      const pageHeight = this.A4_HEIGHT
      const headerTopMargin = 20 / this.MM_TO_PX // 页头距离顶部20px转换为mm
      const headerHeight = 15 // 页头高度
      const headerBottomMargin = 20 / this.MM_TO_PX // 页头距离内容20px转换为mm
      const footerTopMargin = 20 / this.MM_TO_PX // 页脚与内容距离20px转换为mm
      const footerHeight = 20 // 页脚高度
      const footerBottomMargin = 10 / this.MM_TO_PX // 页脚与底部10px转换为mm
      const contentMargin = 10 // 内容左右边距

      const contentWidth = pageWidth - (contentMargin * 2) // 左右各留边距
      const headerStartY = headerTopMargin // 页头开始位置
      const contentStartY = headerTopMargin + headerHeight + headerBottomMargin // 内容开始位置
      const footerStartY = pageHeight - footerBottomMargin - footerHeight // 页脚开始位置
      const contentHeight = footerStartY - contentStartY // 内容区域高度

      // 计算每页能容纳的内容高度（像素）
      const contentHeightPx = contentHeight * this.MM_TO_PX
      const pageContentHeight = (contentHeightPx / scale) * 2.0 // 大幅增加每页截取的内容量，确保填满页面

      // 根据实际内容高度计算需要的页数（不压缩内容）
      let  pagesNeeded = Math.ceil(totalHeight / pageContentHeight)
      //pagesNeeded = Math.min(4, pagesNeeded) // 限制只生成10页用于测试

      console.log('PDF生成参数 (测试模式 - 只生成10页):', {
        pageWidth,
        pageHeight,
        '页头距顶部': `${headerTopMargin}mm (20px)`,
        '页头高度': `${headerHeight}mm`,
        '页头距内容': `${headerBottomMargin}mm (20px)`,
        '页脚距内容': `${footerTopMargin}mm (20px)`,
        '页脚高度': `${footerHeight}mm`,
        '页脚距底部': `${footerBottomMargin}mm (10px)`,
        contentWidth,
        contentHeight,
        contentStartY,
        footerStartY,
        contentHeightPx,
        pageContentHeight,
        totalHeight,
        scale,
        pagesNeeded,
        '每页内容高度(原始px)': pageContentHeight,
        '每页内容高度(canvas px)': pageContentHeight * scale
      })

      // 先截取完整内容，然后分页处理
      console.log('开始截取完整内容...')

       

      const fullCanvas = await html2canvas(element, {
        scale,
        useCORS,
        allowTaint,
        backgroundColor,
        width: elementWidth,
        height: totalHeight,
        scrollX: 0,
        scrollY: 0,
        windowWidth: elementWidth,
        windowHeight: totalHeight,
        logging: false
      })

      console.log('完整内容截取完成，canvas尺寸:', {
        width: fullCanvas.width,
        height: fullCanvas.height
      })

      // 计算每页在canvas中的高度（考虑scale）
      const pageHeightInCanvas = pageContentHeight * scale

      // 逐页生成PDF
      for (let pageIndex = 0; pageIndex < pagesNeeded; pageIndex++) {
        if (pageIndex > 0) {
          pdf.addPage()
        }

        console.log(`正在生成第 ${pageIndex + 1}/${pagesNeeded} 页...`)
        ElMessage.info(`正在生成第 ${pageIndex + 1}/${pagesNeeded} 页...`)
        // 第一页不添加页头
        if (pageIndex > 0) {
          await this.addHeaderComponent(pdf, pageWidth, headerStartY)
        }

        // 计算当前页在canvas中的位置
        const sourceY = pageIndex * pageHeightInCanvas
        const sourceHeight = Math.min(pageHeightInCanvas, fullCanvas.height - sourceY)

        if (sourceHeight > 0) {
          console.log(`第${pageIndex + 1}页canvas切割参数:`, {
            pageIndex,
            sourceY,
            sourceHeight,
            pageHeightInCanvas,
            fullCanvasHeight: fullCanvas.height,
            pageContentHeight,
            scale
          })

          // 创建当前页的canvas
          const pageCanvas = document.createElement('canvas')
          const pageCtx = pageCanvas.getContext('2d')!

          pageCanvas.width = fullCanvas.width
          pageCanvas.height = sourceHeight

          // 从完整canvas中切割当前页内容
          pageCtx.drawImage(
            fullCanvas,
            0, sourceY, fullCanvas.width, sourceHeight,
            0, 0, fullCanvas.width, sourceHeight
          )

          // 添加到PDF
          const pageImgData = pageCanvas.toDataURL('image/jpeg', quality)
          const pdfImgHeight = (sourceHeight * contentWidth) / fullCanvas.width

          // 第一页从顶部开始，其他页面从页头下方开始
          const startY = pageIndex === 0 ? 10 : contentStartY
          pdf.addImage(pageImgData, 'JPEG', contentMargin, startY, contentWidth, pdfImgHeight)

          console.log(`第${pageIndex + 1}页添加完成:`, {
            'PDF图片高度': `${pdfImgHeight}mm`,
            '内容高度设置': `${contentHeight}mm`,
            '位置': `(${contentMargin}, ${startY})`,
            '源canvas高度': sourceHeight,
            '页面canvas高度': pageHeightInCanvas,
            '是否第一页': pageIndex === 0,
            '页头页脚': pageIndex === 0 ? '无' : '有'
          })
        }

        // 第一页不添加页脚
        if (pageIndex > 0) {
          await this.addFooterComponent(pdf, pageWidth, pageHeight, pageIndex + 1, pagesNeeded, footerStartY)
        }
      }

      // 保存PDF
      pdf.save(filename)
      console.log('PDF生成完成')

      // 如果启用自动上传且提供了reportId，则上传到COS并更新URL
      if (autoUpload && reportId) {
        try {
          ElMessage.info('正在上传PDF到云存储...')

          // 生成PDF Blob
          const pdfBlob = pdf.output('blob')

          // 创建File对象
          const pdfFile = new File([pdfBlob], filename, {
            type: 'application/pdf'
          })

          // 上传到COS
          console.log('开始上传PDF到COS...')
          const uploadResult = await upload(pdfFile)
          console.log('PDF上传成功:', uploadResult)

          // 构建完整的URL
          const pdfUrl = `https://${uploadResult.Location}`
          //更新到状态管理
          const pdfStore = usePdfStore()
          pdfStore.setPdfUrl(pdfUrl)
          // 更新PDF URL到后端
          console.log('更新PDF URL到后端...', { reportId, pdfUrl })
          await updatePdfUrl({
            report_id: reportId,
            pdf_url: pdfUrl
          })

          ElMessage.success('PDF已生成并上传成功！')
          console.log('PDF URL更新成功')

        } catch (uploadError) {
          console.error('PDF上传失败:', uploadError)
          ElMessage.error('PDF生成成功，但上传失败：' + (uploadError as Error).message)
        }
      }

    } catch (error) {
      console.error('PDF生成失败:', error)
      throw error
    }
  }

  /**
   * 添加页头组件（完全按照ReportHeader.vue的样式）
   */
  private static async addHeaderComponent(pdf: jsPDF, pageWidth: number, startY: number = 0): Promise<void> {
    try {
      // 创建页头HTML（完全复制ReportHeader.vue的结构和样式）
      const headerHtml = `
        <div class="top-green-section" style="display: flex; align-items: center; justify-content: space-between; width: ${pageWidth * this.MM_TO_PX}px; height: 60px; padding: 10px; box-sizing: border-box;">
          <div class="left-top-container" style="display: flex; align-items: center;">
            <img class="right-color-block" style="width: 82px; height: 34px;" src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/pdf-top-green.png" alt="顶部文字" />
            <img class="text-img" style="margin-left: 10px; width: 206px; height: 34px;" src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/top-text.png" alt="顶部文字" />
          </div>
          <div class="content-logo">
            <img style="height: 50px;" src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/pdf_logo.png" alt="Logo" />
          </div>
        </div>
      `

      // 创建临时DOM元素
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = headerHtml
      tempDiv.style.position = 'absolute'
      tempDiv.style.top = '-9999px'
      tempDiv.style.left = '-9999px'
      tempDiv.style.zIndex = '-1000'
      document.body.appendChild(tempDiv)

      const headerElement = tempDiv.firstElementChild as HTMLElement

      // 等待图片加载
      await this.waitForImages(headerElement)

      // 渲染页头
      const canvas = await html2canvas(headerElement, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: 'transparent',
        width: pageWidth * this.MM_TO_PX,
        height: 60
      })

      // 添加到PDF
      const imgData = canvas.toDataURL('image/png', 0.95)
      pdf.addImage(imgData, 'PNG', 0, startY, pageWidth, 15)

      // 清理临时元素
      document.body.removeChild(tempDiv)
    } catch (error) {
      console.error('页头渲染失败，使用简单页头:', error)
      this.addSimpleHeader(pdf, pageWidth)
    }
  }

  /**
   * 添加页脚组件（完全按照ReportFooter.vue的样式）
   */
  private static async addFooterComponent(pdf: jsPDF, pageWidth: number, pageHeight: number, pageNumber: number, totalPages: number, startY?: number): Promise<void> {
    try {
      // 创建页脚HTML（完全复制ReportFooter.vue的结构和样式）
      const footerHtml = `
        <div class="page-footer" style="position: relative; width: ${pageWidth * this.MM_TO_PX}px; height: 80px;">
          <div class="footer-bg" style="position: relative; height: 100%;">
            <div class="page-number" style="background-image: url('https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/bottom-left-bg.png'); background-repeat: no-repeat; background-size: contain; width: 70px; height: 20px; position: absolute; bottom: 20px; right: 40px; font-weight: bold; font-size: 14px; padding-left: 18px; line-height: 20px; color: #333;">
              ${pageNumber} / ${totalPages}
            </div>
          </div>
        </div>
      `

      // 创建临时DOM元素
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = footerHtml
      tempDiv.style.position = 'absolute'
      tempDiv.style.top = '-9999px'
      tempDiv.style.left = '-9999px'
      tempDiv.style.zIndex = '-1000'
      document.body.appendChild(tempDiv)

      const footerElement = tempDiv.firstElementChild as HTMLElement

      // 等待图片加载
      await this.waitForImages(footerElement)

      // 渲染页脚
      const canvas = await html2canvas(footerElement, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: 'transparent',
        width: pageWidth * this.MM_TO_PX,
        height: 80
      })

      // 添加到PDF
      const imgData = canvas.toDataURL('image/png', 0.95)
      const footerY = startY !== undefined ? startY : pageHeight - 20 // 使用传入的位置或默认位置
      pdf.addImage(imgData, 'PNG', 0, footerY, pageWidth, 20)

      // 清理临时元素
      document.body.removeChild(tempDiv)
    } catch (error) {
      console.error('页脚渲染失败，使用简单页脚:', error)
      this.addSimpleFooter(pdf, pageWidth, pageHeight, pageNumber, totalPages)
    }
  }

  /**
   * 简单页头（降级方案）
   */
  private static addSimpleHeader(pdf: jsPDF, pageWidth: number): void {
    pdf.setFillColor(27, 179, 148)
    pdf.rect(0, 0, pageWidth, 15, 'F')
    pdf.setTextColor(255, 255, 255)
    pdf.setFontSize(10)
    pdf.text('AI择校报告系统', 10, 8)
  }

  /**
   * 简单页脚（降级方案）
   */
  private static addSimpleFooter(pdf: jsPDF, pageWidth: number, pageHeight: number, pageNumber: number, totalPages: number): void {
    const footerY = pageHeight - 15 // 页脚区域开始位置
    pdf.setDrawColor(200, 200, 200)
    pdf.setLineWidth(0.3)
    pdf.line(10, footerY - 3, pageWidth - 10, footerY - 3)

    pdf.setTextColor(102, 102, 102)
    pdf.setFontSize(9)
    const pageText = `${pageNumber} / ${totalPages}`
    const textWidth = pdf.getTextWidth(pageText)
    pdf.text(pageText, (pageWidth - textWidth) / 2, footerY + 5)
  }

  /**
   * 等待图表和图片加载完成
   */
  private static async waitForChartsAndImages(element: HTMLElement): Promise<void> {
    console.log('等待图表和图片加载...')
    
    // 等待ECharts图表渲染完成
    const charts = element.querySelectorAll('.echarts-box')
    for (const chart of charts) {
      await new Promise(resolve => {
        const checkChart = () => {
          const canvas = chart.querySelector('canvas')
          if (canvas && canvas.width > 0 && canvas.height > 0) {
            resolve(void 0)
          } else {
            setTimeout(checkChart, 100)
          }
        }
        checkChart()
      })
    }

    // 等待图片加载完成
    const images = element.querySelectorAll('img')
    const imagePromises = Array.from(images).map(img => {
      return new Promise(resolve => {
        if (img.complete) {
          resolve(void 0)
        } else {
          img.onload = () => resolve(void 0)
          img.onerror = () => resolve(void 0)
        }
      })
    })

    await Promise.all(imagePromises)

    // 强制重绘ECharts图表
    const echartsInstances = (window as any).echarts?.getInstanceByDom
    if (echartsInstances) {
      charts.forEach(chart => {
        const instance = (window as any).echarts.getInstanceByDom(chart)
        if (instance) {
          instance.resize()
        }
      })
    }

    await new Promise(resolve => setTimeout(resolve, 1000))
    console.log('图表和图片加载完成')
  }

  /**
   * 等待图片加载完成
   */
  private static async waitForImages(element: HTMLElement): Promise<void> {
    const images = element.querySelectorAll('img')
    const imagePromises = Array.from(images).map(img => {
      return new Promise(resolve => {
        if (img.complete) {
          resolve(void 0)
        } else {
          img.onload = () => resolve(void 0)
          img.onerror = () => resolve(void 0)
        }
      })
    })

    await Promise.all(imagePromises)
    await new Promise(resolve => setTimeout(resolve, 500))
  }
}

export default PreserveStylePdfGenerator
