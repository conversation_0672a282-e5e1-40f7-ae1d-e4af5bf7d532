{"code": 0, "msg": "success", "data": {"recommend_list": [{"school_name": "中国科学技术大学", "major_name": "计算机科学与技术", "difficulty_analysis": "中国科学技术大学计算机科学与技术专业竞争激烈，分数线为363分，略高于学生预估总分。该校计算机专业在国内排名靠前，报考人数众多，竞争压力较大。", "suggest": "建议学生在数学和专业课上下功夫，争取将数学成绩提升至100分以上，专业课提升至120分以上。同时，加强政治和英语的复习，确保不拖后腿。", "reason": "中国科学技术大学是学生的梦想院校，且位于安徽省，地理位置便利。该校计算机专业实力强劲，科研资源丰富，适合有志于从事科研工作的学生。虽然竞争激烈，但通过努力仍有希望。", "school_id": "26439", "admission_stats": {"yearly_stats": [{"total_admission": 22, "avg_initial_score": "379.863636", "max_initial_score": "407.00", "min_initial_score": "367.00", "avg_retest_score": "146.539091", "max_retest_score": "901.00", "min_retest_score": "63.69", "year": 2025}], "has_data": true}, "retest_stats": {"yearly_stats": [{"total_retest": 22, "avg_initial_score": "380.090909", "max_initial_score": "407.00", "min_initial_score": "367.00", "admission_count": 0, "year": 2025, "admission_rate": 0}], "has_data": true}, "basic_info": {"research_direction": "(00)不区分", "exam_range": "(101)思想政治理论；(201)英语（一）；(301)数学（一）；(408)计算机学科专业基础\n\n\n\n2025年初试参考图书\n\n\n\r\n                            (301)数学（一）((204:;(408)计算机学科专业基础((168:;\n\n\n\n2025年复试内容\n\n\n\r\n                            复试内容：1、面试（满分80分）　　内容包括：英语、专业综合基础知识。（口试）　　2、上机（满分120分）　　编程语言：C或者C++。编译环境：提供dev c/c++， code blocks和vs code;；\n\n\n\n2025年学费学制住宿\n\n\n\r\n                            学费：8000元；学制：未公布；住宿：未公布；\n\n\n\n\n2025年报考要求\n\n\n\r\n                            无；\n\n\n\n\n2024年研究方向\n\n\n\r\n                            (00)不区分研究方向\n\n\n\n2024年", "reference_books": "", "retest_content": "", "tuition_fee": "学制住宿元", "study_years": "住宿\n\n\n\r\n                            学费：8000元；年", "accommodation": "", "admission_requirements": ""}, "school_info": {"id": 14, "logo": "yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/dxt/schoolLogo/14.jpeg", "tag_211": 1, "tag_985": 1, "dual_class": 1, "address": "安徽省合肥市金寨路96号", "phone": "0551-63602553,0551-63600096", "home_site": "https://www.ustc.edu.cn/", "zsb_site": "https://zsb.ustc.edu.cn", "is_211": true, "is_985": true, "is_dual_class": true}, "current_year_retest_list": {"list": [{"name": "侯**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "65", "english_score": "64", "major1_score": "142", "major2_score": "136", "initial_score": "407.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "李**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "70", "english_score": "83", "major1_score": "132", "major2_score": "121", "initial_score": "406.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "武**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "71", "english_score": "76", "major1_score": "136", "major2_score": "121", "initial_score": "404.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "姚*", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "68", "english_score": "66", "major1_score": "133", "major2_score": "130", "initial_score": "397.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "况**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "62", "english_score": "65", "major1_score": "134", "major2_score": "131", "initial_score": "392.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "李*", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "62", "english_score": "66", "major1_score": "134", "major2_score": "124", "initial_score": "386.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "唐**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "62", "english_score": "65", "major1_score": "127", "major2_score": "130", "initial_score": "384.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "黄**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "64", "english_score": "55", "major1_score": "134", "major2_score": "129", "initial_score": "382.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "戚**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "61", "english_score": "66", "major1_score": "132", "major2_score": "122", "initial_score": "381.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "刘**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "63", "english_score": "77", "major1_score": "127", "major2_score": "112", "initial_score": "379.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "李*", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "66", "english_score": "67", "major1_score": "119", "major2_score": "124", "initial_score": "376.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "刘**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "64", "english_score": "73", "major1_score": "115", "major2_score": "122", "initial_score": "374.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "张**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "62", "english_score": "65", "major1_score": "125", "major2_score": "121", "initial_score": "373.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "韦**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "69", "english_score": "67", "major1_score": "123", "major2_score": "113", "initial_score": "372.00", "volunteer_type": "一志愿", "admission_status": "调剂外校", "year": 2025}, {"name": "方*", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "61", "english_score": "67", "major1_score": "127", "major2_score": "117", "initial_score": "372.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "林*", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "68", "english_score": "67", "major1_score": "115", "major2_score": "119", "initial_score": "369.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "徐**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "70", "english_score": "62", "major1_score": "110", "major2_score": "127", "initial_score": "369.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "赵**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "64", "english_score": "59", "major1_score": "132", "major2_score": "114", "initial_score": "369.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "田*", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "72", "english_score": "59", "major1_score": "120", "major2_score": "117", "initial_score": "368.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "冯**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "69", "english_score": "63", "major1_score": "123", "major2_score": "113", "initial_score": "368.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}], "year": 2025, "count": 20}, "current_year_admission_list": {"list": [{"name": "侯**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "407", "retest_score": "89.38", "total_score": "86.79", "first_choice_school": "中国科学技术大学", "student_remark": "", "year": 2025}, {"name": "李**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "406", "retest_score": "83.75", "total_score": "83.92", "first_choice_school": "中国科学技术大学", "student_remark": "", "year": 2025}, {"name": "唐**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "384", "retest_score": "90.72", "total_score": "83.91", "first_choice_school": "中国科学技术大学", "student_remark": "", "year": 2025}, {"name": "刘**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "379", "retest_score": "93.69", "total_score": "83.9", "first_choice_school": "中国科学技术大学", "student_remark": "", "year": 2025}, {"name": "黄**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "382", "retest_score": "90.4", "total_score": "83.13", "first_choice_school": "中国科学技术大学", "student_remark": "", "year": 2025}, {"name": "戚**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "381", "retest_score": "87.15", "total_score": "82.38", "first_choice_school": "中国科学技术大学", "student_remark": "", "year": 2025}, {"name": "方*", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "372", "retest_score": "91.59", "total_score": "82.36", "first_choice_school": "中国科学技术大学", "student_remark": "", "year": 2025}, {"name": "况**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "392", "retest_score": "81.42", "total_score": "82.14", "first_choice_school": "中国科学技术大学", "student_remark": "", "year": 2025}, {"name": "龙**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "367", "retest_score": "92.18", "total_score": "81.89", "first_choice_school": "中国科学技术大学", "student_remark": "", "year": 2025}, {"name": "林*", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "369", "retest_score": "91.56", "total_score": "80.69", "first_choice_school": "中国科学技术大学", "student_remark": "", "year": 2025}, {"name": "徐**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "369", "retest_score": "901", "total_score": "79.84", "first_choice_school": "中国科学技术大学", "student_remark": "", "year": 2025}, {"name": "李*", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "386", "retest_score": "77.5", "total_score": "79.83", "first_choice_school": "中国科学技术大学", "student_remark": "", "year": 2025}, {"name": "吴**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "367", "retest_score": "89.82", "total_score": "79.61", "first_choice_school": "中国科学技术大学", "student_remark": "", "year": 2025}, {"name": "刘**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "374", "retest_score": "78.47", "total_score": "77.82", "first_choice_school": "中国科学技术大学", "student_remark": "", "year": 2025}, {"name": "李*", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "376", "retest_score": "78.28", "total_score": "77.76", "first_choice_school": "中国科学技术大学", "student_remark": "", "year": 2025}, {"name": "武**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "404", "retest_score": "655", "total_score": "77.18", "first_choice_school": "中国科学技术大学", "student_remark": "", "year": 2025}, {"name": "冯**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "368", "retest_score": "81.94", "total_score": "77.15", "first_choice_school": "中国科学技术大学", "student_remark": "", "year": 2025}, {"name": "姚*", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "397", "retest_score": "63.69", "total_score": "766", "first_choice_school": "中国科学技术大学", "student_remark": "", "year": 2025}, {"name": "张**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "373", "retest_score": "73.91", "total_score": "76.47", "first_choice_school": "中国科学技术大学", "student_remark": "", "year": 2025}, {"name": "赵**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "369", "retest_score": "75.25", "total_score": "75.92", "first_choice_school": "中国科学技术大学", "student_remark": "", "year": 2025}], "year": 2025, "count": 20}}, {"school_name": "南京大学", "major_name": "计算机科学与技术", "difficulty_analysis": "南京大学计算机科学与技术专业分数线为367分，竞争非常激烈。该校计算机专业在国内享有盛誉，报考人数多，录取比例较低。", "suggest": "建议学生将总分目标定在370分以上，重点提升数学和专业课成绩。可以参加一些专业课的辅导班，系统复习专业知识。", "reason": "南京大学位于江苏省，地理位置优越，计算机专业实力强，科研和就业前景好。虽然分数线较高，但通过努力仍有希望。", "school_id": "18763", "admission_stats": {"yearly_stats": [{"total_admission": 10, "avg_initial_score": "372.000000", "max_initial_score": "383.00", "min_initial_score": "357.00", "avg_retest_score": "229.480000", "max_retest_score": "294.20", "min_retest_score": "197.60", "year": 2025}], "has_data": true}, "retest_stats": {"yearly_stats": [{"total_retest": 12, "avg_initial_score": "370.333333", "max_initial_score": "383.00", "min_initial_score": "357.00", "admission_count": 0, "year": 2025, "admission_rate": 0}], "has_data": true}, "basic_info": {"research_direction": "(10)机器学习与数据挖掘；(05)认知与神经科学启发的人工智能；(08)生成式人工智能；(01)机器感知与机器视觉；(07)三维视觉与可视计算；(02)通用基础大模型；(06)生物信息计算与数字健康；(09)多智能体系统；(03)机器人学与智能系统；(04)AI for Science；(11)自然语言处理\n\n\n\n2025年", "exam_range": "(101)思想政治理论；(201)英语（一）；(301)数学（一）；(408)计算机学科专业基础\n\n\n\n2025年初试参考图书\n\n\n\r\n                            (408)计算机学科专业基础:未公布", "reference_books": "目;(301)数学（一）:未公布", "retest_content": "", "tuition_fee": "学制住宿元", "study_years": "住宿\n\n\n\r\n                            学费：8000元；年", "accommodation": "", "admission_requirements": ""}, "school_info": {"id": 10, "logo": "yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/dxt/schoolLogo/10.jpeg", "tag_211": 1, "tag_985": 1, "dual_class": 1, "address": "仙林校区：江苏省南京市仙林大道163号,鼓楼校区：江苏省南京市汉口路22号,浦口校区：南京市浦口区学府路8号,苏州校区：苏州市太湖大道1520号", "phone": "400-1859680", "home_site": "https://www.nju.edu.cn/", "zsb_site": "https://bkzs.nju.edu.cn", "is_211": true, "is_985": true, "is_dual_class": true}, "current_year_retest_list": {"list": [{"name": "喻**", "college": "智能科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "65", "english_score": "69", "major1_score": "134", "major2_score": "115", "initial_score": "383.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "刘**", "college": "智能科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "69", "english_score": "64", "major1_score": "128", "major2_score": "122", "initial_score": "383.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "王*", "college": "智能科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "62", "english_score": "75", "major1_score": "134", "major2_score": "110", "initial_score": "381.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "李**", "college": "智能科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "65", "english_score": "71", "major1_score": "115", "major2_score": "127", "initial_score": "378.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "豆**", "college": "智能科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "67", "english_score": "65", "major1_score": "126", "major2_score": "120", "initial_score": "378.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "万*", "college": "智能科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "66", "english_score": "74", "major1_score": "107", "major2_score": "121", "initial_score": "368.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "秦**", "college": "智能科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "66", "english_score": "72", "major1_score": "113", "major2_score": "116", "initial_score": "367.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "石**", "college": "智能科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "64", "english_score": "54", "major1_score": "125", "major2_score": "124", "initial_score": "367.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "王*", "college": "智能科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "67", "english_score": "62", "major1_score": "113", "major2_score": "124", "initial_score": "366.00", "volunteer_type": "一志愿", "admission_status": "调剂外校", "year": 2025}, {"name": "徐**", "college": "智能科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "68", "english_score": "62", "major1_score": "105", "major2_score": "123", "initial_score": "358.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "张**", "college": "智能科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "65", "english_score": "62", "major1_score": "131", "major2_score": "100", "initial_score": "358.00", "volunteer_type": "一志愿", "admission_status": "调剂外校", "year": 2025}, {"name": "钟*", "college": "智能科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "71", "english_score": "65", "major1_score": "115", "major2_score": "106", "initial_score": "357.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}], "year": 2025, "count": 12}, "current_year_admission_list": {"list": [{"name": "王*", "college": "智能科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "381", "retest_score": "294.2", "total_score": "675.2", "first_choice_school": "南京大学", "student_remark": "", "year": 2025}, {"name": "喻**", "college": "智能科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "383", "retest_score": "235.6", "total_score": "618.6", "first_choice_school": "南京大学", "student_remark": "", "year": 2025}, {"name": "秦**", "college": "智能科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "367", "retest_score": "245.2", "total_score": "612.2", "first_choice_school": "南京大学", "student_remark": "", "year": 2025}, {"name": "李**", "college": "智能科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "378", "retest_score": "228.2", "total_score": "606.2", "first_choice_school": "南京大学", "student_remark": "", "year": 2025}, {"name": "徐**", "college": "智能科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "358", "retest_score": "242", "total_score": "600", "first_choice_school": "南京大学", "student_remark": "", "year": 2025}, {"name": "刘**", "college": "智能科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "383", "retest_score": "209.4", "total_score": "592.4", "first_choice_school": "南京大学", "student_remark": "", "year": 2025}, {"name": "石**", "college": "智能科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "367", "retest_score": "224.4", "total_score": "591.4", "first_choice_school": "南京大学", "student_remark": "", "year": 2025}, {"name": "万*", "college": "智能科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "368", "retest_score": "212.4", "total_score": "580.4", "first_choice_school": "南京大学", "student_remark": "", "year": 2025}, {"name": "豆**", "college": "智能科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "378", "retest_score": "197.6", "total_score": "575.6", "first_choice_school": "南京大学", "student_remark": "", "year": 2025}, {"name": "钟*", "college": "智能科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "357", "retest_score": "205.8", "total_score": "562.8", "first_choice_school": "南京大学", "student_remark": "", "year": 2025}], "year": 2025, "count": 10}}, {"school_name": "北京大学", "major_name": "计算机科学与技术", "difficulty_analysis": "北京大学计算机科学与技术专业分数线为359分，竞争极为激烈。该校计算机专业在国内顶尖，报考人数众多，录取比例极低。", "suggest": "建议学生将总分目标定在360分以上，重点提升数学和专业课成绩。可以参加一些高水平的专业课辅导班，系统复习专业知识。", "reason": "北京大学是国内顶尖高校，计算机专业实力强，科研和就业前景极好。虽然竞争激烈，但通过努力仍有希望。", "school_id": "75578", "admission_stats": {"yearly_stats": [{"total_admission": 22, "avg_initial_score": "378.409091", "max_initial_score": "405.00", "min_initial_score": "361.00", "avg_retest_score": "89.173636", "max_retest_score": "93.60", "min_retest_score": "84.83", "year": 2025}], "has_data": true}, "retest_stats": {"yearly_stats": [{"total_retest": 22, "avg_initial_score": "379.545455", "max_initial_score": "405.00", "min_initial_score": "363.00", "admission_count": 0, "year": 2025, "admission_rate": 0}], "has_data": true}, "basic_info": {"research_direction": "(00)不区分", "exam_range": "(101)思想政治理论；(201)英语（一）；(301)数学（一）；(408)计算机学科专业基础\n\n\n\n2025年初试参考图书\n\n\n\r\n                            (301)数学（一）((001:未知;(408)计算机学科专业基础((017:未知;\n\n\n\n2025年复试内容\n\n\n\r\n                            复试内容：未知;；\n\n\n\n2025年学费学制住宿\n\n\n\r\n                            学费：暂未公布；学制：暂未公布；住宿：按照学院具体要求执行；\n\n\n\n\n2025年报考要求\n\n\n\r\n                            本专业学习年限3年。；\n\n\n\n\n2024年研究方向\n\n\n\r\n                            (00)不区分研究方向\n\n\n\n2024年", "reference_books": "", "retest_content": "", "tuition_fee": "学制住宿元", "study_years": "住宿\n\n\n\r\n                            学费：暂未公布；年", "accommodation": "", "admission_requirements": ""}, "school_info": {"id": 1, "logo": "yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/dxt/schoolLogo/1.jpeg", "tag_211": 1, "tag_985": 1, "dual_class": 1, "address": "北京市海淀区颐和园路5号", "phone": "010-62751407", "home_site": "https://www.pku.edu.cn/", "zsb_site": "https://www.gotopku.cn/", "is_211": true, "is_985": true, "is_dual_class": true}, "current_year_retest_list": {"list": [{"name": "徐**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "65", "english_score": "74", "major1_score": "142", "major2_score": "124", "initial_score": "405.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "李**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "61", "english_score": "70", "major1_score": "135", "major2_score": "131", "initial_score": "397.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "屈**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "61", "english_score": "72", "major1_score": "138", "major2_score": "124", "initial_score": "395.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "龚**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "69", "english_score": "79", "major1_score": "130", "major2_score": "113", "initial_score": "391.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "彭**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "66", "english_score": "71", "major1_score": "138", "major2_score": "116", "initial_score": "391.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "李*", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "65", "english_score": "72", "major1_score": "140", "major2_score": "113", "initial_score": "390.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "徐**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "56", "english_score": "68", "major1_score": "147", "major2_score": "118", "initial_score": "389.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "周**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "69", "english_score": "70", "major1_score": "125", "major2_score": "118", "initial_score": "382.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "汪**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "63", "english_score": "68", "major1_score": "133", "major2_score": "118", "initial_score": "382.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "陈**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "56", "english_score": "71", "major1_score": "141", "major2_score": "112", "initial_score": "380.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "巩**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "56", "english_score": "69", "major1_score": "125", "major2_score": "125", "initial_score": "375.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "房**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "62", "english_score": "56", "major1_score": "137", "major2_score": "119", "initial_score": "374.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "项**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "52", "english_score": "66", "major1_score": "138", "major2_score": "118", "initial_score": "374.00", "volunteer_type": "一志愿", "admission_status": "", "year": 2025}, {"name": "高**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "58", "english_score": "68", "major1_score": "130", "major2_score": "118", "initial_score": "374.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "郭*", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "57", "english_score": "64", "major1_score": "132", "major2_score": "120", "initial_score": "373.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "董**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "58", "english_score": "60", "major1_score": "141", "major2_score": "114", "initial_score": "373.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "管**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "58", "english_score": "67", "major1_score": "133", "major2_score": "112", "initial_score": "370.00", "volunteer_type": "一志愿", "admission_status": "", "year": 2025}, {"name": "冯**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "61", "english_score": "69", "major1_score": "130", "major2_score": "109", "initial_score": "369.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "李**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "64", "english_score": "74", "major1_score": "119", "major2_score": "112", "initial_score": "369.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "梁**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "57", "english_score": "54", "major1_score": "132", "major2_score": "125", "initial_score": "368.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}], "year": 2025, "count": 20}, "current_year_admission_list": {"list": [{"name": "李*", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "390", "retest_score": "93.6", "total_score": "84.24", "first_choice_school": "北京大学", "student_remark": "", "year": 2025}, {"name": "屈**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "395", "retest_score": "90.8", "total_score": "83.72", "first_choice_school": "北京大学", "student_remark": "", "year": 2025}, {"name": "徐**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "405", "retest_score": "87.67", "total_score": "83.67", "first_choice_school": "北京大学", "student_remark": "", "year": 2025}, {"name": "龚**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "391", "retest_score": "89.17", "total_score": "82.59", "first_choice_school": "北京大学", "student_remark": "", "year": 2025}, {"name": "巩**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "375", "retest_score": "92.4", "total_score": "81.96", "first_choice_school": "北京大学", "student_remark": "", "year": 2025}, {"name": "汪**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "382", "retest_score": "89.83", "total_score": "81.77", "first_choice_school": "北京大学", "student_remark": "", "year": 2025}, {"name": "周**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "382", "retest_score": "89.8", "total_score": "81.76", "first_choice_school": "北京大学", "student_remark": "", "year": 2025}, {"name": "彭**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "391", "retest_score": "87", "total_score": "81.72", "first_choice_school": "北京大学", "student_remark": "", "year": 2025}, {"name": "陈**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "380", "retest_score": "90.17", "total_score": "81.67", "first_choice_school": "北京大学", "student_remark": "", "year": 2025}, {"name": "李**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "397", "retest_score": "84.83", "total_score": "81.57", "first_choice_school": "北京大学", "student_remark": "", "year": 2025}, {"name": "房**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "374", "retest_score": "91.17", "total_score": "81.35", "first_choice_school": "北京大学", "student_remark": "", "year": 2025}, {"name": "董**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "373", "retest_score": "91.17", "total_score": "81.23", "first_choice_school": "北京大学", "student_remark": "", "year": 2025}, {"name": "徐**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "389", "retest_score": "86.17", "total_score": "81.15", "first_choice_school": "北京大学", "student_remark": "", "year": 2025}, {"name": "高**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "374", "retest_score": "89.17", "total_score": "80.55", "first_choice_school": "北京大学", "student_remark": "", "year": 2025}, {"name": "李**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "369", "retest_score": "90", "total_score": "80.28", "first_choice_school": "北京大学", "student_remark": "", "year": 2025}, {"name": "冯**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "369", "retest_score": "90", "total_score": "80.28", "first_choice_school": "北京大学", "student_remark": "", "year": 2025}, {"name": "冯**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "362", "retest_score": "89", "total_score": "794", "first_choice_school": "北京大学", "student_remark": "", "year": 2025}, {"name": "郭*", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "373", "retest_score": "85.67", "total_score": "793", "first_choice_school": "北京大学", "student_remark": "", "year": 2025}, {"name": "梁**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "368", "retest_score": "88", "total_score": "79.36", "first_choice_school": "北京大学", "student_remark": "", "year": 2025}, {"name": "张**", "college": "深圳研究生院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "362", "retest_score": "89.2", "total_score": "79.12", "first_choice_school": "北京大学", "student_remark": "", "year": 2025}], "year": 2025, "count": 20}}, {"school_name": "北京航空航天大学", "major_name": "计算机科学与技术", "difficulty_analysis": "北京航空航天大学计算机科学与技术专业分数线为356分，竞争较为激烈。该校计算机专业在国内排名靠前，报考人数较多。", "suggest": "建议学生将总分目标定在360分以上，重点提升数学和专业课成绩。可以参加一些专业课的辅导班，系统复习专业知识。", "reason": "北京航空航天大学计算机专业实力强，科研和就业前景好。虽然竞争较为激烈，但通过努力仍有希望。", "school_id": "75579", "admission_stats": {"yearly_stats": [{"total_admission": 22, "avg_initial_score": "381.045455", "max_initial_score": "401.00", "min_initial_score": "367.00", "avg_retest_score": "263.352727", "max_retest_score": "295.00", "min_retest_score": "222.55", "year": 2025}], "has_data": true}, "retest_stats": {"yearly_stats": [{"total_retest": 22, "avg_initial_score": "381.045455", "max_initial_score": "401.00", "min_initial_score": "367.00", "admission_count": 0, "year": 2025, "admission_rate": 0}], "has_data": true}, "basic_info": {"research_direction": "(03)可视计算与通用智能；(02)新型系统结构与芯片；(04)未来网络与安全；(05)虚拟现实与增强现实；(01)先进计算系统与环境\n\n\n\n2025年", "exam_range": "(101)思想政治理论；(201)英语（一）；(301)数学（一）；(806)计算机基础综合\n\n\n\n2025年初试参考图书\n\n\n\r\n                            (301)数学（一）((002:未公布", "reference_books": "目;(806)计算机基础综合((006:806计算机基础综合共包括三门课程的内容：计算机组成原理、操作系统、计算机网络技术，分别占60分，50分、40分。所有课程均不指定", "retest_content": "", "tuition_fee": "学制住宿元", "study_years": "住宿\n\n\n\r\n                            学费：8000元；年", "accommodation": "", "admission_requirements": ""}, "school_info": {"id": 35, "logo": "yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/dxt/schoolLogo/35.jpeg", "tag_211": 1, "tag_985": 1, "dual_class": 1, "address": "学院路校区：北京市海淀区学院路37号,沙河校区：北京市昌平区沙河高教园区", "phone": "010-83429695", "home_site": "https://www.buaa.edu.cn/", "zsb_site": "https://zs.buaa.edu.cn/index.htm", "is_211": true, "is_985": true, "is_dual_class": true}, "current_year_retest_list": {"list": [{"name": "", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "62", "english_score": "71", "major1_score": "136", "major2_score": "132", "initial_score": "401.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "65", "english_score": "72", "major1_score": "128", "major2_score": "130", "initial_score": "395.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "67", "english_score": "73", "major1_score": "130", "major2_score": "123", "initial_score": "393.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "64", "english_score": "76", "major1_score": "135", "major2_score": "118", "initial_score": "393.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "60", "english_score": "66", "major1_score": "140", "major2_score": "124", "initial_score": "390.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "65", "english_score": "66", "major1_score": "140", "major2_score": "119", "initial_score": "390.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "67", "english_score": "59", "major1_score": "138", "major2_score": "122", "initial_score": "386.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "62", "english_score": "72", "major1_score": "141", "major2_score": "110", "initial_score": "385.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "57", "english_score": "72", "major1_score": "130", "major2_score": "125", "initial_score": "384.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "55", "english_score": "72", "major1_score": "139", "major2_score": "118", "initial_score": "384.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "62", "english_score": "66", "major1_score": "131", "major2_score": "122", "initial_score": "381.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "63", "english_score": "61", "major1_score": "137", "major2_score": "117", "initial_score": "378.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "63", "english_score": "60", "major1_score": "130", "major2_score": "124", "initial_score": "377.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "58", "english_score": "64", "major1_score": "142", "major2_score": "113", "initial_score": "377.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "65", "english_score": "79", "major1_score": "119", "major2_score": "113", "initial_score": "376.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "60", "english_score": "71", "major1_score": "141", "major2_score": "103", "initial_score": "375.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "59", "english_score": "56", "major1_score": "138", "major2_score": "119", "initial_score": "372.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "59", "english_score": "55", "major1_score": "137", "major2_score": "120", "initial_score": "371.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "63", "english_score": "66", "major1_score": "118", "major2_score": "123", "initial_score": "370.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "58", "english_score": "61", "major1_score": "141", "major2_score": "110", "initial_score": "370.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}], "year": 2025, "count": 20}, "current_year_admission_list": {"list": [{"name": "付**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "401", "retest_score": "279.53", "total_score": "680.53", "first_choice_school": "北京航空航天大学", "student_remark": "", "year": 2025}, {"name": "惠**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "384", "retest_score": "295", "total_score": "679", "first_choice_school": "北京航空航天大学", "student_remark": "", "year": 2025}, {"name": "赵**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "395", "retest_score": "270.61", "total_score": "665.61", "first_choice_school": "北京航空航天大学", "student_remark": "", "year": 2025}, {"name": "刘**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "386", "retest_score": "279.36", "total_score": "665.36", "first_choice_school": "北京航空航天大学", "student_remark": "", "year": 2025}, {"name": "王**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "381", "retest_score": "277.81", "total_score": "658.81", "first_choice_school": "北京航空航天大学", "student_remark": "", "year": 2025}, {"name": "陈**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "393", "retest_score": "263.72", "total_score": "656.72", "first_choice_school": "北京航空航天大学", "student_remark": "", "year": 2025}, {"name": "杨**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "377", "retest_score": "278.58", "total_score": "655.58", "first_choice_school": "北京航空航天大学", "student_remark": "", "year": 2025}, {"name": "贺**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "377", "retest_score": "276.59", "total_score": "653.59", "first_choice_school": "北京航空航天大学", "student_remark": "", "year": 2025}, {"name": "韩**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "385", "retest_score": "264.21", "total_score": "649.21", "first_choice_school": "北京航空航天大学", "student_remark": "", "year": 2025}, {"name": "刘**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "390", "retest_score": "257.86", "total_score": "647.86", "first_choice_school": "北京航空航天大学", "student_remark": "", "year": 2025}, {"name": "邹**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "384", "retest_score": "263.62", "total_score": "647.62", "first_choice_school": "北京航空航天大学", "student_remark": "", "year": 2025}, {"name": "王**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "390", "retest_score": "251.56", "total_score": "641.56", "first_choice_school": "北京航空航天大学", "student_remark": "", "year": 2025}, {"name": "杨**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "378", "retest_score": "260.32", "total_score": "638.32", "first_choice_school": "北京航空航天大学", "student_remark": "", "year": 2025}, {"name": "王*", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "367", "retest_score": "270.38", "total_score": "637.38", "first_choice_school": "北京航空航天大学", "student_remark": "", "year": 2025}, {"name": "孙**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "376", "retest_score": "260.86", "total_score": "636.86", "first_choice_school": "北京航空航天大学", "student_remark": "", "year": 2025}, {"name": "黄**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "372", "retest_score": "262.2", "total_score": "634.2", "first_choice_school": "北京航空航天大学", "student_remark": "", "year": 2025}, {"name": "赵**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "371", "retest_score": "261.76", "total_score": "632.76", "first_choice_school": "北京航空航天大学", "student_remark": "", "year": 2025}, {"name": "李**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "370", "retest_score": "259.25", "total_score": "629.25", "first_choice_school": "北京航空航天大学", "student_remark": "", "year": 2025}, {"name": "胡**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "370", "retest_score": "249.72", "total_score": "619.72", "first_choice_school": "北京航空航天大学", "student_remark": "", "year": 2025}, {"name": "鹿*", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "375", "retest_score": "242.49", "total_score": "617.49", "first_choice_school": "北京航空航天大学", "student_remark": "", "year": 2025}], "year": 2025, "count": 20}}, {"school_name": "清华大学", "major_name": "计算机科学与技术", "difficulty_analysis": "清华大学计算机科学与技术专业分数线为350分，竞争极为激烈。该校计算机专业在国内顶尖，报考人数众多，录取比例极低。", "suggest": "建议学生将总分目标定在360分以上，重点提升数学和专业课成绩。可以参加一些高水平的专业课辅导班，系统复习专业知识。", "reason": "清华大学是国内顶尖高校，计算机专业实力强，科研和就业前景极好。虽然竞争激烈，但通过努力仍有希望。", "school_id": "75580", "admission_stats": {"yearly_stats": [{"total_admission": 13, "avg_initial_score": "361.076923", "max_initial_score": "406.00", "min_initial_score": "344.00", "avg_retest_score": "412.050769", "max_retest_score": "450.13", "min_retest_score": "374.13", "year": 2025}], "has_data": true}, "retest_stats": {"yearly_stats": [], "has_data": false}, "basic_info": [], "school_info": {"id": 3, "logo": "yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/dxt/schoolLogo/3.jpeg", "tag_211": 1, "tag_985": 1, "dual_class": 1, "address": "北京市海淀区清华大学", "phone": "010-62770334,010-62782051", "home_site": "https://www.tsinghua.edu.cn", "zsb_site": "https://www.join-tsinghua.edu.cn", "is_211": true, "is_985": true, "is_dual_class": true}, "current_year_retest_list": {"list": [], "year": null, "count": 0}, "current_year_admission_list": {"list": [{"name": "陈*", "college": "计算机科学与技术系", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "406", "retest_score": "445.27", "total_score": "851.27", "first_choice_school": "清华大学", "student_remark": "", "year": 2025}, {"name": "夏**", "college": "计算机科学与技术系", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "352", "retest_score": "450.13", "total_score": "802.13", "first_choice_school": "清华大学", "student_remark": "", "year": 2025}, {"name": "麦*", "college": "计算机科学与技术系", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "362", "retest_score": "426.47", "total_score": "788.47", "first_choice_school": "清华大学", "student_remark": "", "year": 2025}, {"name": "张**", "college": "计算机科学与技术系", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "373", "retest_score": "414.8", "total_score": "787.8", "first_choice_school": "清华大学", "student_remark": "", "year": 2025}, {"name": "蔡**", "college": "计算机科学与技术系", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "365", "retest_score": "419", "total_score": "784", "first_choice_school": "清华大学", "student_remark": "", "year": 2025}, {"name": "高**", "college": "计算机科学与技术系", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "359", "retest_score": "424", "total_score": "783", "first_choice_school": "清华大学", "student_remark": "", "year": 2025}, {"name": "黄*", "college": "计算机科学与技术系", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "360", "retest_score": "413.13", "total_score": "773.13", "first_choice_school": "清华大学", "student_remark": "", "year": 2025}, {"name": "粟**", "college": "计算机科学与技术系", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "350", "retest_score": "421.87", "total_score": "771.87", "first_choice_school": "清华大学", "student_remark": "", "year": 2025}, {"name": "邵**", "college": "计算机科学与技术系", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "366", "retest_score": "402", "total_score": "768", "first_choice_school": "清华大学", "student_remark": "", "year": 2025}, {"name": "张**", "college": "计算机科学与技术系", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "368", "retest_score": "378.93", "total_score": "746.93", "first_choice_school": "清华大学", "student_remark": "", "year": 2025}, {"name": "王**", "college": "计算机科学与技术系", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "344", "retest_score": "400.8", "total_score": "744.8", "first_choice_school": "清华大学", "student_remark": "", "year": 2025}, {"name": "谢**", "college": "计算机科学与技术系", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "345", "retest_score": "386.13", "total_score": "731.13", "first_choice_school": "清华大学", "student_remark": "", "year": 2025}, {"name": "彭**", "college": "计算机科学与技术系", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "344", "retest_score": "374.13", "total_score": "718.13", "first_choice_school": "清华大学", "student_remark": "", "year": 2025}], "year": 2025, "count": 13}}, {"school_name": "北京理工大学", "major_name": "计算机科学与技术", "difficulty_analysis": "北京理工大学计算机科学与技术专业分数线为343分，竞争较为适中。该校计算机专业在国内有一定影响力，报考人数较多。", "suggest": "建议学生将总分目标定在350分以上，重点提升数学和专业课成绩。可以参加一些专业课的辅导班，系统复习专业知识。", "reason": "北京理工大学计算机专业实力较强，科研和就业前景较好。竞争相对适中，适合学生报考。", "school_id": "75581", "admission_stats": {"yearly_stats": [{"total_admission": 22, "avg_initial_score": "372.590909", "max_initial_score": "417.00", "min_initial_score": "351.00", "avg_retest_score": "118.445909", "max_retest_score": "865.00", "min_retest_score": "66.30", "year": 2025}], "has_data": true}, "retest_stats": {"yearly_stats": [{"total_retest": 22, "avg_initial_score": "372.590909", "max_initial_score": "417.00", "min_initial_score": "351.00", "admission_count": 0, "year": 2025, "admission_rate": 0}], "has_data": true}, "basic_info": {"research_direction": "(06)计算机体系结构与先进网络；(05)可视媒体计算；(04)数据科学与知识工程；(01)语言智能与社会计算；(03)软件智能与软件工程；(02)图像计算与感知智能\n\n\n\n2025年", "exam_range": "(101)思想政治理论；(201)英语（一）；(301)数学（一）；(408)计算机学科专业基础\n\n\n\n2025年初试参考图书\n\n\n\r\n                            (301)数学（一）((207:未知;(408)计算机学科专业基础((207:未知;\n\n\n\n2025年复试内容\n\n\n\r\n                            复试内容：笔试科目：C/C++语言程序设计（上机）面试内容：外语口语听力测试；计算机专业相关基础与专业知识;；\n\n\n\n2025年学费学制住宿\n\n\n\r\n                            学费：未公布；学制：未公布；住宿：未公布；\n\n\n\n\n2025年报考要求\n\n\n\r\n                            无；\n\n\n\n\n2024年研究方向\n\n\n\r\n                            (06)计算机体系结构与先进网络；(05)可视媒体计算；(04)数据科学与知识工程；(01)语言智能与社会计算；(03)软件智能与软件工程；(02)图像计算与感知智能\n\n\n\n2024年", "reference_books": "", "retest_content": "", "tuition_fee": "学制住宿元", "study_years": "住宿\n\n\n\r\n                            学费：未公布；年", "accommodation": "", "admission_requirements": ""}, "school_info": {"id": 49, "logo": "yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/dxt/schoolLogo/49.jpeg", "tag_211": 1, "tag_985": 1, "dual_class": 1, "address": "北京市海淀区中关村南大街5号", "phone": "010-68913345,010-68949926", "home_site": "https://www.bit.edu.cn/", "zsb_site": "https://admission.bit.edu.cn", "is_211": true, "is_985": true, "is_dual_class": true}, "current_year_retest_list": {"list": [{"name": "曾**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "73", "english_score": "81", "major1_score": "139", "major2_score": "124", "initial_score": "417.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "聂**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "62", "english_score": "79", "major1_score": "132", "major2_score": "129", "initial_score": "402.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "许**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "65", "english_score": "65", "major1_score": "139", "major2_score": "125", "initial_score": "394.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "陈**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "68", "english_score": "69", "major1_score": "121", "major2_score": "123", "initial_score": "381.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "史**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "60", "english_score": "75", "major1_score": "126", "major2_score": "120", "initial_score": "381.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "马**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "62", "english_score": "73", "major1_score": "135", "major2_score": "110", "initial_score": "380.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "孟**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "67", "english_score": "50", "major1_score": "132", "major2_score": "126", "initial_score": "375.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "杨*", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "56", "english_score": "71", "major1_score": "128", "major2_score": "120", "initial_score": "375.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "刘**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "58", "english_score": "66", "major1_score": "129", "major2_score": "120", "initial_score": "373.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "张**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "66", "english_score": "75", "major1_score": "131", "major2_score": "101", "initial_score": "373.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "李**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "58", "english_score": "62", "major1_score": "131", "major2_score": "121", "initial_score": "372.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "肖**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "65", "english_score": "64", "major1_score": "131", "major2_score": "110", "initial_score": "370.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "甄**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "67", "english_score": "66", "major1_score": "123", "major2_score": "114", "initial_score": "370.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "严**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "54", "english_score": "67", "major1_score": "132", "major2_score": "115", "initial_score": "368.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "张**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "58", "english_score": "59", "major1_score": "138", "major2_score": "113", "initial_score": "368.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "康**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "56", "english_score": "63", "major1_score": "138", "major2_score": "110", "initial_score": "367.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "原**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "62", "english_score": "62", "major1_score": "122", "major2_score": "115", "initial_score": "361.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "吴**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "58", "english_score": "61", "major1_score": "126", "major2_score": "115", "initial_score": "360.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "俞**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "67", "english_score": "72", "major1_score": "124", "major2_score": "92", "initial_score": "355.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "李**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "57", "english_score": "66", "major1_score": "124", "major2_score": "105", "initial_score": "352.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}], "year": 2025, "count": 20}, "current_year_admission_list": {"list": [{"name": "许**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "394", "retest_score": "93.45", "total_score": "430.625", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "曾**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "417", "retest_score": "865", "total_score": "423.625", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "史**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "381", "retest_score": "92.2", "total_score": "421", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "聂**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "402", "retest_score": "85.225", "total_score": "41463", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "张**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "373", "retest_score": "90.8", "total_score": "413.5", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "曾**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "351", "retest_score": "92.7", "total_score": "407.25", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "张**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "352", "retest_score": "91.4", "total_score": "404.5", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "陈**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "381", "retest_score": "85.55", "total_score": "404.375", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "刘**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "373", "retest_score": "85.275", "total_score": "399.688", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "李**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "352", "retest_score": "87", "total_score": "393.5", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "杨*", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "375", "retest_score": "82.3", "total_score": "393.25", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "肖**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "370", "retest_score": "82.925", "total_score": "392.313", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "张**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "368", "retest_score": "83.15", "total_score": "391.875", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "马**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "380", "retest_score": "80", "total_score": "390", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "李**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "372", "retest_score": "81.35", "total_score": "389.375", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "康**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "367", "retest_score": "78.325", "total_score": "379.313", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "孟**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "375", "retest_score": "76.4", "total_score": "378.5", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "甄**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "370", "retest_score": "77.375", "total_score": "378.438", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "严**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "368", "retest_score": "77.675", "total_score": "378.188", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "吴**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "360", "retest_score": "79.125", "total_score": "377.813", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}], "year": 2025, "count": 20}}, {"school_name": "华东师范大学", "major_name": "计算机科学与技术", "difficulty_analysis": "华东师范大学计算机科学与技术专业分数线为342分，竞争较为适中。该校计算机专业在国内有一定影响力，报考人数较多。", "suggest": "建议学生将总分目标定在350分以上，重点提升数学和专业课成绩。可以参加一些专业课的辅导班，系统复习专业知识。", "reason": "华东师范大学位于上海市，地理位置优越，计算机专业实力较强，科研和就业前景较好。竞争相对适中，适合学生报考。", "school_id": "84647", "admission_stats": {"yearly_stats": [{"total_admission": 17, "avg_initial_score": "358.529412", "max_initial_score": "390.00", "min_initial_score": "329.00", "avg_retest_score": "372.576471", "max_retest_score": "424.20", "min_retest_score": "301.00", "year": 2025}], "has_data": true}, "retest_stats": {"yearly_stats": [{"total_retest": 22, "avg_initial_score": "353.772727", "max_initial_score": "390.00", "min_initial_score": "328.00", "admission_count": 0, "year": 2025, "admission_rate": 0}], "has_data": true}, "basic_info": {"research_direction": "(00)不区分", "exam_range": "(101)思想政治理论；(201)英语（一）；(301)数学（一）；(408)计算机学科专业基础\n\n\n\n2025年初试参考图书\n\n\n\r\n                            (408)计算机学科专业基础:统考;(301)数学（一）:统考;\n\n\n\n2025年复试内容\n\n\n\r\n                            复试内容：1.上机考试：主要考查学生运用计算机编程解决问题的能力，上机语言为C, C++, Java, Python(3)。2.运用专业基础知识的综合能力（口试）。3.外语听力、口语测试。;；\n\n\n\n2025年学费学制住宿\n\n\n\r\n                            学费：8000元；学制：3年；住宿：提供住宿；\n\n\n\n\n2025年报考要求\n\n\n\r\n                            无；\n\n\n\n\n2024年研究方向\n\n\n\r\n                            (00)不区分研究方向\n\n\n\n2024年", "reference_books": "", "retest_content": "", "tuition_fee": "学制住宿元", "study_years": "住宿\n\n\n\r\n                            学费：8000元；年", "accommodation": "", "admission_requirements": ""}, "school_info": {"id": 51, "logo": "yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/dxt/schoolLogo/51.jpeg", "tag_211": 1, "tag_985": 1, "dual_class": 1, "address": "普陀校区：上海市普陀区中山北路3663号,闵行校区：上海市闵行区东川路500号", "phone": "021-62232212", "home_site": "https://www.ecnu.edu.cn/", "zsb_site": "https://zsb.ecnu.edu.cn/", "is_211": true, "is_985": true, "is_dual_class": true}, "current_year_retest_list": {"list": [{"name": "余**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "60.00", "english_score": "69.00", "major1_score": "130.00", "major2_score": "131.00", "initial_score": "390.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "马**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "58.00", "english_score": "51.00", "major1_score": "147.00", "major2_score": "125.00", "initial_score": "381.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "叶**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "66.00", "english_score": "74.00", "major1_score": "124.00", "major2_score": "115.00", "initial_score": "379.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "赵**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "60.00", "english_score": "54.00", "major1_score": "126.00", "major2_score": "130.00", "initial_score": "370.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "刘**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "69.00", "english_score": "61.00", "major1_score": "114.00", "major2_score": "122.00", "initial_score": "366.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "张**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "67.00", "english_score": "62.00", "major1_score": "110.00", "major2_score": "126.00", "initial_score": "365.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "陈**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "54.00", "english_score": "61.00", "major1_score": "129.00", "major2_score": "120.00", "initial_score": "364.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "秦**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "69.00", "english_score": "70.00", "major1_score": "116.00", "major2_score": "108.00", "initial_score": "363.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "潘**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "68.00", "english_score": "56.00", "major1_score": "135.00", "major2_score": "104.00", "initial_score": "363.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "许*", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "58.00", "english_score": "67.00", "major1_score": "127.00", "major2_score": "107.00", "initial_score": "359.00", "volunteer_type": "一志愿", "admission_status": "调剂外校", "year": 2025}, {"name": "许*", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "65.00", "english_score": "62.00", "major1_score": "130.00", "major2_score": "102.00", "initial_score": "359.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "谢**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "64.00", "english_score": "66.00", "major1_score": "123.00", "major2_score": "105.00", "initial_score": "358.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "殷**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "66.00", "english_score": "61.00", "major1_score": "108.00", "major2_score": "114.00", "initial_score": "349.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "董**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "60.00", "english_score": "59.00", "major1_score": "125.00", "major2_score": "98.00", "initial_score": "342.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "章**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "60.00", "english_score": "46.00", "major1_score": "119.00", "major2_score": "117.00", "initial_score": "342.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "王**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "58.00", "english_score": "51.00", "major1_score": "106.00", "major2_score": "124.00", "initial_score": "339.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "尚*", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "57.00", "english_score": "51.00", "major1_score": "119.00", "major2_score": "111.00", "initial_score": "338.00", "volunteer_type": "一志愿", "admission_status": "调剂外校", "year": 2025}, {"name": "朱**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "64.00", "english_score": "69.00", "major1_score": "112.00", "major2_score": "91.00", "initial_score": "336.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "郭**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "60.00", "english_score": "63.00", "major1_score": "98.00", "major2_score": "111.00", "initial_score": "332.00", "volunteer_type": "一志愿", "admission_status": "", "year": 2025}, {"name": "童**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "61.00", "english_score": "61.00", "major1_score": "108.00", "major2_score": "101.00", "initial_score": "331.00", "volunteer_type": "一志愿", "admission_status": "调剂外校", "year": 2025}], "year": 2025, "count": 20}, "current_year_admission_list": {"list": [{"name": "马**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "381.00", "retest_score": "422.60", "total_score": "401.80", "first_choice_school": "华东师范大学", "student_remark": "", "year": 2025}, {"name": "陈**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "364.00", "retest_score": "424.20", "total_score": "394.10", "first_choice_school": "华东师范大学", "student_remark": "", "year": 2025}, {"name": "赵**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "370.00", "retest_score": "412.80", "total_score": "391.40", "first_choice_school": "华东师范大学", "student_remark": "", "year": 2025}, {"name": "余**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "390.00", "retest_score": "378.00", "total_score": "384.00", "first_choice_school": "华东师范大学", "student_remark": "", "year": 2025}, {"name": "潘**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "363.00", "retest_score": "402.40", "total_score": "382.70", "first_choice_school": "华东师范大学", "student_remark": "", "year": 2025}, {"name": "张**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "365.00", "retest_score": "400.20", "total_score": "382.60", "first_choice_school": "华东师范大学", "student_remark": "", "year": 2025}, {"name": "许*", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "359.00", "retest_score": "404.40", "total_score": "381.70", "first_choice_school": "华东师范大学", "student_remark": "", "year": 2025}, {"name": "刘**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "366.00", "retest_score": "388.20", "total_score": "377.10", "first_choice_school": "华东师范大学", "student_remark": "", "year": 2025}, {"name": "谢**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "358.00", "retest_score": "356.80", "total_score": "357.40", "first_choice_school": "华东师范大学", "student_remark": "", "year": 2025}, {"name": "章**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "342.00", "retest_score": "370.00", "total_score": "356.00", "first_choice_school": "华东师范大学", "student_remark": "", "year": 2025}, {"name": "王**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "339.00", "retest_score": "371.40", "total_score": "355.20", "first_choice_school": "华东师范大学", "student_remark": "", "year": 2025}, {"name": "丁**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "329.00", "retest_score": "372.60", "total_score": "350.80", "first_choice_school": "华东师范大学", "student_remark": "", "year": 2025}, {"name": "董**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "342.00", "retest_score": "354.80", "total_score": "348.40", "first_choice_school": "华东师范大学", "student_remark": "", "year": 2025}, {"name": "叶**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "379.00", "retest_score": "312.20", "total_score": "345.60", "first_choice_school": "华东师范大学", "student_remark": "", "year": 2025}, {"name": "朱**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "336.00", "retest_score": "345.40", "total_score": "340.70", "first_choice_school": "华东师范大学", "student_remark": "", "year": 2025}, {"name": "殷**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "349.00", "retest_score": "316.80", "total_score": "332.90", "first_choice_school": "华东师范大学", "student_remark": "", "year": 2025}, {"name": "秦**", "college": "计算机科学与技术学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "363.00", "retest_score": "301.00", "total_score": "332.00", "first_choice_school": "华东师范大学", "student_remark": "", "year": 2025}], "year": 2025, "count": 17}}, {"school_name": "同济大学", "major_name": "计算机科学与技术", "difficulty_analysis": "同济大学计算机科学与技术专业分数线为330分，竞争较为适中。该校计算机专业在国内有一定影响力，报考人数较多。", "suggest": "建议学生将总分目标定在340分以上，重点提升数学和专业课成绩。可以参加一些专业课的辅导班，系统复习专业知识。", "reason": "同济大学位于上海市，地理位置优越，计算机专业实力较强，科研和就业前景较好。竞争相对适中，适合学生报考。", "school_id": "84648", "admission_stats": {"yearly_stats": [{"total_admission": 19, "avg_initial_score": "342.421053", "max_initial_score": "380.00", "min_initial_score": "312.00", "avg_retest_score": "257.133684", "max_retest_score": "291.10", "min_retest_score": "225.90", "year": 2025}], "has_data": true}, "retest_stats": {"yearly_stats": [], "has_data": false}, "basic_info": {"research_direction": "(05)仿真与多媒体处理；(03)网络与分布式计算；(04)认知与智能信息处理；(01)软件与云边计算；(02)感知与嵌入式系统\n\n\n\n2025年", "exam_range": "(101)思想政治理论；(201)英语（一）；(301)数学（一）；(408)计算机学科专业基础\n\n\n\n2025年初试参考图书\n\n\n\r\n                            (301)数学（一）:未知;(408)计算机学科专业基础:;\n\n\n\n2025年复试内容\n\n\n\r\n                            复试内容：①170003综合能力测试;；\n\n\n\n2025年学费学制住宿\n\n\n\r\n                            学费：以入学当年物价局备案为准；学制：2.5年；住宿：可以申请住宿；\n\n\n\n\n2025年报考要求\n\n\n\r\n                            不接收同等学力考生。；\n\n\n\n\n2024年研究方向\n\n\n\r\n                            (05)仿真与多媒体处理；(03)网络与分布式计算；(04)认知与智能信息处理；(01)软件与云边计算；(02)感知与嵌入式系统\n\n\n\n2024年", "reference_books": "", "retest_content": "", "tuition_fee": "学制住宿元", "study_years": "住宿\n\n\n\r\n                            学费：以入学当年", "accommodation": "", "admission_requirements": ""}, "school_info": {"id": 33, "logo": "yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/dxt/schoolLogo/33.jpeg", "tag_211": 1, "tag_985": 1, "dual_class": 1, "address": "四平路校区（主校区）：上海市杨浦区四平路1239号,嘉定校区：上海市嘉定区曹安公路4800号,沪西校区：上海市普陀区真南路500号,沪北校区：上海市静安区中山北路727号", "phone": "021-65982643,021-65981513", "home_site": "https://www.tongji.edu.cn", "zsb_site": "https://bkzs.tongji.edu.cn/", "is_211": true, "is_985": true, "is_dual_class": true}, "current_year_retest_list": {"list": [], "year": null, "count": 0}, "current_year_admission_list": {"list": [{"name": "闾**", "college": "计算机科学与技术学院（软件学院）", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "366.00", "retest_score": "291.10", "total_score": "77.29", "first_choice_school": "同济大学", "student_remark": "", "year": 2025}, {"name": "陈**", "college": "计算机科学与技术学院（软件学院）", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "380.00", "retest_score": "272.50", "total_score": "76.76", "first_choice_school": "同济大学", "student_remark": "", "year": 2025}, {"name": "陈**", "college": "计算机科学与技术学院（软件学院）", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "369.00", "retest_score": "270.30", "total_score": "75.21", "first_choice_school": "同济大学", "student_remark": "", "year": 2025}, {"name": "彭**", "college": "计算机科学与技术学院（软件学院）", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "372.00", "retest_score": "253.00", "total_score": "73.53", "first_choice_school": "同济大学", "student_remark": "", "year": 2025}, {"name": "彭**", "college": "计算机科学与技术学院（软件学院）", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "333.00", "retest_score": "282.80", "total_score": "72.42", "first_choice_school": "同济大学", "student_remark": "", "year": 2025}, {"name": "殷**", "college": "计算机科学与技术学院（软件学院）", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "367.00", "retest_score": "246.14", "total_score": "72.14", "first_choice_school": "同济大学", "student_remark": "", "year": 2025}, {"name": "朱**", "college": "计算机科学与技术学院（软件学院）", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "337.00", "retest_score": "273.50", "total_score": "71.80", "first_choice_school": "同济大学", "student_remark": "", "year": 2025}, {"name": "苏**", "college": "计算机科学与技术学院（软件学院）", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "349.00", "retest_score": "260.60", "total_score": "71.71", "first_choice_school": "同济大学", "student_remark": "", "year": 2025}, {"name": "单**", "college": "计算机科学与技术学院（软件学院）", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "351.00", "retest_score": "258.00", "total_score": "71.64", "first_choice_school": "同济大学", "student_remark": "", "year": 2025}, {"name": "杨**", "college": "计算机科学与技术学院（软件学院）", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "344.00", "retest_score": "258.40", "total_score": "70.86", "first_choice_school": "同济大学", "student_remark": "", "year": 2025}, {"name": "尹**", "college": "计算机科学与技术学院（软件学院）", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "337.00", "retest_score": "259.86", "total_score": "70.21", "first_choice_school": "同济大学", "student_remark": "", "year": 2025}, {"name": "杨**", "college": "计算机科学与技术学院（软件学院）", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "331.00", "retest_score": "253.64", "total_score": "68.77", "first_choice_school": "同济大学", "student_remark": "", "year": 2025}, {"name": "顾**", "college": "计算机科学与技术学院（软件学院）", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "330.00", "retest_score": "252.60", "total_score": "68.53", "first_choice_school": "同济大学", "student_remark": "", "year": 2025}, {"name": "石*", "college": "计算机科学与技术学院（软件学院）", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "328.00", "retest_score": "247.50", "total_score": "67.70", "first_choice_school": "同济大学", "student_remark": "", "year": 2025}, {"name": "杨**", "college": "计算机科学与技术学院（软件学院）", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "318.00", "retest_score": "256.86", "total_score": "67.61", "first_choice_school": "同济大学", "student_remark": "", "year": 2025}, {"name": "丁**", "college": "计算机科学与技术学院（软件学院）", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "331.00", "retest_score": "233.14", "total_score": "66.37", "first_choice_school": "同济大学", "student_remark": "", "year": 2025}, {"name": "孟**", "college": "计算机科学与技术学院（软件学院）", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "312.00", "retest_score": "249.00", "total_score": "65.98", "first_choice_school": "同济大学", "student_remark": "", "year": 2025}, {"name": "常**", "college": "计算机科学与技术学院（软件学院）", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "333.00", "retest_score": "225.90", "total_score": "65.76", "first_choice_school": "同济大学", "student_remark": "", "year": 2025}, {"name": "宋**", "college": "计算机科学与技术学院（软件学院）", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "318.00", "retest_score": "240.70", "total_score": "65.72", "first_choice_school": "同济大学", "student_remark": "", "year": 2025}], "year": 2025, "count": 19}}, {"school_name": "浙江大学", "major_name": "计算机科学与技术", "difficulty_analysis": "浙江大学计算机科学与技术专业分数线较高，竞争非常激烈。该校计算机专业在国内顶尖，报考人数众多，录取比例较低。", "suggest": "建议学生将总分目标定在370分以上，重点提升数学和专业课成绩。可以参加一些高水平的专业课辅导班，系统复习专业知识。", "reason": "浙江大学计算机专业实力强，科研和就业前景极好。虽然竞争激烈，但通过努力仍有希望。"}, {"school_name": "复旦大学", "major_name": "计算机科学与技术", "difficulty_analysis": "复旦大学计算机科学与技术专业分数线较高，竞争非常激烈。该校计算机专业在国内顶尖，报考人数众多，录取比例较低。", "suggest": "建议学生将总分目标定在370分以上，重点提升数学和专业课成绩。可以参加一些高水平的专业课辅导班，系统复习专业知识。", "reason": "复旦大学计算机专业实力强，科研和就业前景极好。虽然竞争激烈，但通过努力仍有希望。"}], "high_recommend_list": [{"school_name": "北京理工大学", "major_name": "计算机科学与技术", "reason": "北京理工大学计算机专业实力较强，科研和就业前景较好。竞争相对适中，适合学生报考。且预估分数与分数线接近，通过努力有较大希望录取。", "school_id": "75581", "admission_stats": {"yearly_stats": [{"total_admission": 22, "avg_initial_score": "372.590909", "max_initial_score": "417.00", "min_initial_score": "351.00", "avg_retest_score": "118.445909", "max_retest_score": "865.00", "min_retest_score": "66.30", "year": 2025}], "has_data": true}, "retest_stats": {"yearly_stats": [{"total_retest": 22, "avg_initial_score": "372.590909", "max_initial_score": "417.00", "min_initial_score": "351.00", "admission_count": 0, "year": 2025, "admission_rate": 0}], "has_data": true}, "basic_info": {"research_direction": "(06)计算机体系结构与先进网络；(05)可视媒体计算；(04)数据科学与知识工程；(01)语言智能与社会计算；(03)软件智能与软件工程；(02)图像计算与感知智能\n\n\n\n2025年", "exam_range": "(101)思想政治理论；(201)英语（一）；(301)数学（一）；(408)计算机学科专业基础\n\n\n\n2025年初试参考图书\n\n\n\r\n                            (301)数学（一）((207:未知;(408)计算机学科专业基础((207:未知;\n\n\n\n2025年复试内容\n\n\n\r\n                            复试内容：笔试科目：C/C++语言程序设计（上机）面试内容：外语口语听力测试；计算机专业相关基础与专业知识;；\n\n\n\n2025年学费学制住宿\n\n\n\r\n                            学费：未公布；学制：未公布；住宿：未公布；\n\n\n\n\n2025年报考要求\n\n\n\r\n                            无；\n\n\n\n\n2024年研究方向\n\n\n\r\n                            (06)计算机体系结构与先进网络；(05)可视媒体计算；(04)数据科学与知识工程；(01)语言智能与社会计算；(03)软件智能与软件工程；(02)图像计算与感知智能\n\n\n\n2024年", "reference_books": "", "retest_content": "", "tuition_fee": "学制住宿元", "study_years": "住宿\n\n\n\r\n                            学费：未公布；年", "accommodation": "", "admission_requirements": ""}, "school_info": {"id": 49, "logo": "yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/dxt/schoolLogo/49.jpeg", "tag_211": 1, "tag_985": 1, "dual_class": 1, "address": "北京市海淀区中关村南大街5号", "phone": "010-68913345,010-68949926", "home_site": "https://www.bit.edu.cn/", "zsb_site": "https://admission.bit.edu.cn", "is_211": true, "is_985": true, "is_dual_class": true}, "current_year_retest_list": {"list": [{"name": "曾**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "73", "english_score": "81", "major1_score": "139", "major2_score": "124", "initial_score": "417.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "聂**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "62", "english_score": "79", "major1_score": "132", "major2_score": "129", "initial_score": "402.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "许**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "65", "english_score": "65", "major1_score": "139", "major2_score": "125", "initial_score": "394.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "陈**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "68", "english_score": "69", "major1_score": "121", "major2_score": "123", "initial_score": "381.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "史**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "60", "english_score": "75", "major1_score": "126", "major2_score": "120", "initial_score": "381.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "马**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "62", "english_score": "73", "major1_score": "135", "major2_score": "110", "initial_score": "380.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "孟**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "67", "english_score": "50", "major1_score": "132", "major2_score": "126", "initial_score": "375.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "杨*", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "56", "english_score": "71", "major1_score": "128", "major2_score": "120", "initial_score": "375.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "刘**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "58", "english_score": "66", "major1_score": "129", "major2_score": "120", "initial_score": "373.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "张**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "66", "english_score": "75", "major1_score": "131", "major2_score": "101", "initial_score": "373.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "李**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "58", "english_score": "62", "major1_score": "131", "major2_score": "121", "initial_score": "372.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "肖**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "65", "english_score": "64", "major1_score": "131", "major2_score": "110", "initial_score": "370.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "甄**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "67", "english_score": "66", "major1_score": "123", "major2_score": "114", "initial_score": "370.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "严**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "54", "english_score": "67", "major1_score": "132", "major2_score": "115", "initial_score": "368.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "张**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "58", "english_score": "59", "major1_score": "138", "major2_score": "113", "initial_score": "368.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "康**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "56", "english_score": "63", "major1_score": "138", "major2_score": "110", "initial_score": "367.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "原**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "62", "english_score": "62", "major1_score": "122", "major2_score": "115", "initial_score": "361.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "吴**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "58", "english_score": "61", "major1_score": "126", "major2_score": "115", "initial_score": "360.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "俞**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "67", "english_score": "72", "major1_score": "124", "major2_score": "92", "initial_score": "355.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}, {"name": "李**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "politics_score": "57", "english_score": "66", "major1_score": "124", "major2_score": "105", "initial_score": "352.00", "volunteer_type": "一志愿", "admission_status": "一志愿", "year": 2025}], "year": 2025, "count": 20}, "current_year_admission_list": {"list": [{"name": "许**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "394", "retest_score": "93.45", "total_score": "430.625", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "曾**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "417", "retest_score": "865", "total_score": "423.625", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "史**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "381", "retest_score": "92.2", "total_score": "421", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "聂**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "402", "retest_score": "85.225", "total_score": "41463", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "张**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "373", "retest_score": "90.8", "total_score": "413.5", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "曾**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "351", "retest_score": "92.7", "total_score": "407.25", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "张**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "352", "retest_score": "91.4", "total_score": "404.5", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "陈**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "381", "retest_score": "85.55", "total_score": "404.375", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "刘**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "373", "retest_score": "85.275", "total_score": "399.688", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "李**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "352", "retest_score": "87", "total_score": "393.5", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "杨*", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "375", "retest_score": "82.3", "total_score": "393.25", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "肖**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "370", "retest_score": "82.925", "total_score": "392.313", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "张**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "368", "retest_score": "83.15", "total_score": "391.875", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "马**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "380", "retest_score": "80", "total_score": "390", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "李**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "372", "retest_score": "81.35", "total_score": "389.375", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "康**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "367", "retest_score": "78.325", "total_score": "379.313", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "孟**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "375", "retest_score": "76.4", "total_score": "378.5", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "甄**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "370", "retest_score": "77.375", "total_score": "378.438", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "严**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "368", "retest_score": "77.675", "total_score": "378.188", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}, {"name": "吴**", "college": "计算机学院", "major_code": "081200", "major_name": "计算机科学与技术", "initial_score": "360", "retest_score": "79.125", "total_score": "377.813", "first_choice_school": "北京理工大学", "student_remark": "", "year": 2025}], "year": 2025, "count": 20}}], "school_list": [{"id": 1, "school_name": "南京大学", "region": "华东", "city": "江苏省", "college": "智能科学与技术学..", "major_name": "081200计算机科学与技术", "major_code": "081200", "min_score": 367, "score_diff": -17, "tags": ["985", "211", "双一流"], "tag_985": true, "tag_211": true, "tag_double": true}, {"id": 2, "school_name": "中国科学技术大学", "region": "华东", "city": "安徽省", "college": "计算机科学与技术..", "major_name": "081200计算机科学与技术", "major_code": "081200", "min_score": 363, "score_diff": -13, "tags": ["985", "211", "双一流"], "tag_985": true, "tag_211": true, "tag_double": true}, {"id": 3, "school_name": "北京大学", "region": "华北", "city": "北京市", "college": "深圳研究生院", "major_name": "081200计算机科学与技术", "major_code": "081200", "min_score": 359, "score_diff": -9, "tags": ["985", "211", "双一流"], "tag_985": true, "tag_211": true, "tag_double": true}, {"id": 4, "school_name": "北京航空航天大学", "region": "华北", "city": "北京市", "college": "计算机学院", "major_name": "081200计算机科学与技术", "major_code": "081200", "min_score": 356, "score_diff": -6, "tags": ["985", "211", "双一流"], "tag_985": true, "tag_211": true, "tag_double": true}, {"id": 5, "school_name": "清华大学", "region": "华北", "city": "北京市", "college": "计算机科学与技术..", "major_name": "081200计算机科学与技术", "major_code": "081200", "min_score": 350, "score_diff": 0, "tags": ["985", "211", "双一流"], "tag_985": true, "tag_211": true, "tag_double": true}, {"id": 6, "school_name": "北京理工大学", "region": "华北", "city": "北京市", "college": "计算机学院", "major_name": "081200计算机科学与技术", "major_code": "081200", "min_score": 343, "score_diff": 7, "tags": ["985", "211", "双一流"], "tag_985": true, "tag_211": true, "tag_double": true}, {"id": 7, "school_name": "华东师范大学", "region": "华东", "city": "上海市", "college": "计算机科学与技术..", "major_name": "081200计算机科学与技术", "major_code": "081200", "min_score": 342, "score_diff": 8, "tags": ["985", "211", "双一流"], "tag_985": true, "tag_211": true, "tag_double": true}, {"id": 8, "school_name": "同济大学", "region": "华东", "city": "上海市", "college": "计算机科学与技术..", "major_name": "081200计算机科学与技术", "major_code": "081200", "min_score": 330, "score_diff": 20, "tags": ["985", "211", "双一流"], "tag_985": true, "tag_211": true, "tag_double": true}]}}