<template>
  <div class="page-footer">
    <div class="footer-bg">
      <div class="page-number">
        {{ prop.pageNumber }} / {{ prop.totalPages }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from "vue";
const prop = defineProps({
  pageNumber: {
    type: Number,
    default: 1,
  },
  totalPages: {
    type: Number,
    default: 1,
  },
});
</script>

<style scoped lang="less">
/* 页脚样式 */
.page-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 80px;

  .footer-bg {
    position: relative;
    height: 100%;

    img {
      height: 100%;
      width: auto;
    }

    .page-number {
      background-image: url("https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/bottom-left-bg.png");
      background-repeat: no-repeat;
      background-size: contain;
      width: 70px;
      height: 20px;
      position: absolute;
      bottom: 20px;
      right: 40px;
      font-weight: bold;
      font-size: 14px;
      padding-left: 18px;
    }
  }
}
</style>
