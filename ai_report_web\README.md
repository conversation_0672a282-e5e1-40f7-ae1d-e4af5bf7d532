# 后台管理系统

基于 Vite + Vue3 + TypeScript + ElementPlus + Pinia + Vue Router + Axios 的后台管理系统。

## 技术栈

- **前端框架**：Vue 3
- **构建工具**：Vite
- **编程语言**：TypeScript
- **UI 框架**：Element Plus
- **状态管理**：Pinia
- **路由管理**：Vue Router
- **HTTP 请求**：Axios

## 功能特性

- 用户登录/登出
- 权限控制
- 响应式布局
- 主题定制
- 动态路由

## 项目结构

```
├── public/               # 静态资源
├── src/
│   ├── assets/           # 资源文件
│   ├── components/       # 公共组件
│   ├── layout/           # 布局组件
│   ├── router/           # 路由配置
│   ├── store/            # Pinia 状态管理
│   ├── types/            # TypeScript 类型定义
│   ├── utils/            # 工具函数
│   ├── views/            # 页面
│   ├── App.vue           # 根组件
│   ├── main.ts           # 入口文件
│   ├── env.d.ts          # 环境变量类型定义
│   └── style.css         # 全局样式
├── .env                  # 环境变量
├── index.html            # HTML 模板
├── package.json          # 依赖管理
├── tsconfig.json         # TypeScript 配置
└── vite.config.ts        # Vite 配置
```

## 开发

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览生产构建
npm run preview
```

## 浏览器支持

支持所有现代浏览器，以及 IE 11+（需要额外的 polyfill）。
