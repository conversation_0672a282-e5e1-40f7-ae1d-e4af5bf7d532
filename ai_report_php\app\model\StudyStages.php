<?php

namespace app\model;

use think\Model;
use think\facade\Db;
use support\Log;

/**
 * 学习阶段模型
 */
class StudyStages extends Model
{
    protected $name = 'study_stages';
    
    // 设置字段信息
    protected $schema = [
        'id'          => 'int',
        'report_id'   => 'int',
        'title'       => 'string',
        'sort_order'  => 'int',
        'created_at'  => 'datetime',
        'updated_at'  => 'datetime',
    ];
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    /**
     * 根据报告ID删除学习阶段数据
     *
     * @param int $reportId 报告ID
     * @return bool
     */
    public static function deleteByReportId($reportId)
    {
        return self::where('report_id', $reportId)->delete();
    }

    /**
     * 批量插入学习阶段数据
     *
     * @param int $reportId 报告ID
     * @param array $stages 学习阶段数据
     * @return array 返回插入的阶段ID映射
     */
    public static function insertBatch($reportId, $stages)
    {
        if (empty($stages) || !is_array($stages)) {
            return [];
        }

        $stageIdMap = []; // 用于存储原始stage_id与数据库id的映射关系
        
        foreach ($stages as $index => $stage) {
            $stageData = [
                'report_id' => $reportId,
                'title' => $stage['title'] ?? '',
                'sort_order' => $index + 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ];

            // 使用insertGetId方法直接获取插入后的ID
            try {
                $insertedId = self::insertGetId($stageData);

                if ($insertedId) {
                    // 记录原始stage_id与数据库id的映射
                    $originalStageId = $stage['id'] ?? 'stage' . ($index + 1);
                    $stageIdMap[$originalStageId] = $insertedId;
                    Log::info("学习阶段保存成功，原始ID: {$originalStageId}, 数据库ID: {$insertedId}");
                } else {
                    Log::error("学习阶段插入失败，数据: " . json_encode($stageData));
                }
            } catch (\Exception $e) {
                Log::error("学习阶段插入异常: " . $e->getMessage() . ", 数据: " . json_encode($stageData));
            }
        }

        return $stageIdMap;
    }

    /**
     * 根据报告ID获取学习阶段数据
     *
     * @param int $reportId 报告ID
     * @return array
     */
    public static function getByReportId($reportId)
    {
        return self::where('report_id', $reportId)
            ->order('sort_order', 'asc')
            ->select()
            ->toArray();
    }
}
