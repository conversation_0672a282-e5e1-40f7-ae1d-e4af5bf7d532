package config

import (
	"log"
	"os"

	"gopkg.in/yaml.v3"
)

type Server struct {
	Port int    `yaml:"port"`
	Mode string `yaml:"mode"`
}
type AliQwRecommend struct {
	Appid string `yaml:"appid"`
	Key   string `yaml:"key"`
}

type Mysql struct {
	Port     int    `yaml:"port"`
	Host     string `yaml:"host"`
	User     string `yaml:"user"`
	Passwd   string `yaml:"passwd"`
	Database string `yaml:"database"`
}

type Config struct {
	Server         Server         `ymal:"server"`
	AliQwRecommend AliQwRecommend `yaml:"aliqwrecommend"`
	Mysql          Mysql          `yaml:"mysql"`
}

var GlobalConfig Config

func InitConfig(configpath string) error {
	file, err := os.ReadFile(configpath)
	if err != nil {
		log.Fatalln("打开配置文件出错")
		return err
	}
	err = yaml.Unmarshal(file, &GlobalConfig)
	if err != nil {
		log.Fatalln("解析配置文件出错")
		return err
	}
	return nil
}
