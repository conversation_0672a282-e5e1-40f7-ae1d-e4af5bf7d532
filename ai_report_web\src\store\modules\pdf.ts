import { defineStore } from 'pinia'
import { ref } from 'vue'

/**
 * PDF状态管理
 * 用于存储PDF URL
 */
export const usePdfStore = defineStore('pdf', () => {
  // PDF URL存储
  const pdfUrl = ref<string>('')

  /**
   * 设置PDF URL
   * @param url PDF的完整URL
   */
  const setPdfUrl = (url: string) => {
    pdfUrl.value = url
    console.log('PDF URL已更新:', url)
  }

  /**
   * 清除PDF URL
   */
  const clearPdfUrl = () => {
    pdfUrl.value = ''
    console.log('PDF URL已清除')
  }

 

  return {
    // 状态
    pdfUrl,

    // 方法
    setPdfUrl,
    clearPdfUrl,
  }
})
