<?php
use Webman\Route;

// 管理功能相关接口
Route::group("/api", function () {
    // 标签管理
    Route::get('/tag/all', [app\controller\TagController::class, 'getAll']);
    Route::post('/tag/add', [app\controller\TagController::class, 'add']);
    Route::post('/tag/edit', [app\controller\TagController::class, 'edit']);
    Route::get('/tag/delete', [app\controller\TagController::class, 'delete']);
})->middleware([app\middleware\JwtMiddleware::class]); 