package model

import (
	"go-api/db"
)

// BaSchoolReport 映射数据库中的 `ba_school_report` 表
// 择校报告表
type BaSchoolReport struct {
	// ID 报告id, int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY
	// GORM 默认会将名为 `ID` 的 `uint` 字段视为主键
	ID uint `gorm:"primaryKey;autoIncrement" json:"id"`

	// StudentID 学生id, int(11) unsigned NOT NULL
	StudentID uint `gorm:"column:student_id;not null;index:idx_student_id" json:"student_id"`

	// AdminID 创建报告的用户ID, 关联的是user表, int(11) DEFAULT NULL
	// 使用指针类型表示可为空的字段
	AdminID *uint `gorm:"column:admin_id" json:"admin_id"`

	// UndergraduateMajorName 本科专业名称, varchar(255) DEFAULT NULL
	UndergraduateMajorName string `gorm:"column:undergraduate_major_name" json:"undergraduate_major_name"`

	// UndergraduateSchoolName 本科院校名称, varchar(255) DEFAULT NULL
	UndergraduateSchoolName string `gorm:"column:undergraduate_school_name" json:"undergraduate_school_name"`

	// IsCrossMajor 是否跨专业：1-是,0-否, tinyint(1) DEFAULT '0'
	// GORM 会将 bool 类型映射为 tinyint(1)
	IsCrossMajor bool `gorm:"column:is_cross_major;default:0" json:"is_cross_major"`

	// EducationalStyle 培养方式 0：全日制，1：非全日制, tinyint(1) unsigned DEFAULT '0'
	EducationalStyle uint8 `gorm:"column:educational_style;default:0" json:"educational_style"`

	// TargetMajor 目标专业名称, text DEFAULT NULL
	TargetMajor string `gorm:"column:target_major" json:"target_major"`

	// TargetMajorCode 目标专业id, text DEFAULT NULL
	TargetMajorCode string `gorm:"column:target_major_code" json:"target_major_code"`

	// MajorCode 专业代码, text DEFAULT NULL
	MajorCode string `gorm:"column:major_code" json:"major_code"`

	// DreamSchoolID 梦校id, int(11) DEFAULT NULL
	DreamSchoolID *uint `gorm:"column:dream_school_id" json:"dream_school_id"`

	// DreamSchool 梦校名称, varchar(100) DEFAULT NULL
	DreamSchool string `gorm:"column:dream_school" json:"dream_school"`

	// TargetRegion 地区倾向, varchar(50) DEFAULT NULL
	TargetRegion string `gorm:"column:target_region" json:"target_region"`

	// TargetProvinces 省份选择，直接存储汉字, varchar(255) DEFAULT NULL
	TargetProvinces string `gorm:"column:target_provinces" json:"target_provinces"`

	// SchoolLevel 院校层次, varchar(50) DEFAULT NULL
	SchoolLevel string `gorm:"column:school_level" json:"school_level"`

	// ReferenceBooks 专业课指定参考书, varchar(255) DEFAULT NULL
	ReferenceBooks string `gorm:"column:reference_books" json:"reference_books"`

	// PoliticsScore 政治预估分数, varchar(10) DEFAULT NULL
	PoliticsScore string `gorm:"column:politics_score" json:"politics_score"`

	// EnglishScore 英语预估分数, varchar(10) DEFAULT NULL
	EnglishScore string `gorm:"column:english_score" json:"english_score"`

	// EnglishType 英语类型（英语一/英语二）, varchar(20) DEFAULT NULL
	EnglishType string `gorm:"column:english_type" json:"english_type"`

	// MathScore 数学预估分数, varchar(10) DEFAULT NULL
	MathScore string `gorm:"column:math_score" json:"math_score"`

	// MathType 数学类型（数学一/数学二/数学三/199管理类联考/396经济类联考）, varchar(50) DEFAULT NULL
	MathType string `gorm:"column:math_type" json:"math_type"`

	// ProfessionalScore 专业课预估分数, varchar(10) DEFAULT NULL
	ProfessionalScore string `gorm:"column:professional_score" json:"professional_score"`

	// TotalScore 总分预估, varchar(10) DEFAULT NULL
	TotalScore string `gorm:"column:total_score" json:"total_score"`

	// PersonalNeeds 个性化需求, text DEFAULT NULL
	PersonalNeeds string `gorm:"column:personal_needs" json:"personal_needs"`

	// WeakModules 薄弱模块, text DEFAULT NULL
	WeakModules string `gorm:"column:weak_modules" json:"weak_modules"`

	// PdfURL pdf 地址, varchar(255) DEFAULT NULL
	PdfURL string `gorm:"column:pdf_url" json:"pdf_url"`

	// UpdatePdfTime 更新pdf 链接的时间, bigint(15) unsigned DEFAULT NULL
	// 使用指针类型表示可为空的字段，并映射到 bigint
	UpdatePdfTime *uint64 `gorm:"column:update_pdf_time" json:"update_pdf_time"`

	// CreateTime 创建时间, int(11) DEFAULT NULL
	// 使用指针类型表示可为空的字段，并映射到 int
	CreateTime *int64 `gorm:"column:create_time" json:"create_time"`

	// UpdateTime 更新时间, int(11) DEFAULT NULL
	// 使用指针类型表示可为空的字段，并映射到 int
	UpdateTime *int64 `gorm:"column:update_time" json:"update_time"`

	// IsDelete 是否删除：0未删除，1已删除, tinyint(1) DEFAULT '0'
	IsDelete bool `gorm:"column:is_delete;default:0" json:"is_delete"`

	// Context 提交给ai的上下文, longtext DEFAULT NULL
	Context string `gorm:"column:context" json:"context"`

	// IDCode 随机id标识, varchar(32) DEFAULT NULL
	IDCode string `gorm:"column:idcode" json:"idcode"`

	// 你可以选择嵌入 gorm.Model，但由于你的表有自定义的 create_time, update_time, is_delete，
	// 并且它们的类型是 int(11) 和 tinyint(1)，与 gorm.Model 默认的 time.Time 和 gorm.DeletedAt 不符，
	// 所以这里选择手动定义这些字段。
	// 如果你希望使用 GORM 的自动时间戳和软删除功能，并且可以接受 time.Time 类型，
	// 则可以考虑调整数据库字段类型或使用 GORM 的 Hooks 来处理类型转换。
}

// TableName 方法返回该模型对应的数据库表名
// GORM 默认会将结构体名转换为蛇形复数形式作为表名（例如 BaSchoolReport -> ba_school_reports）
// 如果你的表名是 `ba_school_report` (单数形式)，则需要手动指定
func (BaSchoolReport) TableName() string {
	return "ba_school_report"
}

// 根据 id 获取content
func (r *BaSchoolReport) GetContextByID(id uint) (string, error) {
	var report BaSchoolReport
	result := db.DB.First(&report, id)
	if result.Error != nil {
		return "", result.Error
	}
	return report.Context, nil
}
