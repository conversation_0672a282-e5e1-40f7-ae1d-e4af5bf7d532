import{_ as Hs}from"./_plugin-vue_export-helper-BCEt7Ct6.js";import{g as Us}from"./school-BRpLdP4V.js";import{_ as r}from"./tag-Ch_qj8li.js";import{init as Ws}from"./index-YsyrxmwF.js";import{r as Gs,g as Js,a as Ks}from"./report-CU5jWwkq.js";import{m as Qs,r as _,n as Xs,f as Ys,k as z,c as i,p as Zs,b as s,v as st,t as e,q as b,F as y,s as k,d as D,o as a}from"./index-Cu_gl4pu.js";const tt="/assets/logo-BpQfJcvm.png",et="/assets/person_base1-BU3i2X6_.png",lt="/assets/professional_analysis-BZnWKqOx.png",ot="/assets/school_overview-B3uDg_ab.png",nt="/assets/school_analysis-DeHbumP5.png",it="/assets/five_png-BC1ovPFV.png",at={class:"home-bg"},dt={class:"content-wrapper"},ct={class:"section"},rt={class:"card"},vt={class:"info-section"},ut={class:"info-block"},_t={class:"info-block-content"},mt={class:"info-block-item"},gt={class:"info-block-item-content"},ft={class:"info-block-item"},bt={class:"info-block-item-content"},pt={class:"info-block-item"},ht={class:"info-block-item-content"},yt={class:"info-block-item"},kt={class:"info-block-item-content"},xt={class:"info-block-item"},St={class:"info-block-item-content"},jt={class:"info-block-item"},Ct={class:"info-block-item-content"},At={class:"info-block-item"},Bt={class:"info-block-item-content"},wt={class:"info-block-item"},Lt={class:"info-block-item-content"},Mt={class:"info-block-item major-block"},Nt={class:"info-block-item-content"},Tt={class:"info-block-item major-block"},zt={class:"info-block-item-content"},Dt={class:"info-block-content"},Rt={class:"info-block-item-title"},Pt={class:"info-block-item-content"},$t={class:"info-block-content"},Ft={class:"info-block-item"},Wt={class:"info-block-item-content"},It={class:"info-block-item"},Ot={class:"info-block-item-content"},Vt={class:"info-block-item"},Et={class:"info-block-item-content"},qt={class:"info-block-item"},Ht={class:"info-block-item-content"},Ut={class:"info-block-item"},Gt={class:"info-block-item-content"},Jt={class:"info-block-item"},Kt={class:"info-block-item-content"},Qt={class:"info-other-block-content"},Xt={class:"info-other-block-item info_area"},Yt={class:"info-other-block-item-content"},Zt={class:"info-other-block-item"},se={class:"info-other-block-item-content"},te={class:"info-other-block-content"},ee={class:"info-other-block-item info_area"},le={class:"info-other-block-item-content"},oe={class:"info-other-block-item"},ne={class:"info-other-block-item-content"},ie={class:"info-other-block-content"},ae={class:"info-other-block-item",style:{"flex-basis":"100%"}},de={class:"info-other-block-item-content"},ce={class:"info-section"},re={class:"info-block"},ve={class:"score-table"},ue={class:"table-row-score"},_e={class:"td-cell"},me={class:"td-cell"},ge={class:"td-cell"},fe={class:"td-cell"},be={class:"td-cell"},pe={class:"personal-demands"},he={class:"demands-label"},ye={class:"expertise-advice"},ke={class:"advice-label"},xe={class:"step-content"},Se={class:"subject_info"},je={class:"subject_info"},Ce={class:"step-content"},Ae={class:"school-table-container"},Be={class:"school-table-body"},we={class:"body-cell"},Le={class:"body-cell school-name"},Me={class:"school-tags"},Ne={key:0,class:"tag tag-985"},Te={key:1,class:"tag tag-211"},ze={key:2,class:"tag tag-double"},De={class:"body-cell"},Re={class:"body-cell"},Pe={class:"body-cell"},$e={class:"body-cell"},Fe={class:"body-cell"},We={class:"body-cell"},Ie={class:"body-cell"},Oe={class:"school-detail-card"},Ve={class:"school-header"},Ee={class:"school-logo"},qe=["src"],He={class:"school-info"},Ue={class:"school-title"},Ge={class:"school-location"},Je={class:"school-tags-row"},Ke={key:0,class:"tag tag_other tag-double"},Qe={key:1,class:"tag tag_other tag-985"},Xe={key:2,class:"tag tag_other tag-211"},Ye={class:"reexam-container"},Ze={class:"reexam-card"},sl={class:"reexam-content"},tl={class:"reexam-card"},el={class:"reexam-content"},ll={class:"reexam-card"},ol={class:"reexam-content last-reexam-content"},nl={class:"reexam-container"},il={class:"reexam-card"},al={class:"reexam-content"},dl={class:"reexam-card"},cl={class:"reexam-content last-reexam-content"},rl={class:"step-content-zs"},vl={key:0,class:"admission-title"},ul={class:"admission-table"},_l={class:"admission-table"},ml={class:"reexam-container"},gl={class:"reexam-card"},fl={class:"reexam-content"},bl={class:"admission-table"},pl={class:"reexam-container"},hl={class:"reexam-card"},yl={class:"reexam-content"},kl={class:"reexam-card"},xl={class:"reexam-content last-reexam-content"},Sl={class:"recommend-school-container"},jl={class:"recommend-school-card"},Cl={class:"recommend-school-content"},Al={class:"recommend-school-container"},Bl={class:"recommend-school-content"},wl={class:"recommend-school-content"},Ll={class:"recommend-school-content"},Ml={class:"info-section"},Nl={class:"info-block"},Tl={class:"score-table target_score-table"},zl={class:"table-row-score"},Dl={class:"td-cell"},Rl={class:"td-cell"},Pl={class:"td-cell"},$l={class:"td-cell"},Fl={class:"td-cell"},Wl={class:"info-section"},Il={class:"info-block"},Ol={class:"first_stage"},Vl={class:"first_stage_content"},El={class:"stage_module"},ql={class:"recommend-school-content"},Hl={class:"recommend-school-content"},Ul={class:"recommend-school-content"},Gl={class:"recommend-school-content"},Jl={class:"report_suggest"},Kl={class:"report_suggest_content"},Ql=Qs({__name:"show_report",setup(Xl){const Is=Ys(),R=_(!0),o=_(),p=_(),w=_(),v=_({subject_code:"",subject_name:"",years:[],a_total:[],a_single_100:[],a_single_over100:[],b_total:[],b_single_100:[],b_single_over100:[]}),x=_({weakModuleAnalysis:[],studyPlanning:{title:"初试各科学习规划",stages:[]},comprehensiveAdvice:""});_([]);const C=_(),A=_();let L=null,M=null;const N=_("");function Os(n){if(!n||n.length<2)return"";const t=n.substring(0,2);return{"01":"哲学","02":"经济学","03":"法学","04":"教育学","05":"文学","06":"历史学","07":"理学","08":"工学","09":"农学",10:"医学",11:"军事学",12:"管理学",13:"艺术学"}[t]||""}Xs(()=>{const n=Is.currentRoute.value.params.idcode;N.value=Array.isArray(n)?n[0]:n||"",Gs(N.value).then(u=>{o.value=u.data.studentInfo,p.value=u.data.report_info,o.value&&o.value.majorCode&&(o.value.firstLevelSubject=Os(o.value.majorCode)),w.value=u.data.report,t(),R.value=!1}),Vs(),Es();const t=()=>{var m,f;if(!((m=o.value)!=null&&m.majorCode))return;let g=((f=o.value)==null?void 0:f.majorCode.split(","))[0].substring(0,4);Us(g).then(B=>{console.log(B),v.value=B.data,qs()})}});const Vs=async()=>{try{const n=await Js({report_id:N.value});n.code===0&&n.data?(x.value=n.data,console.log("开始渲染学习计划数据:",x.value)):(console.error("获取学习计划失败:",n.msg),z.error(n.msg||"获取学习计划失败"))}catch(n){console.error("获取学习计划异常:",n),z.error("获取学习计划失败")}},S=_(),Es=async()=>{try{const n=await Ks(N.value);n.code===0&&n.data?(S.value=n.data,console.log("开始渲染学习计划数据:",S.value)):(console.error("获取学习计划失败:",n.msg),z.error(n.msg||"获取学习计划失败"))}catch(n){console.error("获取学习计划异常:",n),z.error("获取学习计划失败")}},P=_(""),$=_(""),F=(n=!0)=>{const t=v.value.years.length>0?v.value.years:["2021","2022","2023","2024","2025"],u=n?v.value.a_total.length>0?v.value.a_total:[360,370,360,370,370]:v.value.b_total.length>0?v.value.b_total:[340,350,340,350,350],g=n?v.value.a_single_100.length>0?v.value.a_single_100:[60,65,60,65,65]:v.value.b_single_100.length>0?v.value.b_single_100:[55,60,55,60,60],m=n?v.value.a_single_over100.length>0?v.value.a_single_over100:[90,95,90,95,95]:v.value.b_single_over100.length>0?v.value.b_single_over100:[85,90,85,90,90];return console.log(`${n?"A区":"B区"}图表数据:`,{years:t,totalScores:u,single100Scores:g,singleOver100Scores:m}),{title:{text:"",left:"left",textStyle:{fontSize:16,fontWeight:"bold",color:"#1bb394"},top:10},grid:{left:10,right:20,top:30,bottom:15,containLabel:!0},legend:{data:["总分","单科(满分=100)","单科(满分>100)"],right:10,top:10,icon:"rect",itemWidth:16,itemHeight:8,textStyle:{fontSize:12}},tooltip:{trigger:"axis",backgroundColor:"rgba(0,0,0,0.7)",borderRadius:8,textStyle:{color:"#fff"}},xAxis:{type:"category",boundaryGap:!1,data:t,axisLine:{lineStyle:{color:"#1bb394"}},axisLabel:{color:"#666"}},yAxis:{type:"value",min:0,max:400,interval:100,splitLine:{lineStyle:{color:"#eee"}},axisLine:{show:!1},axisLabel:{color:"#666",fontSize:6}},series:[{name:"总分",type:"line",smooth:!0,symbol:"circle",symbolSize:6,areaStyle:{color:"rgba(255, 153, 0, 0.15)",origin:"start"},lineStyle:{color:"#ff9900",width:2},label:{show:!0,position:"top",fontSize:12,color:"#ff9900",fontWeight:"bold",offset:[0,3],formatter:function(f){return f.value}},data:u},{name:"单科(满分=100)",type:"line",smooth:!0,symbol:"circle",symbolSize:6,areaStyle:{color:"rgba(27, 179, 148, 0.15)",origin:"start"},lineStyle:{color:"#1bb394",width:2},label:{show:!0,position:"top",fontSize:12,color:"#1bb394",fontWeight:"bold",offset:[0,5],formatter:function(f){return f.value}},data:g},{name:"单科(满分>100)",type:"line",smooth:!0,symbol:"circle",symbolSize:6,areaStyle:{color:"rgba(255, 99, 132, 0.10)",origin:"start"},lineStyle:{color:"#ff6384",width:2},label:{show:!0,position:"top",fontSize:12,color:"#ff6384",fontWeight:"bold",offset:[0,-5],formatter:function(f){return f.value}},data:m}]}},qs=()=>{if(console.log("开始初始化图表..."),console.log("echartsARef.value:",C.value),console.log("echartsBRef.value:",A.value),C.value){const n=C.value.getBoundingClientRect();if(console.log("A区图表容器尺寸:",n),console.log("A区容器样式:",window.getComputedStyle(C.value)),n.width>0&&n.height>0)try{L&&(console.log("销毁已存在的A区图表"),L.dispose()),console.log("开始初始化A区图表"),L=Ws(C.value);const t=F(!0);if(console.log("A区图表配置:",t),L.setOption(t),console.log("A区图表初始化成功"),o.value&&o.value.targetMajorName&&o.value.majorCode){let u=o.value.targetMajorName,g=o.value.majorCode,m=o.value.firstLevelSubject||"";P.value=`A区  专业名称：${u}  专业代码：${g}  一级学科：${m}`}}catch(t){console.error("A区图表初始化失败:",t)}else{console.warn("A区图表容器尺寸为0，延迟初始化");return}}else console.warn("A区图表容器不存在");if(A.value){const n=A.value.getBoundingClientRect();if(console.log("B区图表容器尺寸:",n),console.log("B区容器样式:",window.getComputedStyle(A.value)),n.width>0&&n.height>0)try{M&&(console.log("销毁已存在的B区图表"),M.dispose()),console.log("开始初始化B区图表"),M=Ws(A.value);const t=F(!1);if(console.log("B区图表配置:",t),M.setOption(t),o.value&&o.value.targetMajorName&&o.value.majorCode){let u=o.value.targetMajorName,g=o.value.majorCode,m=o.value.firstLevelSubject||"";$.value=`B区  专业名称：${u}  专业代码：${g}  一级学科：${m}`}console.log("B区图表初始化成功")}catch(t){console.error("B区图表初始化失败:",t)}else{console.warn("B区图表容器尺寸为0，延迟初始化");return}}else console.warn("B区图表容器不存在")};return(n,t)=>{var g,m,f,B,W,I,O,V,E,q,H,U,G,J,K,Q,X,Y,Z,ss,ts,es,ls,os,ns,is,as,ds,cs,rs,vs,us,_s,ms,gs,fs,bs,ps,hs,ys,ks,xs,Ss,js,Cs,As,Bs;const u=st;return a(),i("div",at,[Zs((a(),i("div",dt,[t[0]||(t[0]=s("div",{class:"logo-wrapper"},[s("img",{class:"logo",src:tt,alt:"logo"})],-1)),t[1]||(t[1]=s("h1",{class:"title"},"研趣考研院校分析报告",-1)),s("div",ct,"姓名："+e((g=o.value)==null?void 0:g.name),1)])),[[u,R.value,void 0,{fullscreen:!0,lock:!0}]]),s("div",rt,[s("div",vt,[t[61]||(t[61]=s("div",{class:"info-section-title-bg"},[s("img",{src:et,mode:"heightFix",alt:"第一部分标题背景",class:"title-bg-img"})],-1)),s("div",ut,[s("div",null,[t[12]||(t[12]=s("div",{class:"step-num-tag"},[s("span",null,"01"),s("div",{class:"tag-text"},"个人基础信息")],-1)),s("div",_t,[s("div",mt,[t[2]||(t[2]=s("span",{class:"info-block-item-title"},"姓名:",-1)),s("span",gt,e((m=o.value)==null?void 0:m.name),1)]),s("div",ft,[t[3]||(t[3]=s("span",{class:"info-block-item-title"},"性别:",-1)),s("span",bt,e((f=o.value)==null?void 0:f.sexText),1)]),s("div",pt,[t[4]||(t[4]=s("span",{class:"info-block-item-title"},"本科院校:",-1)),s("span",ht,e((B=o.value)==null?void 0:B.undergraduateSchoolName),1)]),s("div",yt,[t[5]||(t[5]=s("span",{class:"info-block-item-title"},"本科专业:",-1)),s("span",kt,e((W=o.value)==null?void 0:W.undergraduateMajorName),1)]),s("div",xt,[t[6]||(t[6]=s("span",{class:"info-block-item-title"},"联系方式:",-1)),s("span",St,e((I=o.value)==null?void 0:I.phone),1)]),s("div",jt,[t[7]||(t[7]=s("span",{class:"info-block-item-title"},"考研届数:",-1)),s("span",Ct,e((O=o.value)==null?void 0:O.examYear),1)]),s("div",At,[t[8]||(t[8]=s("span",{class:"info-block-item-title"},"跨专业:",-1)),s("span",Bt,e((V=o.value)==null?void 0:V.isCrossMajorText),1)]),s("div",wt,[t[9]||(t[9]=s("span",{class:"info-block-item-title"},"培养方式:",-1)),s("span",Lt,e((E=o.value)==null?void 0:E.educationalStyleText),1)]),s("div",Mt,[t[10]||(t[10]=s("span",{class:"info-block-item-title"},"专业代码:",-1)),s("span",Nt,e((q=p.value)==null?void 0:q.major_code),1)]),s("div",Tt,[t[11]||(t[11]=s("span",{class:"info-block-item-title"},"目标专业:",-1)),s("span",zt,e((H=p.value)==null?void 0:H.target_major),1)])])]),s("div",null,[t[13]||(t[13]=s("div",{class:"step-num-tag"},[s("span",null,"02"),s("div",{class:"tag-text"},"本科成绩情况")],-1)),s("div",Dt,[(U=o.value)!=null&&U.undergraduateTranscript&&((G=o.value)==null?void 0:G.undergraduateTranscript.length)>0?(a(!0),i(y,{key:0},k(o.value.undergraduateTranscript,(l,h)=>(a(),i("div",{class:"info-block-item",key:h},[s("span",Rt,e(l.title),1),s("span",Pt,e(l.score),1)]))),128)):b("",!0)])]),s("div",null,[t[20]||(t[20]=s("div",{class:"step-num-tag"},[s("span",null,"03"),s("div",{class:"tag-text"},"英语基础")],-1)),s("div",$t,[s("div",Ft,[t[14]||(t[14]=s("span",{class:"info-block-item-title"},"高考英语成绩:",-1)),s("span",Wt,e((J=o.value)==null?void 0:J.englishScore),1)]),s("div",It,[t[15]||(t[15]=s("span",{class:"info-block-item-title"},"大学四级:",-1)),s("span",Ot,e((K=o.value)==null?void 0:K.cet4),1)]),s("div",Vt,[t[16]||(t[16]=s("span",{class:"info-block-item-title"},"大学六级:",-1)),s("span",Et,e((Q=o.value)==null?void 0:Q.cet6),1)]),s("div",qt,[t[17]||(t[17]=s("span",{class:"info-block-item-title"},"托福:",-1)),s("span",Ht,e((X=o.value)!=null&&X.tofelScore?(Y=o.value)==null?void 0:Y.tofelScore:""),1)]),s("div",Ut,[t[18]||(t[18]=s("span",{class:"info-block-item-title"},"雅思:",-1)),s("span",Gt,e((ss=(Z=o.value)==null?void 0:Z.englishScores)==null?void 0:ss.ielts),1)]),s("div",Jt,[t[19]||(t[19]=s("span",{class:"info-block-item-title"},"熟练语法:",-1)),s("span",Kt,e((es=(ts=o.value)==null?void 0:ts.englishScores)!=null&&es.grammarProficiency?"是":"否"),1)])])]),s("div",null,[t[26]||(t[26]=s("div",{class:"step-num-tag"},[s("span",null,"04"),s("div",{class:"tag-text"},"目标院校倾向")],-1)),s("div",Qt,[s("div",Xt,[t[21]||(t[21]=s("span",{class:"info-other-block-item-title"},"地区倾向:",-1)),s("span",Yt,e((ls=p.value)==null?void 0:ls.region_preference),1)]),s("div",Zt,[t[22]||(t[22]=s("span",{class:"info-other-block-item-title"},"省份选择:",-1)),s("span",se,e((os=p.value)==null?void 0:os.province_selection),1)])]),s("div",te,[s("div",ee,[t[23]||(t[23]=s("span",{class:"info-other-block-item-title"},"梦校:",-1)),s("span",le,e((ns=p.value)==null?void 0:ns.dream_school),1)]),s("div",oe,[t[24]||(t[24]=s("span",{class:"info-other-block-item-title"},"院校层次:",-1)),s("span",ne,e((is=p.value)==null?void 0:is.school_level),1)])]),s("div",ie,[s("div",ae,[t[25]||(t[25]=s("span",{class:"info-other-block-item-title"},"专业课制定参考书:",-1)),s("span",de,e((as=p.value)==null?void 0:as.reference_books),1)])])]),s("div",ce,[s("div",re,[s("div",null,[t[30]||(t[30]=s("div",{class:"step-num-tag"},[s("span",null,"06"),s("div",{class:"tag-text"},"考试成绩预估")],-1)),s("div",ve,[t[27]||(t[27]=D('<div class="table-header" data-v-106d1d35><div class="th-cell cell-color" data-v-106d1d35>政治</div><div class="th-cell cell-color" data-v-106d1d35>英语</div><div class="th-cell cell-color" data-v-106d1d35>业务课一</div><div class="th-cell cell-color" data-v-106d1d35>业务课二</div><div class="th-cell cell-color" data-v-106d1d35>总分</div></div>',1)),s("div",ue,[s("div",_e,e((ds=o.value)==null?void 0:ds.politics),1),s("div",me,e((cs=o.value)==null?void 0:cs.englishS),1),s("div",ge,e((rs=o.value)==null?void 0:rs.englishType),1),s("div",fe,e((vs=o.value)==null?void 0:vs.mathType),1),s("div",be,e((us=o.value)==null?void 0:us.totalScore),1)])]),s("div",pe,[s("div",he,[t[28]||(t[28]=s("span",{class:"demands-label-title"},"个性化需求:",-1)),s("span",null,e((_s=p.value)==null?void 0:_s.personal_needs),1)])]),s("div",ye,[s("div",ke,[t[29]||(t[29]=s("span",{class:"advice-label-title"},"薄弱模块:",-1)),s("span",null,e((ms=p.value)==null?void 0:ms.weak_modules),1)])])])])])]),t[62]||(t[62]=s("div",{class:"info-section-title-bg"},[s("img",{src:lt,alt:"第一部分标题背景",class:"title-bg-img"})],-1)),s("div",xe,[s("div",Se,[s("span",null,e(P.value),1)]),s("div",{ref_key:"echartsARef",ref:C,class:"echarts-box"},null,512),s("div",je,[s("span",null,e($.value),1)]),s("div",{ref_key:"echartsBRef",ref:A,class:"echarts-box"},null,512)]),t[63]||(t[63]=s("div",{class:"info-section-title-bg"},[s("img",{src:ot,alt:"第一部分标题背景",class:"title-bg-img"})],-1)),s("div",Ce,[s("div",Ae,[t[31]||(t[31]=D('<div class="school-table-header" data-v-106d1d35><div class="header-cell" data-v-106d1d35>序号</div><div class="header-cell" data-v-106d1d35>院校名称</div><div class="header-cell" data-v-106d1d35>所在地区</div><div class="header-cell" data-v-106d1d35>所在省市</div><div class="header-cell" data-v-106d1d35>学院</div><div class="header-cell" data-v-106d1d35>专业</div><div class="header-cell" data-v-106d1d35>专业代码</div><div class="header-cell" data-v-106d1d35>最低分</div><div class="header-cell" data-v-106d1d35>最低分分差</div></div>',1)),s("div",Be,[(a(!0),i(y,null,k(((gs=w.value)==null?void 0:gs.school_list)||[],(l,h)=>(a(),i("div",{class:"table-row",key:h},[s("div",we,e(l.id),1),s("div",Le,[s("div",null,e(l.school_name),1),s("div",Me,[l.tag_985?(a(),i("span",Ne,"985")):b("",!0),l.tag_211?(a(),i("span",Te,"211")):b("",!0),l.tag_double?(a(),i("span",ze,"双一流")):b("",!0)])]),s("div",De,e(l.region),1),s("div",Re,e(l.city),1),s("div",Pe,e(l.college),1),s("div",$e,e(l.major_name),1),s("div",Fe,e(l.major_code),1),s("div",We,e(l.min_score),1),s("div",Ie,e(l.score_diff),1)]))),128))])])]),t[64]||(t[64]=s("div",{class:"info-section-title-bg"},[s("img",{src:nt,alt:"第一部分标题背景",class:"title-bg-img"})],-1)),t[65]||(t[65]=s("div",{class:"step-num-tag school-step-num-tag"},[s("span",null,"01"),s("div",{class:"tag-text"},"院校推荐")],-1)),(a(!0),i(y,null,k(((fs=w.value)==null?void 0:fs.recommend_list)||[],(l,h)=>{var c,T,ws,Ls,Ms,Ns,Ts,zs,Ds,Rs,Ps,$s,Fs;return a(),i("div",{class:"step-content",key:h},[s("div",Oe,[s("div",Ve,[s("div",Ee,[s("img",{src:"https://"+((c=l==null?void 0:l.school_info)==null?void 0:c.logo)||"@/assets/images/school.png",alt:"学校logo"},null,8,qe)]),s("div",He,[s("div",Ue,[s("h2",null,e((l==null?void 0:l.school_name)||""),1),s("span",Ge,e((l==null?void 0:l.province_city)||""),1)]),s("div",Je,[(T=l==null?void 0:l.school_info)!=null&&T.is_dual_class?(a(),i("span",Ke,"双一流")):b("",!0),(ws=l==null?void 0:l.school_info)!=null&&ws.tag_985?(a(),i("span",Qe,"985")):b("",!0),(Ls=l==null?void 0:l.school_info)!=null&&Ls.tag_211?(a(),i("span",Xe,"211")):b("",!0)])])]),t[47]||(t[47]=s("h3",{class:"section-title"},"院校情况",-1)),s("div",Ye,[s("div",Ze,[t[32]||(t[32]=s("div",{class:"reexam-header"},[s("img",{src:r,alt:"标签图标"}),s("div",{class:"reexam-title"},"总成绩计算公式")],-1)),s("div",sl,[s("div",null,e((Ms=l==null?void 0:l.basic_info)==null?void 0:Ms.score_formula),1)])]),s("div",tl,[t[33]||(t[33]=s("div",{class:"reexam-header"},[s("img",{src:r,alt:"标签图标"}),s("div",{class:"reexam-title"},"学制")],-1)),s("div",el,[s("div",null,e((Ns=l==null?void 0:l.basic_info)==null?void 0:Ns.study_years),1)])]),s("div",ll,[t[34]||(t[34]=s("div",{class:"reexam-header"},[s("img",{src:r,alt:"标签图标"}),s("div",{class:"reexam-title"},"学费")],-1)),s("div",ol,[s("div",null,e((Ts=l==null?void 0:l.basic_info)==null?void 0:Ts.tuition_fee),1)])])]),t[48]||(t[48]=s("h3",{class:"section-title section-title-font"},"初试模块",-1)),s("div",nl,[s("div",il,[t[35]||(t[35]=s("div",{class:"reexam-header"},[s("img",{src:r,alt:"标签图标"}),s("div",{class:"reexam-title"},"初试考试科目")],-1)),s("div",al,[s("div",null,e((zs=l==null?void 0:l.basic_info)==null?void 0:zs.exam_range),1)])]),s("div",dl,[t[36]||(t[36]=s("div",{class:"reexam-header"},[s("img",{src:r,alt:"标签图标"}),s("div",{class:"reexam-title"},"考试专业课参考书")],-1)),s("div",cl,[s("div",null,e((Ds=l==null?void 0:l.basic_info)==null?void 0:Ds.reference_books),1)])])]),s("div",rl,[l!=null&&l.admission_data&&l.admission_data.length>0?(a(),i("div",vl," 招生情况（"+e((Rs=l.admission_data[0])==null?void 0:Rs.year)+"年） ",1)):b("",!0),s("table",ul,[t[37]||(t[37]=s("thead",null,[s("tr",null,[s("th",null,"招生计划"),s("th",null,"一志愿人数"),s("th",null,"总录取数"),s("th",null,"一志愿录取比"),s("th",null,"调剂人数"),s("th",null,"最高分"),s("th",null,"最低分"),s("th",null,"平均分")])],-1)),s("tbody",null,[(a(!0),i(y,null,k(l==null?void 0:l.admission_data,(d,j)=>(a(),i("tr",{key:j},[s("td",null,e(d.planCount),1),s("td",null,e(d.examCount),1),s("td",null,e(d.admitCount),1),s("td",null,e(d.ratio),1),s("td",null,e(d.studentCount),1),s("td",null,e(d.highestScore),1),s("td",null,e(d.lowestScore),1),s("td",null,e(d.averageScore),1)]))),128))])]),t[43]||(t[43]=s("div",{class:"admission-title"},"一志愿考试名单",-1)),s("table",_l,[t[38]||(t[38]=s("thead",null,[s("tr",null,[s("th",null,"编号"),s("th",null,"学生姓名"),s("th",null,"政治"),s("th",null,"英语"),s("th",null,"专业课一"),s("th",null,"专业课二"),s("th",null,"初试成绩"),s("th",null,"是否一志愿")])],-1)),s("tbody",null,[(a(!0),i(y,null,k(((Ps=l==null?void 0:l.current_year_retest_list)==null?void 0:Ps.list)||[],(d,j)=>(a(),i("tr",{key:j},[s("td",null,e(j+1),1),s("td",null,e(d.name),1),s("td",null,e(d.politics_score),1),s("td",null,e(d.english_score),1),s("td",null,e(d.major1_score),1),s("td",null,e(d.major2_score),1),s("td",null,e(d.initial_score),1),s("td",null,e(d.admission_status),1)]))),128))])]),t[44]||(t[44]=s("div",{class:"admission-title"},"复试模块",-1)),s("div",ml,[s("div",gl,[t[39]||(t[39]=s("div",{class:"reexam-header"},[s("img",{src:r,alt:"标签图标"}),s("div",{class:"reexam-title"},"复试内容")],-1)),s("div",fl,e(($s=l==null?void 0:l.basic_info)==null?void 0:$s.retest_content),1)])]),t[45]||(t[45]=s("div",{class:"admission-title"},"拟录取名单",-1)),s("table",bl,[t[40]||(t[40]=s("thead",null,[s("tr",null,[s("th",null,"编号"),s("th",null,"学生姓名"),s("th",null,"初试成绩"),s("th",null,"复试成绩"),s("th",null,"两项总成绩"),s("th",null,"一志愿学校")])],-1)),s("tbody",null,[(a(!0),i(y,null,k(((Fs=l==null?void 0:l.current_year_admission_list)==null?void 0:Fs.list)||[],(d,j)=>(a(),i("tr",{key:j},[s("td",null,e(j+1),1),s("td",null,e(d.name),1),s("td",null,e(d.initial_score),1),s("td",null,e(d.retest_score),1),s("td",null,e(d.total_score),1),s("td",null,e(d.first_choice_school),1)]))),128))])]),t[46]||(t[46]=s("div",{class:"admission-title"},"综合建议",-1)),s("div",pl,[s("div",hl,[t[41]||(t[41]=s("div",{class:"reexam-header"},[s("img",{src:r,alt:"标签图标"}),s("div",{class:"reexam-title"},"竞争难度分析")],-1)),s("div",yl,[s("div",null,e(l.difficulty_analysis),1)])]),s("div",kl,[t[42]||(t[42]=s("div",{class:"reexam-header"},[s("img",{src:r,alt:"标签图标"}),s("div",{class:"reexam-title"},"备考目标建议")],-1)),s("div",xl,[s("div",null,e(l.suggest),1)])])])])])])}),128)),t[66]||(t[66]=s("div",{class:"step-num-tag"},[s("span",null,"03"),s("div",{class:"tag-text"},"推荐综合性价比高的院校")],-1)),s("div",Sl,[s("div",jl,[t[49]||(t[49]=s("div",{class:"recommend-school-header"},[s("div",{class:"recommend-icon"},[s("img",{src:r,alt:"标签图标"})]),s("div",{class:"recommend-title"},"推荐原因")],-1)),s("div",Cl,e(((ps=(bs=w.value)==null?void 0:bs.high_recommend_list)==null?void 0:ps.reason)||"暂无推荐信息"),1)])]),t[67]||(t[67]=s("div",{class:"info-section-title-bg"},[s("img",{src:it,alt:"第一部分标题背景",class:"title-bg-img"})],-1)),t[68]||(t[68]=s("div",{class:"step-num-tag"},[s("span",null,"01"),s("div",{class:"tag-text"},"薄弱模块分析")],-1)),s("div",Al,[((hs=x.value)==null?void 0:hs.weakModuleAnalysis.length)>0?(a(!0),i(y,{key:0},k(x.value.weakModuleAnalysis,(l,h)=>(a(),i("div",{class:"recommend-school-card",key:h},[t[50]||(t[50]=s("div",{class:"recommend-school-header"},[s("div",{class:"recommend-icon"},[s("img",{src:r,alt:"标签图标"})]),s("div",{class:"recommend-title"},"科目")],-1)),s("div",Bl,e((l==null?void 0:l.subject)||"英语一"),1),t[51]||(t[51]=s("div",{class:"recommend-school-header"},[s("div",{class:"recommend-icon"},[s("img",{src:r,alt:"标签图标"})]),s("div",{class:"recommend-title"},"问题分析")],-1)),s("div",wl,e((l==null?void 0:l.problemAnalysis)||"考研英语难度与要求考研英语（一）难度较高，重点考查 阅读理解（40分）、写作（30分） 和 长难句分析能力，词汇量要求约5500词。USTC电子信息专业虽为工科，但英语单科线通常在 50分左右（需参考往年分数线），且总分要求较高，英语短板可能拖累整体成绩。你的薄弱节可能包括词汇量不足：高频词、学术词汇（如科技类文章常见词）不熟悉。长难句解析困难：影响阅读速度和答题准确率。写作模板化：缺乏灵活应对图表作文、议论文的能力。时管理差：阅读或翻译耗时过长，导致完形填空等题型失分。电子信息专业的特殊性复试可"),1),t[52]||(t[52]=s("div",{class:"recommend-school-header"},[s("div",{class:"recommend-icon"},[s("img",{src:r,alt:"标签图标"})]),s("div",{class:"recommend-title"},"解决方案")],-1)),s("div",Ll,e((l==null?void 0:l.problemAnalysis)||"暂无推荐信息"),1)]))),128)):b("",!0)]),s("div",Ml,[s("div",Nl,[t[54]||(t[54]=s("div",{class:"step-num-tag"},[s("span",null,"02"),s("div",{class:"tag-text"},"目标分数")],-1)),s("div",Tl,[t[53]||(t[53]=D('<div class="table-header" data-v-106d1d35><div class="th-cell cell-color" data-v-106d1d35>政治</div><div class="th-cell cell-color" data-v-106d1d35>英语</div><div class="th-cell cell-color" data-v-106d1d35>业务课一</div><div class="th-cell cell-color" data-v-106d1d35>业务课二</div><div class="th-cell cell-color" data-v-106d1d35>总分</div></div>',1)),s("div",zl,[s("div",Dl,e((ys=S.value)==null?void 0:ys.politics),1),s("div",Rl,e((ks=S.value)==null?void 0:ks.english),1),s("div",Pl,e((xs=S.value)==null?void 0:xs.business1),1),s("div",$l,e((Ss=S.value)==null?void 0:Ss.business2),1),s("div",Fl,e((js=S.value)==null?void 0:js.total),1)])])])]),s("div",Wl,[s("div",Il,[t[59]||(t[59]=s("div",{class:"step-num-tag"},[s("span",null,"03"),s("div",{class:"tag-text"},"初试各科学习规划")],-1)),((Cs=x.value)==null?void 0:Cs.studyPlanning.stages.length)>0?(a(!0),i(y,{key:0},k((As=x.value)==null?void 0:As.studyPlanning.stages,(l,h)=>(a(),i("div",{key:h},[s("div",Ol,e(l.title),1),s("div",Vl,[l.modules.length>0?(a(!0),i(y,{key:0},k(l.modules,(c,T)=>(a(),i("div",{key:T},[s("div",El,e(c==null?void 0:c.name),1),t[55]||(t[55]=s("div",{class:"recommend-school-header"},[s("div",{class:"recommend-icon"},[s("img",{src:r,alt:"标签图标"})]),s("div",{class:"stage_module-title"},"学习内容")],-1)),s("div",ql,e((c==null?void 0:c.studyContent)||"暂无"),1),t[56]||(t[56]=s("div",{class:"recommend-school-header"},[s("div",{class:"recommend-icon"},[s("img",{src:r,alt:"标签图标"})]),s("div",{class:"recommend-title"},"学习方法")],-1)),s("div",Hl,e((c==null?void 0:c.studyMethod)||"暂无"),1),t[57]||(t[57]=s("div",{class:"recommend-school-header"},[s("div",{class:"recommend-icon"},[s("img",{src:r,alt:"标签图标"})]),s("div",{class:"recommend-title"},"资料建议")],-1)),s("div",Ul,e((c==null?void 0:c.studyMaterials)||"暂无"),1),t[58]||(t[58]=s("div",{class:"recommend-school-header"},[s("div",{class:"recommend-icon"},[s("img",{src:r,alt:"标签图标"})]),s("div",{class:"recommend-title"},"要点提醒")],-1)),s("div",Gl,e((c==null?void 0:c.studyReminder)||"暂无"),1)]))),128)):b("",!0)])]))),128)):b("",!0)])]),s("div",Jl,[t[60]||(t[60]=s("div",{class:"report_suggest_title"},"综合建议",-1)),s("div",Kl,e(((Bs=x.value)==null?void 0:Bs.comprehensiveAdvice)||"暂无"),1)])])])])}}}),oo=Hs(Ql,[["__scopeId","data-v-106d1d35"]]);export{oo as default};
