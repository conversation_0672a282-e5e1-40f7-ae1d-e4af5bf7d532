<template>
  <div class="socket-container">
    <div class="connection-status">
      连接状态: <span :class="{ 'connected': isConnected, 'disconnected': !isConnected }">
        {{ isConnected ? '已连接' : '未连接' }}
      </span>
    </div>
    
    <div class="control-panel">
      <el-button type="primary" @click="connectSocket" :disabled="isConnected">连接</el-button>
      <el-button type="danger" @click="disconnectSocket" :disabled="!isConnected">断开连接</el-button>
    </div>
    
    <div class="message-panel">
      <div class="message-list">
        <div v-for="(msg, index) in messages" :key="index" class="message-item">
          <span class="message-time">{{ formatTime(msg.time) }}</span>
          <span class="message-content">{{ msg.content }}</span>
        </div>
      </div>
    
      <div class="message-list">
        <div v-for="(msgitem, index) in   allSchoolArrString" :key="index" class="message-item">
          <!-- <span class="message-time">{{ formatTime(msg.time) }}</span> -->
          <span class="message-content">{{   msgitem.str }}</span>
        </div>
      </div>
      
      <div class="message-input">
        <el-input v-model="messageInput" placeholder="请输入消息" @keyup.enter="sendMessage"></el-input>
        <el-button type="primary" @click="sendMessage" :disabled="!isConnected">发送</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { io } from 'socket.io-client';
import { getReportBaseString ,getReportReasonString} from  '@/api/report'
import axios from 'axios';
import { ref, onMounted, onUnmounted } from 'vue';
    // const schoolStr = ref("{\"content\":\"```json\\n{\\n  \\\"recommendList\\\": [\\n    {\\n      \\\"school\\\": \\\"中国科学技术大学\\\",\\n      \\\"major\\\": \\\"网络工程\\\",\\n      \\\"reason\\\": \\\"中国科学技术大学作为985高校，计算机科学与技术专业在全国排名靠前，尤其在网络工程方向有较强的研究实力。该校位于安徽省合肥市，地理位置优越，且近年来长三角地区AI技术发展迅速，提供了良好的就业和发展机会。根据历年录取分数线，您的预估分数在该校的录取范围内，建议作为冲刺目标。\\\"\\n    },\\n    {\\n      \\\"school\\\": \\\"南京大学\\\",\\n      \\\"major\\\": \\\"网络工程\\\",\\n      \\\"reason\\\": \\\"南京大学是985高校，计算机科学与技术专业实力雄厚，网络工程方向有较强的研究团队和资源。南京作为长三角核心城市，科技产业发展迅速，提供了丰富的实习和就业机会。根据历年录取分数线，您的预估分数在该校的录取范围内，建议作为稳妥选择。\\\"\\n    },\\n    {\\n      \\\"school\\\": \\\"中山大学\\\",\\n      \\\"major\\\": \\\"网络工程\\\",\\n      \\\"reason\\\": \\\"中山大学是985高校，计算机科学与技术专业在全国有较高的声誉，网络工程方向有较强的研究实力。广州作为一线城市，科技产业发展迅速，提供了丰富的实习和就业机会。根据历年录取分数线，您的预估分数在该校的录取范围内，建议作为稳妥选择。\\\"\\n    },\\n    {\\n      \\\"school\\\": \\\"浙江大学\\\",\\n      \\\"major\\\": \\\"网络工程\\\",\\n      \\\"reason\\\": \\\"浙江大学是985高校，计算机科学与技术专业在全国排名靠前，网络工程方向有较强的研究团队和资源。杭州作为长三角核心城市，科技产业发展迅速，提供了丰富的实习和就业机会。根据历年录取分数线，您的预估分数在该校的录取范围内，建议作为冲刺目标。\\\"\\n    },\\n    {\\n      \\\"school\\\": \\\"上海交通大学\\\",\\n      \\\"major\\\": \\\"网络工程\\\",\\n      \\\"reason\\\": \\\"上海交通大学是985高校，计算机科学与技术专业实力雄厚，网络工程方向有较强的研究团队和资源。上海作为一线城市，科技产业发展迅速，提供了丰富的实习和就业机会。根据历年录取分数线，您的预估分数在该校的录取范围内，建议作为冲刺目标。\\\"\\n    },\\n    {\\n      \\\"school\\\": \\\"华中科技大学\\\",\\n      \\\"major\\\": \\\"网络工程\\\",\\n      \\\"reason\\\": \\\"华中科技大学是985高校，计算机科学与技术专业在全国有较高的声誉，网络工程方向有较强的研究实力。武汉作为中部地区的重要城市，科技产业发展迅速，提供了丰富的实习和就业机会。根据历年录取分数线，您的预估分数在该校的录取范围内，建议作为稳妥选择。\\\"\\n    }\\n  ]\\n}\\n```\"}"
// )
    const socket = ref(null);
    const isConnected = ref(false);
    const contentString = ref('');
    const messages = ref([]);
    const messageInput = ref('');
    const sessionTmpId = ref('')
    const reandomRequest = ref('')
    const schoolStr = ref ('');
    const showString   =  ref('中国科学技术大学、中山大学');
    const schoolArr = ref([]);
    // 连接WebSocket
    const reqData = ref({
        "base": {"cls":"2026届","col":"安徽建筑大学","ele":"计算机科学与技术","is_kua":"是","tar_code":"080903","tar_get":"网络工程","is_all":"否"},
        "will": {"score1": "70","score2": "70","score3": "110","score4":"130"},
        "tar": {"tar_area": "A区","tar_school": "中国科学技术大学","school_type": "985"},
        "english": {"english1": "120","english2":{"et4": "425", "et6":"430"},"seng":{"seng4": "","seng8": ""},"isto": "","yuda": "","isys":""},
        "math":{"math2": "80","math3": "80","math": "80","math4": "80","math5": "80", "math6": "130"},
        "remark":"备注信息",
        'school':'',
      
    })
    const contentFun = async () => {
      // let data = {
      //   "base": {"cls":"2026届","col":"安徽建筑大学","ele":"计算机科学与技术","is_kua":"是","tar_code":"080903","tar_get":"网络工程","is_all":"否"},
      //   "will": {"score1": "70","score2": "70","score3": "110","score4":"130"},
      //   "tar": {"tar_area": "A区","tar_school": "中国科学技术大学","school_type": "985"},
      //   "english": {"english1": "120","english2":{"et4": "425", "et6":"430"},"seng":{"seng4": "","seng8": ""},"isto": "","yuda": "","isys":""},
      //   "math":{"math2": "80","math3": "80","math": "80","math4": "80","math5": "80", "math6": "130"},
      //   "remark":"备注信息"
      // }
      const result  =  await getReportBaseString(reqData.value); 
      console.log(result.data.string); 
      contentString.value = result.data.string;
    }
    const connectSocket = () => {
    console.log(result.value.token)
      try {
        // 创建Socket.IO连接，添加授权信息
        socket.value = io('wss://wss.lke.cloud.tencent.com', {
          path: '/v1/qbot/chat/conn/',
          transports: ['websocket'],
          query: {
            EIO: 4,
            transport: 'websocket'
          },
          auth: {
            token: result.value.token // 这里替换为实际的授权token
          }
        });
        
        // 连接成功事件
        socket.value.on('connect', () => {
          isConnected.value = true;
          addMessage('系统', '连接成功');
          messageInput.value =  '测试';
          // sendMessage();
          testReq();
          console.log('Socket连接成功，ID:', socket.value.id);
        });
        
        // 连接错误事件
        socket.value.on('connect_error', (error) => {
          console.error('连接错误:', error);
          addMessage('系统', `连接错误: ${error.message}`);
        });
        
        // 断开连接事件
        socket.value.on('disconnect', (reason) => {
          isConnected.value = false;
          addMessage('系统', `断开连接: ${reason}`);
          console.log('Socket断开连接:', reason);
        });
        
        // 接收消息事件
        socket.value.on('message', (data) => {
          addMessage('服务器', data);
        });
       
        socket.value.on('reply', (data) => {
          console.log('收到回复:', data);
          if (data && data.payload && data.payload  && !data.payload.is_from_self  && data.payload.content) {
           // addMessage('AI助手', data.payload.work_flow.current_node.Output);
            if(data.payload.request_id != '') {
              allSchoolArrString.value[data.payload.request_id].str += data.payload.content
              if(data.payload.is_final) {
                allSchoolArrString.value[data.payload.request_id].strObj = JSON.parse(allSchoolArrString.value[data.payload.request_id].str)
                //dealSchoolMsg();
              // "reason":"推荐理由",difficulty_analysis:"专业的难度分析","suggest":"备考目标建议"
                addMessage( '推荐理由',`${ allSchoolArrString.value[data.payload.request_id].strObj.reason}`)
                addMessage( '专业的难度分析',`${ allSchoolArrString.value[data.payload.request_id].strObj.difficulty_analysis}`)
                addMessage( '备考目标建议',`${ allSchoolArrString.value[data.payload.request_id].strObj.suggest}`)
                // addMessage('原因',`${item.reason}`)
              }
            } else  {
              showString.value  +=  data.payload.content
              addMessage('AI助手',  showString.value)
              if(data.payload.is_final) {
                  schoolArr.value = showString.value.split('、');
                  dealSchoolMsg();
              }
            }
            
          }
        });
        
      } catch (error) {
        console.error('创建Socket连接失败:', error);
        addMessage('系统', `创建连接失败: ${error.message}`);
      }
    };
    const testReq = () =>  {
      schoolArr.value = showString.value.split('、');
      // console.log(schoolArr.value);return;
      dealSchoolMsg();
    }
    // 出来学校的prompt
    const allSchoolArrString = ref({});
    const dealSchoolMsg  =  async () =>  {
      schoolArr.value.map(item=> {
        reqData.value.school = item;
        getReportReasonString(reqData.value).then(result => {
          // 处理返回结果
          console.log(result);
          let tmpRequest  = generateRequestFlag();
          allSchoolArrString.value[tmpRequest] = {
            "school_name" : item,
            "str":'',
            "strObj":'',
            "reqid":tmpRequest,
            'prompt':result.data.string
          }
          sendSchoolMessage(allSchoolArrString.value[tmpRequest])
          console.log(allSchoolArrString.value)
        }).catch(error => {
          console.error('获取报告原因失败:', error);
        });
      })
    } 
    // 请求大模型获取学校分析
    const sendSchoolMessage = (schoolObj) => {
      console.log(schoolObj)
     // if (!messageInput.value.trim() || !isConnected.value) return;
       let msg = {
           "session_id":sessionTmpId.value,
            "bot_app_key": "zNZkvmYnJtNJEcxEzrcBUAenZsbxkwYyRruQTyOKUCWwajXScCdWieSjPHwRWhsRtkLyestwexqCvKMlIwePSEMsnREIXdbEwPaeEapBkAFCVyiVMilsxqMSCbaiLhst",
            "content": schoolObj.prompt,
            "request_id":schoolObj.reqid,
            "incremental": true,
            "streaming_throttle": 10,
            "visitor_labels": [],
            // "custom_variables": {
            //     "base_string": "{\"cls\":\"2026届\",\"col\":\"安徽建筑大学\",\"ele\":\"计算机科学与技术\",\"is_kua\":\"是\",\"tar_code\":\"080903\",\"tar_get\":\"网络工程\",\"is_all\":\"1\"}",
            //     "will_info_string": "{\"score1\": \"70\",\"score2\": \"70\",\"score3\": \"110\",\"score4\":\"130\"}",
            //     "tar_info_string": "{\"tar_area\": \"A区\",\"tar_school\": \"中国科学技术大学\",\"school_type\": \"985\"}",
            //     "english_string": "{\"english1\": \"120\",\"english2\":{\"et4\": \"425\", \"et6\":\"430\"},\"seng\":{\"seng4\": \"\",\"seng8\": \"\"},\"isto\": \"\",\"yuda\": \"\"}",
            //     "math_string":"{\"math2\": \"80\",\"math3\": \"80\",\"math\": \"80\",\"math4\": \"80\",\"math5\": \"80\", \"math6\": \"130\"}",
            //     "remark":"备注信息"
            // }
        }
        console.log(msg)
      //  socket.value.emit('send', msg);
      
      socket.value.emit('send', {"payload":msg});
      // addMessage('我', '测试');
      // messageInput.value = '';
    };


    const recommendSchools = ref([]);
    const parseJsonData = () => {
      try {
        // 1. 获取 content 属性中的内容
        const parsedObj = JSON.parse(schoolStr.value);
        const content = parsedObj.content;
    
        
        // 2. 去除 ```json 和 ``` 标记
        const jsonString = content
          .replace(/```json\n|\n```/g, '')
          .trim();
        
        console.log('处理后的JSON字符串:', jsonString);
        
        // 3. 解析为JSON对象
        const jsonData = JSON.parse(jsonString);
        
        // 4. 提取recommendList数组
        if (jsonData && jsonData.recommendList && Array.isArray(jsonData.recommendList)) {
          recommendSchools.value = jsonData.recommendList;
          console.log('解析出的推荐列表:', recommendSchools.value);
          
          // 5. 可以将解析出的数据添加到消息列表中展示
          recommendSchools.value.forEach((item, index) => {
            addMessage('推荐学校', `${index + 1}. ${item.school}`);
            addMessage( '专业',`${item.major}`)
            addMessage('原因',`${item.reason}`)
          });
        } else {
          console.warn('未找到推荐列表或格式不正确');
        }
      } catch (error) {
        console.error('JSON解析错误:', error);
        recommendSchools.value = []; // 失败时设为空数组
      }
    }

    // 断开WebSocket连接
    const disconnectSocket = () => {
      if (socket.value && socket.value.connected) {
        socket.value.disconnect();
      }
    };
    
    // 发送消息
    // "session_id": "a29bae68-cb1c-489d-8097-6be78f136ac9",
                      // bcab682d-23c5-a7d9-a6a0-24f05f8c00c9
    const sendMessage = () => {
      if (!messageInput.value.trim() || !isConnected.value) return;
       let msg = {
           "session_id":sessionTmpId.value,
            "bot_app_key": "zNZkvmYnJtNJEcxEzrcBUAenZsbxkwYyRruQTyOKUCWwajXScCdWieSjPHwRWhsRtkLyestwexqCvKMlIwePSEMsnREIXdbEwPaeEapBkAFCVyiVMilsxqMSCbaiLhst",
            "content": contentString.value,
            "incremental": true,
            "streaming_throttle": 10,
            "visitor_labels": [],
            // "custom_variables": {
            //     "base_string": "{\"cls\":\"2026届\",\"col\":\"安徽建筑大学\",\"ele\":\"计算机科学与技术\",\"is_kua\":\"是\",\"tar_code\":\"080903\",\"tar_get\":\"网络工程\",\"is_all\":\"1\"}",
            //     "will_info_string": "{\"score1\": \"70\",\"score2\": \"70\",\"score3\": \"110\",\"score4\":\"130\"}",
            //     "tar_info_string": "{\"tar_area\": \"A区\",\"tar_school\": \"中国科学技术大学\",\"school_type\": \"985\"}",
            //     "english_string": "{\"english1\": \"120\",\"english2\":{\"et4\": \"425\", \"et6\":\"430\"},\"seng\":{\"seng4\": \"\",\"seng8\": \"\"},\"isto\": \"\",\"yuda\": \"\"}",
            //     "math_string":"{\"math2\": \"80\",\"math3\": \"80\",\"math\": \"80\",\"math4\": \"80\",\"math5\": \"80\", \"math6\": \"130\"}",
            //     "remark":"备注信息"
            // }
        }
      //  socket.value.emit('send', msg);
      
      socket.value.emit('send', {"payload":msg});
      addMessage('我', '测试');
      messageInput.value = '';
    };
    
    // 添加消息到列表
    const addMessage = (sender, content) => {
      messages.value.push({
        sender,
        content: `${sender}: ${content}`,
        time: new Date()
      });
    };
    // 生成uuid
    const generateId = () => {
      const s4 = () => Math.floor((1 + Math.random()) * 0x10000)
        .toString(16)
        .substring(1);
      return `${s4()}${s4()}-${s4()}-${s4()}-${s4()}-${s4()}${s4()}${s4()}`;
    }
    //生成requestid
    const generateRequestFlag = () => {
      const timePart = Date.now().toString(36);
      const randomPart = Math.random().toString(36).substr(2, 6);
      return `SESS_${timePart}_${randomPart}`.toUpperCase();
    }

    onMounted(() => {
        sessionTmpId.value = generateId();
        reandomRequest.value = generateRequestFlag()
        tokenGet();
        contentFun();
        // testReq();
    })
    // 格式化时间
    const formatTime = (date) => {
      return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`;
    };
    let result = ref({})
    const tokenGet = () => {
        return new Promise(async (resolve, reject) => {
        let demoToken = '';
       
        const res = await axios.get('https://watang.yanqu.cn/addons/fzdc/text/text');
        console.log(res)
        if (res && res.data.data && res.data.data.Response && res.data.data.Response.Token) {
            demoToken = res.data.data.Response.Token;
        } 
        
        result.value = {
            type: 5,
            token: demoToken,
            access: 'ws'
        };
        console.log('【init msg----------demoToken---->】', result.value);
        resolve(result.value);
    });
    }
   

    
    // 组件卸载时断开连接
    onUnmounted(() => {
      disconnectSocket();
    });

</script>

<style scoped>
.socket-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.connection-status {
  margin-bottom: 20px;
  font-size: 16px;
}

.connected {
  color: green;
  font-weight: bold;
}

.disconnected {
  color: red;
  font-weight: bold;
}

.control-panel {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
}

.message-panel {
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.message-list {
  height: 400px;
  overflow-y: auto;
  padding: 10px;
  background-color: #f9f9f9;
}

.message-item {
  margin-bottom: 10px;
  padding: 8px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.message-time {
  color: #999;
  font-size: 12px;
  margin-right: 10px;
}

.message-input {
  display: flex;
  padding: 10px;
  background-color: white;
  border-top: 1px solid #ddd;
}

.message-input .el-input {
  margin-right: 10px;
}
</style>