<?php
namespace app\model;

use think\Model;

class NationalLine extends Model
{
    protected $table = 'ba_national_line';
    protected $pk = 'id';
    protected $autoWriteTimestamp = false;

    /**
     * 根据专业代码获取对应一级学科的国家线数据
     *
     * @param string $majorCode 专业代码
     * @param array $years 年份数组
     * @return array 国家线数据
     */
    public static function getNationalLineBySubjectCode($majorCode, $years = [])
    {
        // 如果未提供年份，获取最近三年
        if (empty($years)) {
            $currentYear = date('Y');
            $years = [$currentYear - 2, $currentYear - 1, $currentYear];
        }

        // 检查专业代码是否有效
        if (strlen($majorCode) < 2) {
            return [];
        }

        // 从专业代码中提取学科代码（前两位）
        $subjectCode = substr($majorCode, 0, 2);

        // 验证学科代码是否有效
        $validSubjectCodes = [
            '01', // 哲学
            '02', // 经济学
            '03', // 法学
            '04', // 教育学
            '05', // 文学
            '06', // 历史学
            '07', // 理学
            '08', // 工学
            '09', // 农学
            '10', // 医学
            '11', // 军事学
            '12', // 管理学
            '13', // 艺术学
        ];

        // 检查提取的学科代码是否有效
        if (!in_array($subjectCode, $validSubjectCodes) || empty($subjectCode)) {
            return [];
        }

        // 查询国家线数据
        $nationalLineData = self::where('subject_code', $subjectCode)
            ->whereIn('exam_year', $years)
            ->order('exam_year', 'asc')
            ->select()
            ->toArray();

        if (empty($nationalLineData)) {
            return [];
        }

        // 处理返回数据
        $result = [
            'subject_code' => $subjectCode,
            'subject_name' => $nationalLineData[0]['subject_name'],
            'years' => [],
            'a_total' => [],
            'a_single_100' => [],
            'a_single_over100' => [],
            'b_total' => [],
            'b_single_100' => [],
            'b_single_over100' => []
        ];

        foreach ($nationalLineData as $item) {
            $result['years'][] = $item['exam_year'];
            $result['a_total'][] = $item['a_total'];
            $result['a_single_100'][] = $item['a_single_100'];
            $result['a_single_over100'][] = $item['a_single_over100'];
            $result['b_total'][] = $item['b_total'];
            $result['b_single_100'][] = $item['b_single_100'];
            $result['b_single_over100'][] = $item['b_single_over100'];
        }

        return $result;
    }
}
