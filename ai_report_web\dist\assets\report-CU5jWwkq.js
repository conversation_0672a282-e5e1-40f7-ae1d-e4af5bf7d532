import{a8 as r}from"./index-Cu_gl4pu.js";function _(e,u){return new Promise((i,d)=>{const l=localStorage.getItem("token");let s="";s=`/api/remote/stream_ai_recommendation?report_id=${e.report_id}&token=${l}`;const a=new EventSource(s),n=/([A-K]\.)/;let c=[];a.onmessage=t=>{try{const o=t.data.replace(`
`,"");if(n.test(o)){c=o.split(n),c.forEach(p=>{p.trim()!=""&&(console.log("value:",p),u({type:t.type,data:p}))});return}console.log("event",t),u({type:t.type,data:o})}catch(o){console.error("解析SSE数据失败:",o)}},a.addEventListener("start",t=>{console.log("SSE流开始:",t.data),u({type:"start",data:t.data})}),a.addEventListener("end",t=>{u({type:"end",data:t.data}),a.close(),i()}),a.addEventListener("error",t=>{console.error("SSE错误事件:",t);try{const o=JSON.parse(t.data);u({type:"error",error:o.error||"未知错误"}),a.close(),d(new Error(o.error||"未知错误"))}catch{u({type:"error",error:"解析错误数据失败"}),a.close(),d(new Error("解析错误数据失败"))}}),a.onerror=t=>{console.error("SSE连接错误:",t),a.close(),d(t)}})}function g(e){return r({url:"/report/detail",method:"get",params:{report_id:e}})}function f(e){return r({url:"/report/basic-info",method:"get",params:{report_id:e}})}function h(e){return r({url:"/remote/get_msg_str",method:"post",data:e})}function S(e){return r({url:"/remote/school_and_major",method:"post",data:e})}function y(){return r({url:"/get_years",method:"get"})}function v(e){return r({url:"/report",method:"get",params:e})}function E(e){return r({url:"/save_report",method:"post",data:e})}function R(){return r({url:"/report_count",method:"get"})}function w(e){return r({url:"/remote/get_school_detail_new",method:"post",data:e})}function D(e){return r({url:"/api/report/detail",method:"post",data:e})}const b=e=>r({url:"/remote/save_report_data",method:"post",data:e});function k(e){return r({url:"/report/pdf-url",method:"put",data:e})}function L(e){return r({url:"/get_user_report",method:"get",params:{idcode:e}})}function A(e){return r({url:"/remote/get_study_plan",method:"post",data:e})}function P(e){return r({url:"/remote/get_study_plan_from_database",method:"post",data:e})}function $(e){return r({url:"/remote/get_study_plan_from_database_edit",method:"post",data:e})}function x(e){return r({url:"/remote/update_weak_module_analysis",method:"post",data:e})}function C(e){return r({url:"/remote/update_study_module",method:"post",data:e})}function F(e){return r({url:"/remote/update_comprehensive_advice",method:"post",data:e})}function B(e){return r({url:"/remote/save_target_scores",method:"post",data:e})}function N(e){return r({url:"/remote/get_target_scores",method:"get",params:{report_id:e}})}function T(e){return r({url:"/generate_code",method:"post",data:e})}function I(e){return r({url:"/remote/get_target_scores",method:"get",params:{report_id:e}})}function W(e){return r({url:"/remote/save_report_field_edit",method:"post",data:e})}function q(e,u,i,d,l,s){let a="";a=`/api/remote/regenerate_field?report_id=${e}&school_id=${u}&field_name=${i}`;const n=new EventSource(a);let c="";return n.onmessage=t=>{try{const o=t.data;console.log("event.data:",t.data),o&&o!=="重新生成完成"&&(c+=o,d(c))}catch(o){console.error("解析SSE数据失败:",o),s&&s(o)}},n.addEventListener("complete",t=>{console.log("重新生成完成:",t.data),n.close(),l&&l()}),n.addEventListener("error",t=>{console.error("SSE连接错误:",t),n.close(),s&&s(t)}),n.onerror=t=>{console.error("EventSource连接失败:",t),n.close(),s&&s(t)},n}export{I as a,q as b,w as c,W as d,b as e,P as f,$ as g,A as h,C as i,F as j,B as k,k as l,y as m,v as n,R as o,T as p,h as q,L as r,_ as s,S as t,x as u,g as v,E as w,f as x,N as y,D as z};
