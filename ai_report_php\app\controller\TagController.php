<?php
namespace app\controller;

use app\model\Tag;
use support\Request;

class TagController
{
    /**
     * 不需要登录的方法
     * @var array|string
     */
    protected $noNeedLogin = [];
    
    /**
     * 获取所有标签
     * @param Request $request
     * @return \support\Response
     */
    public function getAll(Request $request)
    {
        // 获取所有层级的标签
        $firstLevel = Tag::getFirstLevelTags();
        $secondLevel = Tag::getSecondLevelTags();
        $thirdLevel = Tag::getThirdLevelTags();
        
        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => [
                'first' => $firstLevel,
                'second' => $secondLevel,
                'third' => $thirdLevel
            ]
        ]);
    }
    
    /**
     * 添加标签
     * @param Request $request
     * @return \support\Response
     */
    public function add(Request $request)
    {
        $data = $request->post();
        
        // 验证数据
        if (empty($data['name'])) {
            return json([
                'code' => 400,
                'msg' => '标签名称不能为空',
                'data' => []
            ]);
        }
        
        if (!isset($data['level']) || !in_array($data['level'], [1, 2, 3])) {
            return json([
                'code' => 400,
                'msg' => '标签层级不正确',
                'data' => []
            ]);
        }
        
        // 如果是二级或三级标签，需要验证父级标签
        if ($data['level'] > 1) {
            if (empty($data['parent_id'])) {
                return json([
                    'code' => 400,
                    'msg' => '请选择父级标签',
                    'data' => []
                ]);
            }
            
            // 验证父级标签是否存在
            $parentLevel = $data['level'] - 1;
            $parentTag = Tag::where('id', $data['parent_id'])
                ->where('level', $parentLevel)
                ->where('is_delete', 0)
                ->find();
                
            if (!$parentTag) {
                return json([
                    'code' => 400,
                    'msg' => '父级标签不存在',
                    'data' => []
                ]);
            }
        } else {
            // 一级标签的父级ID为0
            $data['parent_id'] = 0;
        }
        
        // 创建标签
        $tag = new Tag;
        $tag->name = $data['name'];
        $tag->level = $data['level'];
        $tag->parent_id = $data['parent_id'];
        $tag->color = $data['level'] == 1 ? ($data['color'] ?? '#16b788') : '';
        $tag->sort = $data['sort'] ?? 0;
        $tag->is_delete = 0;
        
        if ($tag->save()) {
            return json([
                'code' => 0,
                'msg' => '添加成功',
                'data' => $tag
            ]);
        } else {
            return json([
                'code' => 500,
                'msg' => '添加失败',
                'data' => []
            ]);
        }
    }
    
    /**
     * 编辑标签
     * @param Request $request
     * @return \support\Response
     */
    public function edit(Request $request)
    {
        $data = $request->post();
        
        // 验证数据
        if (empty($data['id'])) {
            return json([
                'code' => 400,
                'msg' => '标签ID不能为空',
                'data' => []
            ]);
        }
        
        if (empty($data['name'])) {
            return json([
                'code' => 400,
                'msg' => '标签名称不能为空',
                'data' => []
            ]);
        }
        
        // 查找标签
        $tag = Tag::where('id', $data['id'])
            ->where('is_delete', 0)
            ->find();
            
        if (!$tag) {
            return json([
                'code' => 400,
                'msg' => '标签不存在',
                'data' => []
            ]);
        }
        
        // 更新标签
        $tag->name = $data['name'];
        if ($tag->level == 1 && isset($data['color'])) {
            $tag->color = $data['color'];
        }
        if (isset($data['sort'])) {
            $tag->sort = $data['sort'];
        }
        
        if ($tag->save()) {
            return json([
                'code' => 0,
                'msg' => '更新成功',
                'data' => $tag
            ]);
        } else {
            return json([
                'code' => 500,
                'msg' => '更新失败',
                'data' => []
            ]);
        }
    }
    
    /**
     * 删除标签
     * @param Request $request
     * @return \support\Response
     */
    public function delete(Request $request)
    {
        $id = $request->get('id');
        
        if (empty($id)) {
            return json([
                'code' => 400,
                'msg' => '标签ID不能为空',
                'data' => []
            ]);
        }
        
        // 查找标签
        $tag = Tag::where('id', $id)
            ->where('is_delete', 0)
            ->find();
            
        if (!$tag) {
            return json([
                'code' => 400,
                'msg' => '标签不存在',
                'data' => []
            ]);
        }
        
        // 如果是一级或二级标签，需要检查是否有子标签
        if ($tag->level < 3) {
            $childCount = Tag::where('parent_id', $id)
                ->where('level', $tag->level + 1)
                ->where('is_delete', 0)
                ->count();
                
            if ($childCount > 0) {
                return json([
                    'code' => 400,
                    'msg' => '该标签下有子标签，请先删除子标签',
                    'data' => []
                ]);
            }
        }
        
        // 软删除标签
        if ($tag->softDelete()) {
            return json([
                'code' => 0,
                'msg' => '删除成功',
                'data' => []
            ]);
        } else {
            return json([
                'code' => 500,
                'msg' => '删除失败',
                'data' => []
            ]);
        }
    }
}
