<?php
namespace app\model;

use think\Model;
use support\Log;
class SchoolInfo extends Model
{
    protected $table = 'ba_school_info';
    protected $pk = 'id';
    protected $autoWriteTimestamp = false;

    /**
     * 根据院校名称批量查询院校ID
     *
     * @param array $schoolNames 院校名称数组
     * @param string $marjor_code 专业代码
     * @return array 院校ID数组
     */
    public static function getIdsByNames($schoolNames, $marjor_code)
    {
        try {
            if (empty($schoolNames) || !is_array($schoolNames)) {
                return [];
            }
            
            // 查询院校ID
            $schoolIds = self::whereIn('school_name', $schoolNames)
                ->where('major_code', $marjor_code)
                ->column('id');
            // 返回去重后的院校ID数组
            return array_values(array_unique($schoolIds));
            
        } catch (\Exception $e) {
            Log::error('SchoolInfo模型查询院校ID异常: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 根据院校名称查询院校信息（包含ID和名称的映射）
     *
     * @param array $schoolNames 院校名称数组
     * @return array 以院校名称为键，院校ID为值的关联数组
     */
    public static function getNameIdMapping($schoolNames)
    {
        try {
            if (empty($schoolNames) || !is_array($schoolNames)) {
                return [];
            }
            
            // 查询院校信息，返回名称和ID的映射
            $schoolMapping = self::whereIn('school_name', $schoolNames)
                ->column('id', 'school_name');
            
            return $schoolMapping ?: [];
            
        } catch (\Exception $e) {
            Log::error('SchoolInfo模型查询院校名称ID映射异常: ' . $e->getMessage());
            return [];
        }
    }
}
