<?php
/**
 * Here is your custom functions.
 */

/**
 * 发送GET请求
 *
 * @param string $url 请求URL
 * @param array $params 请求参数
 * @param array $headers 请求头
 * @param int $timeout 超时时间（秒）
 * @return string|false 返回响应内容，失败返回false
 */
function http_get($url, $params = [], $headers = [], $timeout = 10)
{
    
    // 如果有参数，将参数添加到URL
    if (!empty($params)) {
        $url .= (strpos($url, '?') === false ? '?' : '&') . http_build_query($params);
    }

    // 初始化cURL
    $ch = curl_init();

    // 设置cURL选项
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    // 设置请求头
    if (!empty($headers)) {
        $headerArray = [];
        foreach ($headers as $key => $value) {
            $headerArray[] = $key . ': ' . $value;
        }
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headerArray);
    }

    // 执行请求
    $response = curl_exec($ch);
    // 检查是否有错误
    if (curl_errno($ch)) {
        $error = curl_error($ch);
        curl_close($ch);
        // 可以在这里记录错误信息
        if (function_exists('Log::error')) {
            \support\Log::error('HTTP GET请求错误: ' . $error . ', URL: ' . $url);
        }
        return false;
    }

    // 关闭cURL
    curl_close($ch);

    return $response;
}

/**
 * 发送POST请求
 *
 * @param string $url 请求URL
 * @param array|string $data 请求数据
 * @param array $headers 请求头
 * @param int $timeout 超时时间（秒）
 * @return string|false 返回响应内容，失败返回false
 */
function http_post($url, $data = [], $headers = [], $timeout = 10)
{
    // 初始化cURL
    $ch = curl_init();

    // 设置cURL选项
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    // 设置POST数据
    if (is_array($data)) {
        // 如果是数组，转换为查询字符串
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    } else {
        // 如果是字符串（如JSON），直接使用
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    }

    // 设置请求头
    if (!empty($headers)) {
        $headerArray = [];
        foreach ($headers as $key => $value) {
            $headerArray[] = $key . ': ' . $value;
        }
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headerArray);
    }

    // 执行请求
    $response = curl_exec($ch);

    // 检查是否有错误
    if (curl_errno($ch)) {
        $error = curl_error($ch);
        curl_close($ch);
        // 可以在这里记录错误信息
        if (function_exists('Log::error')) {
            \support\Log::error('HTTP POST请求错误: ' . $error . ', URL: ' . $url);
        }
        return false;
    }

    // 关闭cURL
    curl_close($ch);

    return $response;
}

/**
 * 发送JSON格式的POST请求
 *
 * @param string $url 请求URL
 * @param array $data 请求数据（将被转换为JSON）
 * @param array $headers 请求头
 * @param int $timeout 超时时间（秒）
 * @return string|false 返回响应内容，失败返回false
 */
function http_post_json($url, $data = [], $headers = [], $timeout = 10)
{
    // 添加JSON相关的请求头
    $headers = array_merge([
        'Content-Type' => 'application/json',
        'Accept' => 'application/json'
    ], $headers);

    // 将数据转换为JSON
    $jsonData = json_encode($data);

    // 调用POST请求函数
    return http_post($url, $jsonData, $headers, $timeout);
}

/**
 * 获取HTTP状态码
 *
 * @param string $url 请求URL
 * @param int $timeout 超时时间（秒）
 * @return int|false 返回HTTP状态码，失败返回false
 */
function http_status_code($url, $timeout = 10)
{
    // 初始化cURL
    $ch = curl_init();

    // 设置cURL选项
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_NOBODY, true); // 不获取响应体
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    // 执行请求
    curl_exec($ch);

    // 获取HTTP状态码
    $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    // 检查是否有错误
    if (curl_errno($ch)) {
        $error = curl_error($ch);
        curl_close($ch);
        // 可以在这里记录错误信息
        if (function_exists('Log::error')) {
            \support\Log::error('HTTP状态码请求错误: ' . $error . ', URL: ' . $url);
        }
        return false;
    }

    // 关闭cURL
    curl_close($ch);

    return $statusCode;
}

/**
 * 清理JSON字符串，移除markdown代码块标记并修复格式问题
 * @param string $rawString 原始字符串
 * @return string 清理后的JSON字符串
 */
function cleanJsonString($rawString) {
    // 记录调试信息
    if (function_exists('\support\Log::info')) {
        \support\Log::info('cleanJsonString - 输入长度: ' . strlen($rawString));
        \support\Log::info('cleanJsonString - 输入前200字符: ' . substr($rawString, 0, 200));
    }

    // 移除markdown代码块标记
    $cleaned = preg_replace('/^```json\s*/', '', $rawString);
    $cleaned = preg_replace('/\s*```$/', '', $cleaned);
    $cleaned = trim($cleaned);

    // 尝试修复常见的JSON格式问题
    try {
        // 先尝试直接解析
        json_decode($cleaned, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            if (function_exists('\support\Log::info')) {
                \support\Log::info('cleanJsonString - 原始JSON有效，直接返回');
            }
            return $cleaned; // 如果已经是有效JSON，直接返回
        }

        if (function_exists('\support\Log::info')) {
            \support\Log::info('cleanJsonString - 原始JSON无效，开始修复。错误: ' . json_last_error_msg());
        }

        // 如果解析失败，尝试修复常见问题

        // 1. 修复字符串中的未转义换行符
        // 使用更简单的方法：直接替换所有实际的换行符
        $cleaned = str_replace(["\r\n", "\n", "\r"], ["\\n", "\\n", "\\n"], $cleaned);
        $cleaned = str_replace("\t", "\\t", $cleaned);

        // 2. 修复可能的尾随逗号问题
        $cleaned = preg_replace('/,(\s*[}\]])/', '$1', $cleaned);

        // 3. 确保JSON结构完整
        $cleaned = trim($cleaned);
        if (!empty($cleaned) && !preg_match('/^[\{\[]/', $cleaned)) {
            // 如果不是以 { 或 [ 开头，可能缺少开始标记
            if (strpos($cleaned, '"weakModuleAnalysis"') !== false || strpos($cleaned, '"studyPlanning"') !== false) {
                $cleaned = '{' . $cleaned;
            }
        }
        if (!empty($cleaned) && !preg_match('/[\}\]]$/', $cleaned)) {
            // 如果不是以 } 或 ] 结尾，可能缺少结束标记
            if (strpos($cleaned, '"weakModuleAnalysis"') !== false || strpos($cleaned, '"studyPlanning"') !== false) {
                $cleaned = $cleaned . '}';
            }
        }

        // 4. 最终验证
        json_decode($cleaned, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            if (function_exists('\support\Log::info')) {
                \support\Log::info('cleanJsonString - 修复成功');
            }
        } else {
            if (function_exists('\support\Log::error')) {
                \support\Log::error('cleanJsonString - 修复后仍然无效: ' . json_last_error_msg());
                \support\Log::error('cleanJsonString - 修复后内容前500字符: ' . substr($cleaned, 0, 500));
            }
        }

        return $cleaned;

    } catch (Exception $exception) {
        // 如果修复过程中出现异常，返回原始清理后的字符串
        if (function_exists('\support\Log::error')) {
            \support\Log::error('JSON清理过程异常: ' . $exception->getMessage());
        }
        return $cleaned;
    }
}

/**
 * 解析JSON字符串为PHP数组（基础版本）
 * @param string $jsonString JSON字符串
 * @return array 解析后的数组
 */
function parseJsonToArray($jsonString) {
    try {
        $cleanedString = cleanJsonString($jsonString);
        $data = json_decode($cleanedString, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log('JSON解析失败: ' . json_last_error_msg() . ', 原始字符串: ' . $jsonString);
            // 返回默认结构而不是抛出异常
            return [
                'recommend_list' => [],
                'high_recommend_list' => []
            ];
        }
        
        return $data;
    } catch (Exception $e) {
        error_log('JSON解析错误: ' . $e->getMessage());
        // 返回默认结构而不是抛出异常
        return [
            'recommend_list' => [],
            'high_recommend_list' => []
        ];
    }
}

/**
 * 发送流式HTTP请求（支持SSE）
 *
 * @param string $url 请求URL
 * @param array $data 请求数据（将被转换为JSON）
 * @param array $headers 请求头
 * @param callable $callback 回调函数，用于处理每个数据块
 * @param int $timeout 超时时间（秒）
 * @return void
 * @throws Exception
 */
function http_stream_request($url, $data = [], $headers = [], $callback = null, $timeout = 600)
{
    try {
        // 添加默认请求头
        $defaultHeaders = [
            'Content-Type' => 'application/json',
            'Accept' => 'text/event-stream'
        ];
        $headers = array_merge($defaultHeaders, $headers);
        
        // 记录请求信息
        if (function_exists('\support\Log::info')) {
            \support\Log::info('流式HTTP请求URL: ' . $url);
            \support\Log::info('流式HTTP请求数据: ' . json_encode($data, JSON_UNESCAPED_UNICODE));
        }
        
        // 初始化cURL
        $ch = curl_init($url);
        
        // 设置cURL选项
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, false); // 不返回响应
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30); // 连接超时时间（秒）
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout); // 总超时时间
        
        // 设置请求头
        $headerArray = [];
        foreach ($headers as $key => $value) {
            $headerArray[] = $key . ': ' . $value;
        }
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headerArray);
        
        // 设置更长的超时时间，避免超时问题
        if (function_exists('ini_set')) {
            ini_set('max_execution_time', $timeout);
        }
        
        // 设置写入回调函数
        curl_setopt($ch, CURLOPT_WRITEFUNCTION, function($_, $data) use ($callback) {
            // 如果没有提供回调函数，直接返回数据长度
            if (!is_callable($callback)) {
                return strlen($data);
            }
            
            // 处理SSE数据
            $lines = explode("\n", $data);
            foreach ($lines as $line) {
                $line = trim($line);
                if (empty($line)) continue;
                
                if (strpos($line, 'data: ') === 0) {
                    $jsonData = substr($line, 6); // 去掉 "data: " 前缀
                    
                    if ($jsonData === '[DONE]') {
                        // 流结束
                        if (function_exists('\support\Log::info')) {
                            \support\Log::info('流式HTTP响应结束');
                        }
                        $callback('done');
                        continue;
                    }
                    
                    try {
                        $decoded = json_decode($jsonData, true);
                        if ($decoded !== null) {
                            // 调用回调函数处理解析后的数据
                            $callback($decoded);
                        } else {
                            // 如果不是JSON格式，直接传递原始数据
                            $callback($jsonData);
                        }
                    } catch (Exception $e) {
                        if (function_exists('\support\Log::error')) {
                            \support\Log::error('解析流式响应失败: ' . $e->getMessage());
                        }
                        // 解析失败时，传递原始数据
                        $callback($jsonData);
                    }
                } else {
                    // 非SSE格式的数据，直接传递
                    $callback($line);
                }
            }
            return strlen($data); // 必须返回数据长度
        });
        
        // 执行请求
        curl_exec($ch);
        
        // 检查错误
        if (curl_errno($ch)) {
            $error = curl_error($ch);
            curl_close($ch);
            if (function_exists('\support\Log::error')) {
                \support\Log::error('流式HTTP请求失败: ' . $error . ', URL: ' . $url);
            }
            throw new Exception('流式HTTP请求失败: ' . $error);
        }
        
        // 关闭cURL
        curl_close($ch);
        
    } catch (Exception $e) {
        if (function_exists('\support\Log::error')) {
            \support\Log::error('流式HTTP请求异常: ' . $e->getMessage());
        }
        throw $e;
    }
}

/**
 * 发送流式POST JSON请求（简化版本）
 *
 * @param string $url 请求URL
 * @param array $data 请求数据
 * @param array $headers 额外的请求头
 * @param callable $callback 回调函数
 * @param int $timeout 超时时间（秒）
 * @return void
 * @throws Exception
 */
function http_stream_post_json($url, $data = [], $headers = [], $callback = null, $timeout = 600)
{
    http_stream_request($url, $data, $headers, $callback, $timeout);
}

/**
 * 根据学校名称 (school_name) 和专业代码 (major_code) 对学校数据进行去重。
 *
 * @param array $schools 包含学校信息的数组，每个元素都应是包含 'school_name' 和 'major_code' 键的关联数组。
 * 示例:
 * [
 * {"school_name":"东华大学","major_code":"081200","id":84649},
 * {"school_name":"上海大学","major_code":"081200","id":84651}
 * ]
 * @return array 去重后的学校数据数组。如果存在重复的 school_name 和 major_code 组合，
 * 则只保留第一次出现的记录。
 */
function uniqueSchoolData(array $schools): array
{
    $items = [];
    foreach($schools as $v){
        $items[$v['school_name'].$v['major_code'].$v['college']] = $v;
    }
    return array_values($items);
}

?>
