import request from "@/utils/request";
import { ApiResponse, Tag, AddTagParams, EditTagParams } from "@/types";

/**
 * 获取所有标签
 * @returns Promise
 */
export function getAllTags(): Promise<
  ApiResponse<{
    first: Tag[],
    second: Tag[],
    third: Tag[],
  }>
> {
  return request({
    url: "/tag/all",
    method: "get",
  });
}

/**
 * 添加标签
 * @param data - 标签数据
 * @returns Promise
 */
export function addTag(data: AddTagParams): Promise<ApiResponse<Tag>> {
  return request({
    url: "/tag/add",
    method: "post",
    data,
  });
}

/**
 * 编辑标签
 * @param data - 标签数据
 * @returns Promise
 */
export function editTag(data: EditTagParams): Promise<ApiResponse<Tag>> {
  return request({
    url: "/tag/edit",
    method: "post",
    data,
  });
}

/**
 * 删除标签
 * @param id - 标签ID
 * @returns Promise
 */
export function deleteTag(id: number): Promise<ApiResponse<null>> {
  return request({
    url: "/tag/delete",
    method: "get",
    params: { id },
  });
}

export default {
  getAllTags,
  addTag,
  editTag,
  deleteTag,
};
