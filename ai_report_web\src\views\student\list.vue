<template>
  <div class="student-list-container">
    <!-- 筛选区域 -->
    <div class="filter-section">
      <div class="filter-header">
        <div class="filter-icon"></div>
        <div class="filter-title">筛选区</div>
      </div>
      <div class="filter-form-container">
        <div class="filter-form">
          <div class="form-row">
            <div class="form-item">
              <div class="item-label">学员姓名</div>
              <el-input
                v-model="searchForm.name"
                placeholder="请输入学员姓名"
              ></el-input>
            </div>
            <div class="form-item">
              <div class="item-label">联系方式</div>
              <el-input
                v-model="searchForm.phone"
                placeholder="请输入联系方式"
              ></el-input>
            </div>
            <div class="form-item">
              <div class="item-label">标签筛选</div>
              <el-cascader
                v-model="searchForm.tagId"
                :options="tagCascaderOptions"
                placeholder="请选择标签"
                clearable
                filterable
                multiple
                collapse-tags
                :props="{
                  checkStrictly: true,
                  value: 'id',
                  label: 'name',
                  children: 'children',
                  expandTrigger: 'hover',
                }"
              ></el-cascader>
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <div class="item-label">本科院校</div>
              <el-select
                v-model="searchForm.undergraduate"
                placeholder="输入关键字搜索院校"
                filterable
                remote
                reserve-keyword
                :remote-method="remoteSearchSchoolForList"
                :loading="schoolListSearchLoading"
                clearable
              >
                <el-option
                  v-for="item in schoolListOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </div>
            <div class="form-item">
              <div class="item-label">本科专业</div>
              <!-- <el-select v-model="searchForm.undergraduateMajor" placeholder="输入关键字搜索专业" filterable remote reserve-keyword :remote-method="remoteSearchMajorForList" :loading="majorListSearchLoading" clearable :disabled="!searchForm.undergraduate">
                <el-option v-for="item in majorListOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select> -->

              <el-select
                v-model="searchForm.undergraduateMajor"
                placeholder="输入关键字搜索专业"
                filterable
                remote
                reserve-keyword
                :remote-method="searchRemoteMajor"
                :loading="majorSearchLoading"
                class="full-width"
                :disabled="!searchForm.undergraduate"
                @focus="handleMajorFocus"
                popper-class="major-select-dropdown"
              >
                <el-option
                  v-for="item in majorOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
                <div
                  v-if="majorHasMore && !majorSearchLoading"
                  class="load-more-option"
                  @click="loadMoreMajors"
                >
                  <el-button
                    style="color: #1bb394; padding-left: 20px"
                    type="text"
                    size="small"
                    >点击加载更多...</el-button
                  >
                </div>
                <div
                  v-if="majorSearchLoading && majorCurrentPage > 1"
                  class="loading-option"
                >
                  <el-icon>
                    <Loading />
                  </el-icon>
                  加载中...
                </div>
              </el-select>
            </div>
            <div class="form-item">
              <div class="item-label">目标院校</div>
              <el-select
                v-model="searchForm.targetSchool"
                placeholder="输入关键字搜索院校"
                filterable
                remote
                reserve-keyword
                :remote-method="remoteSearchTargetSchoolForList"
                :loading="targetSchoolListSearchLoading"
                clearable
              >
                <el-option
                  v-for="item in targetSchoolListOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </div>
            <div class="form-item">
              <div class="item-label">目标专业</div>
              <el-select
                v-model="searchForm.targetMajor"
                placeholder="请输入关键字搜索专业"
                filterable
                remote
                reserve-keyword
                :remote-method="remoteSearchTargetMajorForList"
                :loading="targetMajorListSearchLoading"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="item in targetMajorListOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </div>
          </div>
        </div>

        <div class="filter-buttons">
          <el-button type="primary" class="filter-button" @click="getList"
            >查询</el-button
          >
          <el-button class="filter-button" @click="resetSearch">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 学员列表区域 -->
    <div class="student-list-section">
      <div class="list-header">
        <div class="list-header-content">
          <div class="list-icon"></div>
          <div class="list-title">学生列表</div>
          <!-- <el-button
            type="primary"
            class="change-teacher-button"
            @click="handleChangeTeacher"
            :disabled="selectedStudents.length === 0"
            >更换服务老师</el-button
          > -->
        </div>
        <el-button
          type="primary"
          class="add-student-button"
          @click="handleAddStudent"
          >添加学员</el-button
        >
      </div>

      <el-table
        :data="tableData"
        style="width: 100%"
        v-loading="loading"
        class="custom-table"
      >
        <el-table-column
          label="序号"
          type="index"
          width="60"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="name"
          label="姓名"
          width="120"
          align="center"
        ></el-table-column>
        <el-table-column label="性别" width="60" align="center">
          <template #default="scope">
            <span>{{ getSexText(scope.row.sex) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="phone"
          label="联系方式"
          width="130"
          align="center"
        ></el-table-column>
        <el-table-column label="服务老师" width="120" align="center">
          <template #default="scope">
            <span>{{ scope.row.teacherName || "未分配" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="本科院校" align="center">
          <template #default="scope">
            <span>{{ scope.row.undergraduateSchoolName || "未填写" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="本科专业" align="center">
          <template #default="scope">
            <span>{{ scope.row.undergraduateMajorName || "未填写" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="目标院校" align="center">
          <template #default="scope">
            <span>{{ scope.row.targetSchoolName || "未填写" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="目标专业" align="center">
          <template #default="scope">
            <span>{{
              scope.row.targetMajorName || "未填写"
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="标签" align="center">
          <template #default="scope">
            <div class="tag-container">
              <span
                v-for="tag in scope.row.tags"
                :key="tag.value"
                :class="[getTagDisplayClass(tag), 'tag']"
                >{{ tag.label }}</span
              >
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160" align="center">
          <template #default="scope">
            <span @click="handleEdit(scope.row)" class="edit-button">编辑</span>
            <span @click="handleEvaluate(scope.row)" class="evaluate-button"
              >详情</span
            >
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          background
          layout="total,  prev, pager, next, jumper"
          :total="total"
          :page-size="pageSize"
          :current-page="currentPage"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </div>

    <!-- 学员表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="
        dialogType === 'add'
          ? '添加学员'
          : dialogType === 'edit'
          ? '编辑学员'
          : '学员详情'
      "
      width="90%"
      top="5vh"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      class="student-dialog"
    >
      <div class="dialog-container">
        <div class="dialog-sidebar">
          <div
            v-for="(section, index) in formSections"
            :key="index"
            class="sidebar-item"
            :class="{
              active: activeSection == index,
            }"
            @click="scrollToSection(index)"
          >
            <div class="sidebar-item-number">{{ index + 1 }}</div>
            <div class="sidebar-item-text">{{ section }}</div>
          </div>
        </div>
        <div class="dialog-content" ref="formContent">
          <el-form
            :model="studentForm"
            label-width="100px"
            :rules="formRules"
            ref="studentFormRef"
            class="student-form"
          >
            <student-basic
              :student-form="studentForm"
              :dialog-type="dialogType"
              :school-options="schoolOptions"
              :major-options="majorOptions"
              :target-major-options="targetMajorOptions"
              :tag-options="tagOptions"
              :school-search-loading="schoolSearchLoading"
              :major-search-loading="majorSearchLoading"
              :target-major-search-loading="targetMajorSearchLoading"
              @school-change="handleSchoolChange"
              @search-school="remoteSearchSchool"
              @search-major="remoteSearchMajor"
              @search-target-major="remoteSearchTargetMajor"
            />

            <!-- 这里可以添加其他组件，如成绩情况、英语基础等 -->
          </el-form>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">{{
            dialogType === "detail" ? "关闭" : "取消"
          }}</el-button>
          <el-button
            v-if="dialogType !== 'detail'"
            type="primary"
            @click="submitForm"
            :loading="submitLoading"
            class="action-button"
            >确定</el-button
          >
        </span>
      </template>
    </el-dialog>

    <!-- 老师选择器 -->
    <teacher-selector
      v-model:visible="teacherSelectorVisible"
      @select="handleTeacherSelected"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, nextTick } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
import { searchSchool, searchMajor, searchTargetMajor } from "@/api/school";
import { getAllTags } from "@/api/tag";
import { searchTeacher } from "@/api/user";
import StudentBasic from "./components/StudentBasic.vue";
import TeacherSelector from "@/components/TeacherSelector.vue";
import {
  getStudentList,
  getStudentDetail,
  addStudent,
  editStudent,
  deleteStudent,
  updateTeacher,
} from "@/api/student";

const router = useRouter();
const studentFormRef = ref(null);

// 标签选项 - 用于学生表单中的标签选择
const tagOptions = [
  { value: "985", label: "985", class: "tag-orange" },
  { value: "211", label: "211", class: "tag-purple" },
  { value: "double", label: "双一流", class: "tag-green" },
  { value: "important", label: "重点", class: "tag-blue" },
];

// 所有标签列表 - 用于标签筛选
const allTags = ref([]);
const lastMajorQuery = ref("");
// 级联选择器标签数据
const tagCascaderOptions = ref([]);

// 下拉搜索相关 - 用于学生表单
const schoolSearchLoading = ref(false);
const schoolOptions = ref([]);
const schoolSearchTimeout = ref(null);
const schoolSearchCache = {};

// 下拉搜索相关 - 用于列表筛选
const schoolListSearchLoading = ref(false);
const schoolListOptions = ref([]);
const schoolListSearchTimeout = ref(null);
const schoolListSearchCache = {};

const majorSearchLoading = ref(false);
const majorOptions = ref([]);
const majorSearchTimeout = ref(null);
const majorSearchCache = {};

// 下拉搜索相关 - 用于列表筛选专业
const majorListSearchLoading = ref(false);
const majorListOptions = ref([]);
const majorListSearchTimeout = ref(null);
const majorListSearchCache = {};

const targetMajorSearchLoading = ref(false);
const targetMajorOptions = ref([]);
const targetMajorSearchTimeout = ref(null);
const targetMajorSearchCache = {};

// 下拉搜索相关 - 用于列表筛选目标院校
const targetSchoolListSearchLoading = ref(false);
const targetSchoolListOptions = ref([]);
const targetSchoolListSearchTimeout = ref(null);
const targetSchoolListSearchCache = {};

// 下拉搜索相关 - 用于列表筛选目标专业
const targetMajorListSearchLoading = ref(false);
const targetMajorListOptions = ref([]);
const targetMajorListSearchTimeout = ref(null);
const targetMajorListSearchCache = {};

// 搜索表单
const searchForm = reactive({
  name: "",
  phone: "",
  teacher: "",
  school: "",
  undergraduate: "",
  undergraduateMajor: "",
  targetSchool: "",
  targetMajor: "",
  tagId: [], // 标签ID - 使用数组类型
});

// 表格数据
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const pageSize = ref(8);
const currentPage = ref(1);

// 对话框相关
const dialogVisible = ref(false);
const dialogType = ref("add"); // add, edit, or detail
const submitLoading = ref(false);

// 老师选择器相关
const teacherSelectorVisible = ref(false);
const selectedStudents = ref([]);

// 选择学生处理
const handleSelectionChange = (selection) => {
  selectedStudents.value = selection;
};

// 打开更换服务老师弹窗
const handleChangeTeacher = () => {
  if (selectedStudents.value.length === 0) {
    ElMessage.warning("请先选择学生");
    return;
  }
  teacherSelectorVisible.value = true;
};

// 老师选择后的处理
const handleTeacherSelected = (teacher) => {
  if (!teacher || !teacher.id) {
    ElMessage.warning("请选择有效的服务老师");
    return;
  }

  const studentIds = selectedStudents.value.map((student) => student.id);

  // 调用API更新学生的服务老师
  updateTeacher({
    studentIds,
    teacherId: teacher.id,
  })
    .then((res) => {
      if (res.code === 0) {
        ElMessage.success("更换服务老师成功");
        // 刷新列表
        getList();
      } else {
        ElMessage.error(res.msg || "更换服务老师失败");
      }
    })
    .catch((err) => {
      console.error("更换服务老师失败", err);
      ElMessage.error("更换服务老师失败，请稍后重试");
    });
};

// 表单部分相关
const formContent = ref(null);
const formSections = [
  "个人基础信息",
  "本科成绩情况",
  "英语基础",
  "目标院校倾向",
  "考研成绩预估",
];
const activeSection = ref(0);
const sectionRefs = ref([]);
// 记录最后一次点击导航项的时间
const lastClickTime = ref(0);

// 节流函数，用于优化滚动事件处理
const throttle = (func, limit) => {
  let lastFunc;
  let lastRan;
  return function (...args) {
    if (!lastRan) {
      func.apply(this, args);
      lastRan = Date.now();
    } else {
      clearTimeout(lastFunc);
      lastFunc = setTimeout(() => {
        if (Date.now() - lastRan >= limit) {
          func.apply(this, args);
          lastRan = Date.now();
        }
      }, limit - (Date.now() - lastRan));
    }
  };
};

// 滚动到指定部分
const scrollToSection = (index) => {
  // 记录点击时间
  lastClickTime.value = Date.now();
  console.log("记录点击时间:", lastClickTime.value);

  // 先设置活动部分
  activeSection.value = index;
  console.log("设置activeSection为:", index);

  // 延迟一下，确保DOM已经渲染完成
  setTimeout(() => {
    // 使用ID选择器更精确地定位到对应的部分
    const sectionId = `section-${index}`;
    const section = document.getElementById(sectionId);

    if (section) {
      // 滚动到对应的部分，并添加一些偏移量以获得更好的视觉效果
      formContent.value.scrollTop = section.offsetTop - 20;
      console.log("滚动到section:", sectionId, "位置:", section.offsetTop - 20);
    } else {
      // 如果找不到对应的部分，则使用类选择器作为备选方案
      const sections = document.querySelectorAll(".form-section");
      if (sections[index]) {
        formContent.value.scrollTop = sections[index].offsetTop - 20;
        console.log(
          "滚动到sections[index]:",
          index,
          "位置:",
          sections[index].offsetTop - 20
        );
      } else {
        console.log("未找到对应的section");
      }
    }

    // 再次确认activeSection的值
    activeSection.value = index;
    console.log("再次确认activeSection为:", index);
  }, 50);
};

// 监听滚动事件，更新活动部分
const handleScroll = () => {
  if (!formContent.value) return;

  // 获取所有带有id的部分
  const sections = [];
  for (let i = 0; i < formSections.length; i++) {
    const section = document.getElementById(`section-${i}`);
    if (section) {
      sections.push({ index: i, element: section });
    }
  }

  // 如果没有找到任何部分，则直接返回
  if (sections.length === 0) {
    console.log("未找到任何section元素");
    return;
  }

  // 检查是否刚刚点击了导航项（300毫秒内）- 缩短时间使响应更快
  const justClicked = Date.now() - lastClickTime.value < 300;

  if (justClicked) {
    // 如果刚刚点击了导航项，不要改变activeSection的值
    console.log(
      "刚刚点击了导航项，保持activeSection不变:",
      activeSection.value
    );
    return;
  }

  const scrollTop = formContent.value.scrollTop;
  const containerTop = formContent.value.getBoundingClientRect().top;

  // 优化的逻辑：更敏感的section检测
  let newActiveSection = 0;
  let minDistance = Infinity;

  sections.forEach((section) => {
    const sectionRect = section.element.getBoundingClientRect();
    const sectionTop = sectionRect.top;

    // 计算section顶部到容器顶部的距离
    const distanceToTop = Math.abs(sectionTop - containerTop);

    // 降低阈值，使切换更敏感 - 从100改为60
    if (sectionTop <= containerTop + 60 && distanceToTop < minDistance) {
      minDistance = distanceToTop;
      newActiveSection = section.index;
    }
  });

  // 备用逻辑：如果没有找到合适的section，使用传统的offsetTop方法
  // 同样降低阈值使其更敏感
  if (minDistance === Infinity) {
    for (let i = sections.length - 1; i >= 0; i--) {
      const section = sections[i];
      if (scrollTop >= section.element.offsetTop - 60) {
        newActiveSection = section.index;
        break;
      }
    }
  }

  // 只有当活动部分发生变化时才更新
  if (activeSection.value !== newActiveSection) {
    console.log(
      "更新activeSection为:",
      newActiveSection,
      "从:",
      activeSection.value
    );
    activeSection.value = newActiveSection;
  }
};

// 创建节流版本的滚动处理函数 - 每16ms最多执行一次，接近60fps的流畅度
const throttledHandleScroll = throttle(handleScroll, 16);

// 监听对话框打开，初始化滚动事件
watch(dialogVisible, (val) => {
  if (val) {
    nextTick(() => {
      if (formContent.value) {
        // 添加滚动事件监听器 - 使用节流版本
        formContent.value.addEventListener("scroll", throttledHandleScroll);

        // 重置活动部分为第一个
        activeSection.value = 0;
        console.log("对话框打开，重置activeSection为0");

        // 延迟一下，确保DOM已经渲染完成
        setTimeout(() => {
          // 初始滚动到顶部
          formContent.value.scrollTop = 0;

          // 再次确认activeSection为0
          activeSection.value = 0;
          console.log("延迟后再次确认activeSection为0");

          // 检查section元素是否存在
          const sections = [];
          for (let i = 0; i < formSections.length; i++) {
            const section = document.getElementById(`section-${i}`);
            if (section) {
              sections.push({ index: i, element: section });
              console.log(`找到section-${i}，位置:`, section.offsetTop);
            } else {
              console.log(`未找到section-${i}`);
            }
          }
        }, 300);
      }
    });
  } else {
    if (formContent.value) {
      // 移除滚动事件监听器 - 使用节流版本
      formContent.value.removeEventListener("scroll", throttledHandleScroll);
    }
  }
});

// 学员表单
const studentForm = reactive({
  // 基本信息
  id: "",
  name: "",
  sex: "", // 1-男, 2-女, 3-其他
  phone: "",
  teacherPhone: "",
  undergraduateSchool: "",
  undergraduateMajor: "",
  disciplineCategory: null, // 学科门类
  firstLevelDiscipline: null, // 一级学科
  targetMajor: "",
  majorCode: "",
  isPostgraduate: false, // 是否研究生
  examYear: "", // 考研届数: 2026, 2027, 2028, 2029, "定向"
  isMultiDisciplinary: "", // 是否跨专业: 1-是, 2-否

  // 本科成绩情况
  undergraduateTranscript: [
    { id: 1, title: "高数(上)期末考试", score: "" },
    { id: 2, title: "高数(下)期末考试", score: "" },
    { id: 3, title: "积分论期末考试", score: "" },
    { id: 4, title: "线性代数期末考试", score: "" },
  ],

  // 英语基础
  englishScore: "", // 高考英语成绩
  cet4: "", // 四级成绩
  cet6: "", // 六级成绩
  tofelScore: "", // 托福成绩
  ieltsScore: "", // 雅思成绩
  englishAbility: "", // 英语能力

  // 目标院校倾向
  targetRegion: "", // 地区倾向: "A区", "B区"
  targetProvinces: [], // 省份选择
  targetSchool: "", // 梦校
  schoolLevel: "", // 院校层次: "985", "211", "双一流"

  // 考研成绩估算
  politics: "", // 政治
  englishType: "", // 英语类型: "英语一", "英语二"
  mathType: "", // 数学类型: "数学一", "数学二", "数学三"
  englishS: 0,
  mathScore: 0,
  professionalScore: 0,
  totalScore: "", // 总分

  // 个性化需求
  personalNeeds: "",

  // 标签
  tags: [], // 标签列表
});

// 表单验证规则
const formRules = {
  name: [{ required: true, message: "请输入学员姓名", trigger: "blur" }],
  phone: [
    { required: true, message: "请输入手机号码", trigger: "blur" },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号码",
      trigger: "blur",
    },
  ],
  undergraduateSchool: [
    { required: true, message: "请选择本科院校", trigger: "change" },
  ],
  undergraduateMajor: [
    { required: true, message: "请选择本科专业", trigger: "change" },
  ],
  targetMajor: [
    { required: true, message: "请选择目标专业", trigger: "change" },
  ],
};

// 获取表格数据
const getList = () => {
  loading.value = true;

  // 构建查询参数
  const params = {
    page: currentPage.value,
    limit: pageSize.value,
    name: searchForm.name,
    phone: searchForm.phone,
    teacherId: searchForm.teacher, // 使用teacherId参数，而不是teacher
    school: searchForm.school,
    undergraduate: searchForm.undergraduate,
    undergraduateMajor: searchForm.undergraduateMajor,
    targetSchool: searchForm.targetSchool,
    targetMajor: searchForm.targetMajor,
  };

  // 处理标签ID参数 - 如果有选择标签，则添加到参数中
  if (searchForm.tagId && searchForm.tagId.length > 0) {
    // 从级联选择器中提取标签ID
    const tagIds = searchForm.tagId.map((item) => {
      // 级联选择器返回的是数组，最后一个元素是选中的值
      return Array.isArray(item) ? item[item.length - 1] : item;
    });

    // 将标签ID添加到参数中
    params.tagId = tagIds;
  }

  // 请求后端API
  console.log("发送获取学生列表请求，参数:", params);
  getStudentList(params)
    .then((res) => {
      console.log("获取学生列表响应:", res);
      if (res.code === 0) {
        // 处理标签数据
        if (res.data && Array.isArray(res.data)) {
          res.data.forEach((student) => {
            if (student.tags && Array.isArray(student.tags)) {
              // 确保每个标签都有正确的样式类
              student.tags.forEach((tag) => {
                // 在这里不设置class，让getTagDisplayClass函数处理
                // 这样可以确保一级标签不添加随机颜色，而二级和三级标签添加随机颜色
                if (!tag.label && tag.name) {
                  tag.label = tag.name;
                }
              });
            }
          });
        }

        tableData.value = res.data;
        total.value = res.total;
      } else {
        ElMessage.error(res.msg || "获取学生列表失败");
        tableData.value = [];
        total.value = 0;
      }
    })
    .catch((err) => {
      console.error("获取学生列表失败", err);
      ElMessage.error("获取学生列表失败，请稍后重试");
      tableData.value = [];
      total.value = 0;
    })
    .finally(() => {
      loading.value = false;
    });
};

// 随机颜色数组
const tagColors = [
  "tag-orange",
  "tag-purple",
  "tag-green",
  "tag-blue",
  "tag-pink",
];

// 获取随机颜色类
const getRandomTagClass = () => {
  const randomIndex = Math.floor(Math.random() * tagColors.length);
  return tagColors[randomIndex];
};

// 获取性别文本
const getSexText = (sex) => {
  if (sex === "1" || sex === 1) return "男";
  if (sex === "2" || sex === 2) return "女";
  if (sex === "3" || sex === 3) return "其他";
  return "";
};

// 获取标签显示样式类
const getTagDisplayClass = (tag) => {
  // // 如果标签已经有样式类，直接使用
  // if (tag.class) return tag.class;

  // // 检查标签是否在allTags中
  // const foundTag = allTags.value.find(
  //   (t) => t.id === tag.value || t.id === parseInt(tag.value)
  // );
  // if (foundTag) {
  //   // 检查是否是一级标签
  //   const isFirstLevel = !foundTag.parent_id;
  //   if (isFirstLevel) {
  //     // 一级标签使用默认样式
  //     return "";
  //   } else {
  //     // 二级和三级标签使用随机颜色
  //     return foundTag.class || getRandomTagClass();
  //   }
  // }

  // 默认返回随机颜色
  return getRandomTagClass();
};

// 加载所有标签
const loadAllTags = () => {
  getAllTags()
    .then((res) => {
      if (res.code === 0) {
        // 获取各层级标签
        const firstLevel = res.data.first || [];
        const secondLevel = res.data.second || [];
        const thirdLevel = res.data.third || [];

        // 为二级和三级标签添加随机颜色类
        secondLevel.forEach((tag) => {
          tag.class = getRandomTagClass();
        });

        thirdLevel.forEach((tag) => {
          tag.class = getRandomTagClass();
        });

        // 将所有标签合并到一个数组（用于兼容旧代码）
        allTags.value = [...firstLevel, ...secondLevel, ...thirdLevel];

        // 构建级联选择器数据结构
        tagCascaderOptions.value = firstLevel.map((firstTag) => {
          // 找出属于当前一级标签的所有二级标签
          const children = secondLevel
            .filter((secondTag) => secondTag.parent_id === firstTag.id)
            .map((secondTag) => {
              // 找出属于当前二级标签的所有三级标签
              const grandChildren = thirdLevel
                .filter((thirdTag) => thirdTag.parent_id === secondTag.id)
                .map((thirdTag) => ({
                  id: thirdTag.id,
                  name: thirdTag.name,
                  color: thirdTag.color || firstTag.color,
                  class: thirdTag.class, // 使用随机分配的颜色类
                }));

              return {
                id: secondTag.id,
                name: secondTag.name,
                color: secondTag.color || firstTag.color,
                class: secondTag.class, // 使用随机分配的颜色类
                children: grandChildren.length > 0 ? grandChildren : undefined,
              };
            });

          return {
            id: firstTag.id,
            name: firstTag.name,
            color: firstTag.color,
            // 一级标签不添加随机颜色类
            children: children.length > 0 ? children : undefined,
          };
        });
      } else {
        ElMessage.error(res.msg || "获取标签失败");
      }
    })
    .catch((err) => {
      console.error("获取标签失败", err);
      ElMessage.error("获取标签失败，请稍后重试");
    });
};

// 页面加载时获取数据
onMounted(() => {
  getList();
  loadAllTags(); // 加载标签数据
});

// 编辑学员
const handleEdit = async (row) => {
  dialogType.value = "edit";

  // 显示加载中
  submitLoading.value = true;

  try {
    // 获取学生详情
    const res = await getStudentDetail(row.id);

    if (res.code === 0) {
      // 填充表单数据
      Object.keys(studentForm).forEach((key) => {
        if (key in res.data) {
          if (key === "tags") {
            // 确保标签ID是整数
            studentForm[key] = res.data[key]
              .map((tag) => {
                const tagId = tag.value || tag;
                return parseInt(tagId, 10);
              })
              .filter((id) => !isNaN(id) && id > 0);
            console.log("编辑时处理后的标签ID:", studentForm[key]);
          } else {
            // 确保null值被转换为空字符串
            studentForm[key] = res.data[key] === null ? "" : res.data[key];
          }
        }
      });

      // 预加载本科院校选项
      if (studentForm.undergraduateSchool) {
        // 添加当前本科院校到选项中
        if (
          studentForm.undergraduateSchool &&
          res.data.undergraduateSchoolName
        ) {
          schoolOptions.value = [
            {
              value: studentForm.undergraduateSchool,
              label: res.data.undergraduateSchoolName,
            },
          ];
        }

        // 预加载本科专业选项
        if (studentForm.undergraduateMajor && res.data.undergraduateMajorName) {
          // 添加当前本科专业到选项中
          majorOptions.value = [
            {
              value: studentForm.undergraduateMajor,
              label: res.data.undergraduateMajorName,
            },
          ];

          // 确保本科专业显示名称
          studentForm.undergraduateMajorName = res.data.undergraduateMajorName;
          console.log("预加载本科专业:", {
            id: studentForm.undergraduateMajor,
            name: res.data.undergraduateMajorName,
          });
        } else {
          // 预加载专业列表
          remoteSearchMajor("");
        }
      }

      // 确保专业代码正确显示
      console.log("原始majorCode:", res.data.majorCode);
      if (res.data.majorCode) {
        studentForm.majorCode = res.data.majorCode;
      }

      // 预加载目标专业选项 - 支持多选
      if (studentForm.targetMajor && res.data.targetMajorName) {
        // 处理目标专业ID（可能是逗号分隔的字符串）
        const targetMajorIds =
          typeof studentForm.targetMajor === "string"
            ? studentForm.targetMajor.split(",").filter((id) => id.trim())
            : Array.isArray(studentForm.targetMajor)
            ? studentForm.targetMajor
            : [studentForm.targetMajor];

        // 处理目标专业名称（可能是数组或字符串）
        const targetMajorNames = Array.isArray(res.data.targetMajorName)
          ? res.data.targetMajorName
          : typeof res.data.targetMajorName === "string"
          ? res.data.targetMajorName.split(",").filter((name) => name.trim())
          : [res.data.targetMajorName];

        // 处理专业代码（可能是逗号分隔的字符串）
        const majorCodes = studentForm.majorCode
          ? studentForm.majorCode.split(",").filter((code) => code.trim())
          : [];

        // 确保目标专业显示名称
        studentForm.targetMajorName = targetMajorNames;

        // 将targetMajor转换为数组格式
        studentForm.targetMajor = targetMajorIds;

        // 添加到目标专业选项中
        targetMajorOptions.value = targetMajorIds.map((id, index) => ({
          value: id,
          label: targetMajorNames[index] || `专业ID: ${id}`,
          code: majorCodes[index] || "",
          id: id,
          major_code: majorCodes[index] || "",
        }));

        console.log("预加载目标专业(详情):", {
          ids: targetMajorIds,
          names: targetMajorNames,
          codes: majorCodes,
          majorCode: studentForm.majorCode,
        });
      }
      studentForm.englishS = res.data.englishS;
      studentForm.mathScore = res.data.mathScore;
      studentForm.professionalScore = res.data.professionalScore;

      // 确保梦校(targetSchool)显示名称
      if (studentForm.targetSchool && res.data.targetSchoolName) {
        // 直接设置梦校名称
        studentForm.targetSchoolName = res.data.targetSchoolName;

        // 同时更新dreamSchoolOptions，确保下拉框也能正确显示
        setTimeout(() => {
          const studentBasicComponent = document.querySelector("student-basic");
          if (
            studentBasicComponent &&
            studentBasicComponent.__vueParentComponent
          ) {
            const componentInstance =
              studentBasicComponent.__vueParentComponent.ctx;
            if (componentInstance && componentInstance.dreamSchoolOptions) {
              componentInstance.dreamSchoolOptions = [
                {
                  value: studentForm.targetSchool,
                  label: res.data.targetSchoolName,
                },
              ];
            }
          }
        }, 100);
      }

      // 确保省份选择正确显示
      if (studentForm.targetProvinces) {
        console.log("编辑时的省份数据:", studentForm.targetProvinces);

        // 不再自动添加所有区域，严格按照后端返回的数据显示

        // 使用 nextTick 确保在 DOM 更新后再设置省份
        nextTick(() => {
          console.log("编辑时设置省份选择:", studentForm.targetProvinces);
        });
      }

      studentForm.region = studentForm.targetRegion || [];

      dialogVisible.value = true;
    } else {
      ElMessage.error(res.msg || "获取学生详情失败");
    }
  } catch (err) {
    console.error("获取学生详情失败", err);
    ElMessage.error("获取学生详情失败，请稍后重试");
  } finally {
    submitLoading.value = false;
  }
};

// 查看学员详情
const handleEvaluate = async (row) => {
  // 打开详情对话框
  dialogType.value = "detail";

  // 显示加载中
  submitLoading.value = true;

  try {
    // 获取学生详情
    const res = await getStudentDetail(row.id);

    if (res.code === 0) {
      // 填充表单数据
      Object.keys(studentForm).forEach((key) => {
        if (key in res.data) {
          if (key === "tags") {
            // 确保标签ID是整数
            studentForm[key] = res.data[key]
              .map((tag) => {
                const tagId = tag.value || tag;
                return parseInt(tagId, 10);
              })
              .filter((id) => !isNaN(id) && id > 0);
            console.log("详情时处理后的标签ID:", studentForm[key]);
          } else {
            // 确保null值被转换为空字符串
            studentForm[key] = res.data[key] === null ? "" : res.data[key];
          }
        }
      });

      // 预加载本科院校选项
      if (studentForm.undergraduateSchool) {
        // 添加当前本科院校到选项中
        if (
          studentForm.undergraduateSchool &&
          res.data.undergraduateSchoolName
        ) {
          schoolOptions.value = [
            {
              value: studentForm.undergraduateSchool,
              label: res.data.undergraduateSchoolName,
            },
          ];
        }

        // 预加载本科专业选项
        if (studentForm.undergraduateMajor && res.data.undergraduateMajorName) {
          // 添加当前本科专业到选项中
          majorOptions.value = [
            {
              value: studentForm.undergraduateMajor,
              label: res.data.undergraduateMajorName,
            },
          ];

          // 确保本科专业显示名称
          studentForm.undergraduateMajorName = res.data.undergraduateMajorName;
          console.log("预加载本科专业(详情):", {
            id: studentForm.undergraduateMajor,
            name: res.data.undergraduateMajorName,
          });
        } else {
          // 预加载专业列表
          remoteSearchMajor("");
        }
      }

      // 确保专业代码正确显示
      console.log("编辑模式原始majorCode:", res.data.majorCode);
      if (res.data.majorCode) {
        studentForm.majorCode = res.data.majorCode;
      }

      // 预加载目标专业选项 - 支持多选
      if (studentForm.targetMajor && res.data.targetMajorName) {
        // 处理目标专业ID（可能是逗号分隔的字符串）
        const targetMajorIds =
          typeof studentForm.targetMajor === "string"
            ? studentForm.targetMajor.split(",").filter((id) => id.trim())
            : Array.isArray(studentForm.targetMajor)
            ? studentForm.targetMajor
            : [studentForm.targetMajor];

        // 处理目标专业名称（可能是数组或字符串）
        const targetMajorNames = Array.isArray(res.data.targetMajorName)
          ? res.data.targetMajorName
          : typeof res.data.targetMajorName === "string"
          ? res.data.targetMajorName.split(",").filter((name) => name.trim())
          : [res.data.targetMajorName];

        // 处理专业代码（可能是逗号分隔的字符串）
        const majorCodes = studentForm.majorCode
          ? studentForm.majorCode.split(",").filter((code) => code.trim())
          : [];

        // 确保目标专业显示名称
        studentForm.targetMajorName = targetMajorNames;

        // 将targetMajor转换为数组格式
        studentForm.targetMajor = targetMajorIds;

        // 添加到目标专业选项中
        targetMajorOptions.value = targetMajorIds.map((id, index) => ({
          value: id,
          label: targetMajorNames[index] || `专业ID: ${id}`,
          code: majorCodes[index] || "",
          id: id,
          major_code: majorCodes[index] || "",
        }));

        console.log("预加载目标专业(编辑):", {
          ids: targetMajorIds,
          names: targetMajorNames,
          codes: majorCodes,
          majorCode: studentForm.majorCode,
        });
      }

      // 预加载梦校选项
      if (studentForm.targetSchool && res.data.targetSchoolName) {
        // 直接设置梦校名称
        studentForm.targetSchoolName = res.data.targetSchoolName;

        // 在StudentBasic组件中设置梦校选项
        setTimeout(() => {
          const studentBasicComponent = document.querySelector("student-basic");
          if (
            studentBasicComponent &&
            studentBasicComponent.__vueParentComponent
          ) {
            const componentInstance =
              studentBasicComponent.__vueParentComponent.ctx;
            if (componentInstance && componentInstance.dreamSchoolOptions) {
              componentInstance.dreamSchoolOptions = [
                {
                  value: studentForm.targetSchool,
                  label: res.data.targetSchoolName,
                },
              ];
            }
          }
        }, 100);
      }

      // 确保省份选择正确显示
      if (studentForm.targetProvinces) {
        console.log("详情视图的省份数据:", studentForm.targetProvinces);

        // 不再自动添加所有区域，严格按照后端返回的数据显示

        // 使用 nextTick 确保在 DOM 更新后再设置省份
        nextTick(() => {
          console.log("详情视图设置省份选择:", studentForm.targetProvinces);
        });
      }

      dialogVisible.value = true;
    } else {
      ElMessage.error(res.msg || "获取学生详情失败");
    }
  } catch (err) {
    console.error("获取学生详情失败", err);
    ElMessage.error("获取学生详情失败，请稍后重试");
  } finally {
    submitLoading.value = false;
  }
};

// 添加学员
const handleAddStudent = () => {
  dialogType.value = "add";
  // 重置表单 - 确保所有字段都有默认值，避免显示null
  Object.keys(studentForm).forEach((key) => {
    if (key === "tags" || key === "targetProvinces") {
      studentForm[key] = [];
    } else if (key === "mathScores") {
      studentForm[key] = [
        { id: 1, title: "高数(上)期末考试", score: "" },
        { id: 2, title: "高数(下)期末考试", score: "" },
        { id: 3, title: "积分论期末考试", score: "" },
        { id: 4, title: "线性代数期末考试", score: "" },
      ];
    } else if (key === "specializedCourses") {
      studentForm[key] = [
        { id: 1, title: "专业课一", name: "", score: "" },
        { id: 2, title: "专业课二", name: "", score: "" },
        { id: 3, title: "专业课三", name: "", score: "" },
      ];
    } else if (key === "estimatedScores") {
      studentForm[key] = [
        { id: 1, title: "专业课一", name: "", score: "" },
        { id: 2, title: "专业课二", name: "", score: "" },
        { id: 3, title: "专业课三", name: "", score: "" },
      ];
    } else {
      // 确保所有其他字段都是空字符串，而不是null或undefined
      studentForm[key] = "";
    }
  });

  // 清空选项
  schoolOptions.value = [];
  majorOptions.value = [];
  targetMajorOptions.value = [];

  dialogVisible.value = true;
};

// 提交表单
const submitForm = async () => {
  if (!studentFormRef.value) return;

  try {
    await studentFormRef.value.validate();

    submitLoading.value = true;

    // 准备提交的数据
    const formData = {
      ...studentForm,
      mathScoresJson: "",
      specializedCoursesJson: "",
      estimatedScoresJson: "",
    };
    // 处理特殊字段
    // 确保数组字段不为空
    if (!formData.mathScores || !Array.isArray(formData.mathScores)) {
      formData.mathScores = [];
    }

    if (
      !formData.specializedCourses ||
      !Array.isArray(formData.specializedCourses)
    ) {
      formData.specializedCourses = [];
    }

    if (!formData.estimatedScores || !Array.isArray(formData.estimatedScores)) {
      formData.estimatedScores = [];
    }

    if (!formData.targetProvinces || !Array.isArray(formData.targetProvinces)) {
      formData.targetProvinces = [];
    }

    // 处理目标专业ID - 如果ID为0但有专业代码，使用专业代码作为ID
    if (
      (formData.targetMajor === "0" || formData.targetMajor === 0) &&
      formData.majorCode
    ) {
      console.log(
        "提交前：目标专业ID为0，使用专业代码替代:",
        formData.majorCode
      );
      formData.targetMajor = formData.majorCode;
    }

    // 处理标签数据
    if (!formData.tags || !Array.isArray(formData.tags)) {
      formData.tags = [];
    } else {
      // 确保所有标签ID都是整数且大于0
      formData.tags = formData.tags
        .map((tagId) => parseInt(tagId, 10))
        .filter((tagId) => !isNaN(tagId) && tagId > 0);

      console.log("处理后的标签ID:", formData.tags);
    }

    // 将本科成绩单转换为JSON字符串
    formData.mathScoresJson = JSON.stringify(formData.mathScores);
    formData.specializedCoursesJson = JSON.stringify(
      formData.specializedCourses
    );

    // 设置学校和专业名称
    // 本科院校名称
    if (formData.undergraduateSchool) {
      const selectedSchool = schoolOptions.value.find(
        (item) => item.value === formData.undergraduateSchool
      );
      // 使用索引访问解决类型错误
      formData["undergraduateSchoolName"] = selectedSchool
        ? selectedSchool.label
        : "";
    }

    // 本科专业名称
    if (formData.undergraduateMajor) {
      const selectedMajor = majorOptions.value.find(
        (item) => item.value === formData.undergraduateMajor
      );
      // 使用索引访问解决类型错误
      formData["undergraduateMajorName"] = selectedMajor
        ? selectedMajor.label
        : "";
    }

    // 目标专业名称
    if (formData.targetMajor) {
      const selectedTargetMajor = targetMajorOptions.value.find(
        (item) => item.value === formData.targetMajor
      );
      // 使用索引访问解决类型错误
      formData["targetMajorName"] = selectedTargetMajor
        ? selectedTargetMajor.label
        : "";
    }

    // 梦校名称
    if (formData.targetSchool) {
      // 尝试从StudentBasic组件获取梦校名称
      const studentBasicComponent = document.querySelector("student-basic");
      if (studentBasicComponent && studentBasicComponent.__vueParentComponent) {
        const componentInstance =
          studentBasicComponent.__vueParentComponent.ctx;
        if (componentInstance && componentInstance.dreamSchoolOptions) {
          const selectedDreamSchool = componentInstance.dreamSchoolOptions.find(
            (item) => item.value === formData.targetSchool
          );
          // 使用索引访问解决类型错误
          formData["targetSchoolName"] = selectedDreamSchool
            ? selectedDreamSchool.label
            : "";
        }
      }
    }

    // 根据不同的操作类型调用不同的API
    let apiRequest;
    if (dialogType.value === "add") {
      apiRequest = addStudent(formData);
    } else if (dialogType.value === "edit") {
      apiRequest = editStudent(formData);
    } else {
      submitLoading.value = false;
      return;
    }

    // 发送请求
    apiRequest
      .then((res) => {
        if (res.code === 0) {
          ElMessage.success(
            dialogType.value === "add" ? "添加成功" : "更新成功"
          );
          dialogVisible.value = false;
          getList(); // 刷新列表
        } else {
          ElMessage.error(
            res.msg || (dialogType.value === "add" ? "添加失败" : "更新失败")
          );
        }
      })
      .catch((err) => {
        console.error(
          dialogType.value === "add" ? "添加失败" : "更新失败",
          err
        );
        ElMessage.error(
          dialogType.value === "add"
            ? "添加失败，请稍后重试"
            : "更新失败，请稍后重试"
        );
      })
      .finally(() => {
        submitLoading.value = false;
      });
  } catch (error) {
    console.error("表单验证失败", error);
    submitLoading.value = false;
  }
};

// 搜索本科院校 - 用于学生表单
const remoteSearchSchool = (query) => {
  if (!query) {
    schoolOptions.value = [];
    return;
  }

  schoolSearchLoading.value = true;
  console.log("搜索本科院校:", query);

  // 检查缓存中是否已有该查询结果
  if (schoolSearchCache[query]) {
    schoolOptions.value = schoolSearchCache[query];
    schoolSearchLoading.value = false;
    return;
  }

  // 延迟300ms，避免频繁请求
  clearTimeout(schoolSearchTimeout.value);
  schoolSearchTimeout.value = setTimeout(() => {
    searchSchool(query)
      .then((res) => {
        console.log("搜索本科院校结果:", res);
        if (res.code === 0 && res.data) {
          const options = res.data.map((item) => ({
            value: item.id.toString(),
            label: item.name,
          }));
          schoolOptions.value = options;
          // 添加到缓存
          schoolSearchCache[query] = options;
        } else {
          schoolOptions.value = [];
        }
      })
      .catch((err) => {
        console.error("获取学校列表失败", err);
        schoolOptions.value = [];
      })
      .finally(() => {
        schoolSearchLoading.value = false;
      });
  }, 300);
};

// 搜索本科院校 - 用于列表筛选
const remoteSearchSchoolForList = (query) => {
  if (!query) {
    schoolListOptions.value = [];
    return;
  }

  schoolListSearchLoading.value = true;
  console.log("列表筛选搜索本科院校:", query);

  // 检查缓存中是否已有该查询结果
  if (schoolListSearchCache[query]) {
    schoolListOptions.value = schoolListSearchCache[query];
    schoolListSearchLoading.value = false;
    return;
  }

  // 延迟300ms，避免频繁请求
  clearTimeout(schoolListSearchTimeout.value);
  schoolListSearchTimeout.value = setTimeout(() => {
    searchSchool(query)
      .then((res) => {
        console.log("列表筛选搜索本科院校结果:", res);
        if (res.code === 0 && res.data) {
          const options = res.data.map((item) => ({
            value: item.id.toString(),
            label: item.name,
          }));
          schoolListOptions.value = options;
          // 添加到缓存
          schoolListSearchCache[query] = options;
        } else {
          schoolListOptions.value = [];
        }
      })
      .catch((err) => {
        console.error("获取学校列表失败", err);
        schoolListOptions.value = [];
      })
      .finally(() => {
        schoolListSearchLoading.value = false;
      });
  }, 300);
};

// 本科专业分页相关状态
const majorCurrentPage = ref(1);
const majorHasMore = ref(false);
const majorIsInitialized = ref(false);

const handleMajorFocus = () => {
  // 检查是否已选择本科院校
  if (!searchForm.undergraduate) {
    ElMessage.warning("请先选择本科院校");
    return;
  }

  // 如果还未初始化或者选项为空，则加载第一页数据
  if (!majorIsInitialized.value || majorOptions.value.length === 0) {
    majorCurrentPage.value = 1;
    searchRemoteMajor(""); // 传入空字符串，加载该院校所有专业
  }
};

// 搜索本科专业
const searchRemoteMajor = (query) => {
  if (!searchForm.undergraduate) {
    ElMessage.warning("请先选择本科院校");
    return;
  }

  // 如果是新的搜索查询，重置分页状态
  if (query !== lastMajorQuery.value) {
    majorCurrentPage.value = 1;
    majorOptions.value = [];
    majorHasMore.value = false;
    lastMajorQuery.value = query;
  }

  majorSearchLoading.value = true;

  // 生成缓存键，包含学校ID、查询词和页码
  const cacheKey = `${searchForm.undergraduate}_${query || ""}_${
    majorCurrentPage.value
  }`;

  // 检查缓存中是否已有该查询结果
  if (majorSearchCache[cacheKey]) {
    const cachedResult = majorSearchCache[cacheKey];
    if (majorCurrentPage.value === 1) {
      majorOptions.value = cachedResult.data;
    } else {
      majorOptions.value = [...majorOptions.value, ...cachedResult.data];
    }
    majorHasMore.value = cachedResult.hasMore;
    majorSearchLoading.value = false;
    return;
  }

  // 延迟300ms，避免频繁请求
  clearTimeout(majorSearchTimeout.value);
  majorSearchTimeout.value = setTimeout(() => {
    searchMajor(searchForm.undergraduate, query || "", majorCurrentPage.value)
      .then((res) => {
        if (res.code === 0 && res.data) {
          const options = res.data.map((item) => ({
            value: item.id.toString(),
            label: item.major_name,
          }));

          // 处理分页数据
          if (majorCurrentPage.value === 1) {
            majorOptions.value = options;
          } else {
            majorOptions.value = [...majorOptions.value, ...options];
          }

          // 更新分页状态
          majorHasMore.value = res.pagination ? res.pagination.has_more : false;

          // 添加到缓存
          majorSearchCache[cacheKey] = {
            data: options,
            hasMore: majorHasMore.value,
          };
        } else {
          if (majorCurrentPage.value === 1) {
            majorOptions.value = [];
          }
          majorHasMore.value = false;
        }
      })
      .catch(() => {
        if (majorCurrentPage.value === 1) {
          majorOptions.value = [];
        }
        majorHasMore.value = false;
      })
      .finally(() => {
        majorSearchLoading.value = false;
        majorIsInitialized.value = true;
      });
  }, 300);
};

// 搜索本科专业 - 用于学生表单
const remoteSearchMajor = (query) => {
  if (!studentForm.undergraduate) {
    ElMessage.warning("请先选择本科院校");
    return;
  }

  majorSearchLoading.value = true;
  console.log("搜索本科专业:", studentForm.undergraduateSchool, query);

  // 生成缓存键，包含学校ID和查询词
  const cacheKey = `${studentForm.undergraduateSchool}_${query}`;

  // 检查缓存中是否已有该查询结果
  if (majorSearchCache[cacheKey]) {
    majorOptions.value = majorSearchCache[cacheKey];
    majorSearchLoading.value = false;
    return;
  }

  // 延迟300ms，避免频繁请求
  clearTimeout(majorSearchTimeout.value);
  majorSearchTimeout.value = setTimeout(() => {
    searchMajor(studentForm.undergraduateSchool, query || "")
      .then((res) => {
        console.log("搜索本科专业结果:", res);
        if (res.code === 0 && res.data) {
          const options = res.data.map((item) => ({
            value: item.id.toString(),
            label: item.major_name || item.name,
          }));
          majorOptions.value = options;
          // 添加到缓存
          majorSearchCache[cacheKey] = options;
        } else {
          majorOptions.value = [];
        }
      })
      .catch((err) => {
        console.error("获取专业列表失败", err);
        majorOptions.value = [];
      })
      .finally(() => {
        majorSearchLoading.value = false;
      });
  }, 300);
};

// 搜索目标专业 - 用于学生表单
const remoteSearchTargetMajor = (query) => {
  if (!query) {
    targetMajorOptions.value = [];
    return;
  }

  targetMajorSearchLoading.value = true;
  console.log("搜索目标专业:", query);

  // 检查缓存中是否已有该查询结果
  if (targetMajorSearchCache[query]) {
    targetMajorOptions.value = targetMajorSearchCache[query];
    targetMajorSearchLoading.value = false;
    return;
  }

  // 延迟300ms，避免频繁请求
  clearTimeout(targetMajorSearchTimeout.value);
  targetMajorSearchTimeout.value = setTimeout(() => {
    searchTargetMajor(query)
      .then((res) => {
        console.log("搜索目标专业结果:", res);
        if (res.code === 0 && res.data) {
          const options = res.data.map((item) => {
            // 如果没有id字段，则使用code作为id和value
            const id = item.id?.toString() || item.code;
            return {
              value: id,
              label: item.name,
              code: item.code,
              id: id, // 确保 id 属性存在
            };
          });
          targetMajorOptions.value = options;
          // 添加到缓存
          targetMajorSearchCache[query] = options;

          console.log("处理后的目标专业选项:", options);
        } else {
          targetMajorOptions.value = [];
        }
      })
      .catch((err) => {
        console.error("获取目标专业列表失败", err);
        targetMajorOptions.value = [];
      })
      .finally(() => {
        targetMajorSearchLoading.value = false;
      });
  }, 300);
};

// 搜索目标院校 - 用于列表筛选
const remoteSearchTargetSchoolForList = (query) => {
  if (!query) {
    targetSchoolListOptions.value = [];
    return;
  }

  targetSchoolListSearchLoading.value = true;
  console.log("列表筛选搜索目标院校:", query);

  // 检查缓存中是否已有该查询结果
  if (targetSchoolListSearchCache[query]) {
    targetSchoolListOptions.value = targetSchoolListSearchCache[query];
    targetSchoolListSearchLoading.value = false;
    return;
  }

  // 延迟300ms，避免频繁请求
  clearTimeout(targetSchoolListSearchTimeout.value);
  targetSchoolListSearchTimeout.value = setTimeout(() => {
    searchSchool(query)
      .then((res) => {
        console.log("列表筛选搜索目标院校结果:", res);
        if (res.code === 0 && res.data) {
          const options = res.data.map((item) => ({
            value: item.id.toString(),
            label: item.name,
          }));
          targetSchoolListOptions.value = options;
          // 添加到缓存
          targetSchoolListSearchCache[query] = options;
        } else {
          targetSchoolListOptions.value = [];
        }
      })
      .catch((err) => {
        console.error("获取目标院校列表失败", err);
        targetSchoolListOptions.value = [];
      })
      .finally(() => {
        targetSchoolListSearchLoading.value = false;
      });
  }, 300);
};

// 搜索目标专业 - 用于列表筛选
const remoteSearchTargetMajorForList = (query) => {
  if (!query) {
    targetMajorListOptions.value = [];
    return;
  }

  targetMajorListSearchLoading.value = true;
  console.log("列表筛选搜索目标专业:", query);

  // 检查缓存中是否已有该查询结果
  if (targetMajorListSearchCache[query]) {
    targetMajorListOptions.value = targetMajorListSearchCache[query];
    targetMajorListSearchLoading.value = false;
    return;
  }

  // 延迟300ms，避免频繁请求
  clearTimeout(targetMajorListSearchTimeout.value);
  targetMajorListSearchTimeout.value = setTimeout(() => {
    searchTargetMajor(query)
      .then((res) => {
        console.log("列表筛选搜索目标专业结果:", res);
        if (res.code === 0 && res.data) {
          const options = res.data.map((item) => {
            // 如果没有id字段，则使用code作为id和value
            const id = item.id?.toString() || item.code;
            return {
              value: id,
              label: item.name,
              code: item.code,
              id: id, // 确保 id 属性存在
            };
          });
          targetMajorListOptions.value = options;
          // 添加到缓存
          targetMajorListSearchCache[query] = options;
        } else {
          targetMajorListOptions.value = [];
        }
      })
      .catch((err) => {
        console.error("获取目标专业列表失败", err);
        targetMajorListOptions.value = [];
      })
      .finally(() => {
        targetMajorListSearchLoading.value = false;
      });
  }, 300);
};

const loadMoreMajors = () => {
  if (majorHasMore.value && !majorSearchLoading.value) {
    majorCurrentPage.value += 1;
    searchRemoteMajor(lastMajorQuery.value);
  }
};

// 监听本科院校变更
const handleSchoolChange = () => {
  // 清空专业选择
  studentForm.undergraduateMajor = "";
  // 清空专业选项
  majorOptions.value = [];

  // 如果选择了本科院校，可以尝试加载该院校的专业列表
  if (studentForm.undergraduateSchool) {
    remoteSearchMajor("");
  }
};

// 重置搜索条件
const resetSearch = () => {
  // 重置搜索表单
  Object.keys(searchForm).forEach((key) => {
    if (key === "tagId") {
      searchForm[key] = []; // 标签ID重置为空数组
    } else {
      searchForm[key] = "";
    }
  });
  // 重置分页
  currentPage.value = 1;
  // 重新获取数据
  getList();
};

// 分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  getList();
};

// 页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  getList();
};
</script>

<style scoped>
.student-list-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 16px 16px 0px 0px;
}

/* 输入框样式 */
:deep(.el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-select__wrapper) {
  border-radius: 8px;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
}

/* 筛选区样式 */
.filter-section {
  border-radius: 8px;
  padding: 0 20px;
}

.filter-header {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.filter-icon {
  background-image: url("@/assets/images/tag.png");
  height: 20px;
  width: 26px;
  background-size: contain;
  margin-right: 4px;
}

.filter-title {
  font-size: 16px;
  height: 20px;
  line-height: 20px;
  font-weight: bold;
  color: #333;
}

.filter-form-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.filter-form {
  flex: 1;
}

.form-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 20px;
  margin-top: 20px;
}

.form-item {
  width: 300px;
  margin-right: 20px;
  display: flex;
  align-items: center;
}

.form-item:last-child {
  margin-right: 0;
}

.item-label {
  width: 70px;
  text-align: right;
  margin-right: 10px;
  color: #333;
  white-space: nowrap;
}

/* 确保select和input占满容器宽度 */
.form-item :deep(.el-select),
.form-item :deep(.el-input),
.form-item :deep(.el-cascader) {
  width: 100%;
}

.filter-buttons {
  text-align: center;
  margin-left: 70px;
}

.filter-button {
  width: 96px;
  margin: 0 10px;
  border-radius: 8px;
}

.filter-button.el-button--primary {
  background-color: #1bb394;
  border-color: #1bb394;
}

/* 学员列表样式 */
.student-list-section {
  min-height: calc(100vh - (54px + 184px + 20px));
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  padding-top: 10px;
}

.list-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  justify-content: space-between;

  .list-header-content {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }
}

.list-icon {
  display: inline-block;
  background-image: url("@/assets/images/tag.png");
  height: 20px;
  width: 26px;
  background-size: contain;
  margin-right: 4px;
}

.list-title {
  font-size: 16px;
  height: 20px;
  line-height: 20px;
  font-weight: bold;
  color: #333;
  flex: 1;
  margin-right: 10px;
}

.add-student-button,
.change-teacher-button {
  background-color: #1bb394;
  border-color: #1bb394;
  padding: 0 15px;
  border-radius: 14px;
}

.add-student-button {
  width: 120px;
  border-radius: 10px;
}

/* 表格标签样式 */
.tag-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 5px;
}

.el-tag {
  margin: 2px;
  font-size: 12px;
  border: none;
}

.tag-orange {
  background-color: #ff9900;
  color: white;
}

.tag-purple {
  background-color: #8e6df8;
  color: white;
}

.tag-green {
  background-color: #1bb394;
  color: white;
}

.tag-blue {
  background-color: #3d8af7;
  color: white;
}

.tag-pink {
  background-color: #ee7a8f;
  color: white;
}

/* 表格样式 */
.custom-table {
  border-radius: 5px;
  overflow: hidden;
  margin-bottom: 20px;
}

:deep(.el-table th) {
  background-color: #f6f7fb;
  font-weight: bold;
  font-size: 13px;
  color: #313131;
  height: 60px;
  border: none;
}

:deep(.el-table td) {
  height: 52px;
  background: #ffffff;
  border: none;
  font-weight: 400;
  font-size: 13px;
  color: #313131;
  border-bottom: 1px solid #ececec;
}

:deep(.el-table--border) {
  border: none;
  overflow: hidden;
}

:deep(.el-table) {
  border: none;
}

.edit-button,
.evaluate-button {
  padding: 4px 8px;
  color: #1bb394;
  cursor: pointer;
}

/* 分页样式 */
.pagination-container {
  margin-top: 20px;
  text-align: right;
}

:deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
  background-color: #1bb394;
}

/* 输入框统一样式 */
:deep(.el-input__wrapper),
:deep(.el-select .el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
}

:deep(.el-input__wrapper.is-focus),
:deep(.el-select .el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #1bb394 inset !important;
}

:deep(.el-button--primary) {
  background-color: #1bb394;
  border-color: #1bb394;
}

:deep(.el-button--primary:hover) {
  background-color: #19a588;
  border-color: #19a588;
}

/* 下拉菜单样式 */
:deep(.el-select-dropdown__item.selected) {
  color: #1bb394;
  font-weight: bold;
}

:deep(.el-select-dropdown__item:hover) {
  background-color: #e6f7f1;
}

/* 多选框样式 */
:deep(.el-select .el-tag) {
  background-color: #e6f7f1;
  border-color: #1bb394;
  color: #1bb394;
}

/* select下拉箭头颜色 */
:deep(.el-select .el-input__suffix) {
  color: #1bb394;
}

/* 学员表单对话框样式 */
.student-dialog {
  --el-dialog-padding-primary: 0;
}

.dialog-container {
  display: flex;
  height: 75vh;
}

.dialog-sidebar {
  width: 200px;
  background-color: #f5f7fa;
  border-right: 1px solid #e4e7ed;
  padding: 20px 0;
  overflow-y: auto;
}

.sidebar-item {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
}

.sidebar-item:hover {
  background-color: #e6f7f1;
}

.sidebar-item.active {
  background-color: #e6f7f1;
  color: #1bb394;
  font-weight: bold;
}

.sidebar-item.active::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background-color: #1bb394;
}

.sidebar-item-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #1bb394;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  font-size: 14px;
}

.sidebar-item.active .sidebar-item-number {
  background-color: #19a588;
}

.sidebar-item-text {
  font-size: 14px;
}

.dialog-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

/* 学员表单样式 */
.student-form {
  padding: 0 10px;
}

.form-section {
  margin-bottom: 30px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  overflow: hidden;
}

.section-header {
  background-color: #dcf7f0;
  padding: 10px 15px;
  border-bottom: 1px solid #ebeef5;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.section-content {
  padding: 20px;
  background-color: #fff;
  position: relative;
}

.subsection-title {
  font-size: 14px;
  font-weight: bold;
  color: #1bb394;
  margin: 10px 0;
  padding-bottom: 5px;
  border-bottom: 1px dashed #e0e0e0;
}

/* 表单网格布局 */
.form-section .el-row {
  margin-bottom: 20px;
}

.form-section .el-form-item {
  margin-bottom: 15px;
}

/* 按钮样式 */
.action-button {
  width: 104px;
  height: 38px;
  background: #1bb394;
  border-radius: 8px;
  border: 1px solid #1bb394;
}

/* 表单样式覆盖 */
.form-section :deep(.el-input__wrapper) {
  border-color: #1bb394 !important;
  border-radius: 8px;
  box-shadow: 0 0 0 1px #1bb394 inset;
}

.form-section :deep(.el-select__wrapper) {
  border-color: #1bb394 !important;
  border-radius: 8px;
  box-shadow: 0 0 0 1px #1bb394 inset;
}

.form-section :deep(.el-select__wrapper.is-focus) {
  border-color: #1bb394 !important;
  box-shadow: 0 0 0 1px #1bb394 inset !important;
}

.form-section :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #1bb394 inset !important;
}

.form-section :deep(.el-select .el-input__wrapper) {
  border-color: #1bb394 !important;
  border-radius: 8px;
  box-shadow: 0 0 0 1px #1bb394 inset;
}

/* 下拉菜单样式 */
.form-section :deep(.el-select-dropdown__item.selected) {
  color: #1bb394;
  font-weight: bold;
}

.form-section :deep(.el-select-dropdown__item:hover) {
  background-color: #e6f7f1;
}

/* 多选框样式 */
.form-section :deep(.el-select .el-tag) {
  background-color: #e6f7f1;
  border-color: #1bb394;
  color: #1bb394;
}

/* select下拉箭头颜色 */
.form-section :deep(.el-select .el-input__suffix) {
  color: #1bb394;
}

/* 文本域样式 */
.form-section :deep(.el-textarea__inner) {
  border-color: #1bb394 !important;
  border-radius: 8px;
  box-shadow: 0 0 0 1px #1bb394 inset !important;
}

.form-section :deep(.el-textarea__inner:focus) {
  box-shadow: 0 0 0 1px #1bb394 inset !important;
}

.tag {
  font-weight: 400;
  font-size: 10px;
  color: #ffffff;
  display: inline-block;
  width: 60px;
  height: 20px;
  border-radius: 4px 4px 4px 4px;
  line-height: 20px;
  padding: 0 6px;
}
</style>
