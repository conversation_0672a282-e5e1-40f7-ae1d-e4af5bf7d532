package main

import (
	"go-api/config"
	"go-api/db"
	"go-api/routers"
	"log"
	"os"
	"strconv"

	"github.com/gin-gonic/gin"
)

func main() {
	configPath := "config/config.yaml"

	err := config.InitConfig(configPath)
	if err != nil {
		log.Fatalln(err.Error())
		os.Exit(-1)
	}
	if config.GlobalConfig.Server.Mode == "release" {
		gin.SetMode(gin.ReleaseMode)
	}
	db.Init()
	r := gin.Default()

	routers.InitRouter(r)
	r.Run(":" + strconv.Itoa(config.GlobalConfig.Server.Port))
}
