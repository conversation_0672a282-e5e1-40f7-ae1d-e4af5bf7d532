<?php
namespace app\controller;

use app\model\School;
use app\model\SchoolMajor;
use app\model\Major;
use app\model\ReportInfo;
use app\model\SchoolReport;
use support\Request;
use support\Log;
use think\facade\Db;

class ReportController
{
    /**
     * 检查爬虫登录状态
     * @param Request $request
     * @return \support\Response
     */
    public function checkSpiderLogin(Request $request)
    {
        // 获取配置的URL
        $url = config('app.check_spider_login_url');

        // 使用封装的http_get函数发起GET请求
        try {
            // 发起GET请求
            $response = http_get($url);

            // 检查请求是否成功
            if ($response === false) {
                return json([
                    'code' => 1,
                    'msg' => '请求失败',
                    'data' => null
                ]);
            }

            // 尝试解析JSON响应
            $data = json_decode($response, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return json([
                    'code' => 0,
                    'msg' => '成功',
                    'data' => ['raw_response' => $response]
                ]);
            }

            return json([
                'code' => 0,
                'msg' => '成功',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'msg' => '请求异常: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取报告列表
     * @param Request $request
     * @return \support\Response
     */
    public function reportList(Request $request)
    {
        $params = $request->all();
        $name = $params['name'] ?? '';
        $majorName = $params['major_name']?? '';
        $schoolName = $params['school_name']?? '';
        $yclass = $params['class']?? '';
        $limit = $params['limit']?? 10;
        $admin_id = $request->user->uid ?? 0;
        $schoolInfo  =  Db::name('school_report')->alias('sr')
        ->leftJoin('student s','s.id = sr.student_id')            
        ->field('sr.idcode,sr.student_id,sr.undergraduate_school_name as undergraduate,s.name,sr.id,s.exam_year as year,sr.undergraduate_major_name as major')
        ->when(isset($name) && !empty($name), function ($query) use ($name) {
            $query->where('s.name',$name);
        })->when(isset($majorName) && !empty($majorName), function ($query) use ($majorName) {
            $query->where('sr.target_major', $majorName);
        })->when(isset($schoolName) && !empty($schoolName), function ($query) use ($schoolName) {
            $query->where('sr.undergraduate_school_name', $schoolName);
        })->when(isset($yclass) && !empty($yclass), function ($query) use ($yclass) {
            $query->where('s.exam_year',$yclass);
        })->where('s.admin_id',$admin_id)
        ->order('id desc')
        ->paginate($limit);

        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => $schoolInfo
        ]);
    }

    /**
     * 搜索专业
     * @param Request $request
     * @return \support\Response
     */
    public function searchSchoolMajor(Request $request)
    {
        $schoolId = $request->get('school_id', 0);
        $majorName = $request->get('major_name', '');

        // 验证学校ID
        if (empty($schoolId)) {
            return json([
                'code' => 400,
                'msg' => '请选择学校',
                'data' => []
            ]);
        }

        // 构建查询条件
        $query = SchoolMajor::where('school_id', $schoolId)
            ->where('is_delete', 0)
            ->where('type', 0); // 只查询专业，不查询门类

        // 如果有专业名称，添加模糊搜索条件
        if (!empty($majorName)) {
            $query = $query->where('major_name', 'like', "%{$majorName}%");
        }

        // 获取数据
        $majors = $query->field([
            'id',
            'major_name',
        ])->select();

        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => $majors
        ]);
    }

    //新建一个方法 getReportCount 统计所有用户生成报告的次数,并按照json格式返回
    public function getReportCount(Request $request)
    {
        $count = Db::name('school_report')->count();
        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => ['count' => $count]
        ]);
    }

    /**
     * 搜索二级学科
     * @param Request $request
     * @return \support\Response
     */
    public function searchMajor(Request $request)
    {
        $keyword = $request->get('keyword', '');

        if (empty($keyword)) {
            return json([
                'code' => 400,
                'msg' => '请输入搜索关键词',
                'data' => []
            ]);
        }

        $result = Major::where('erji_name', 'like', "%{$keyword}%")
            ->field([
                'erji_name as name',
                'major_code as code'
            ])
            ->select();

        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => $result
        ]);
    }

    /**
     * 获取考研年份列表
     * @return \support\Response
     */
    public function getExamYears()
    {
        // 从配置文件中获取考研年份列表
        $years = config('exam_years.years', []);

        // 转换为前端需要的格式
        $result = [];
        foreach ($years as $key => $value) {
            $result[] = [
                'value' => $key,
                'label' => $value
            ];
        }

        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => $result
        ]);
    }

    /**
     * 获取报告推荐信息
     * @param Request $request
     * @return \support\Response
     */
    public function getReportInfo(Request $request)
    {
        try {
            $reportId = $request->get('report_id');

            if (empty($reportId)) {
                return json([
                    'code' => 400,
                    'msg' => '报告ID不能为空',
                    'data' => null
                ]);
            }

            // 获取ba_report_info表中的记录
            $reportInfoList = ReportInfo::where('report_id', $reportId)
                ->where('status', 1)
                ->field(['id', 'school_id', 'school_name', 'competition_difficulty', 'suggestions'])
                ->select()
                ->toArray();

            return json([
                'code' => 0,
                'msg' => '获取成功',
                'data' => $reportInfoList
            ]);

        } catch (\Exception $e) {
            Log::error('获取报告推荐信息失败', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            return json([
                'code' => 500,
                'msg' => '获取失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 保存报告信息
     * @param Request $request
     * @return \support\Response
     */
    public function saveReport(Request $request)
    {
        try {
            $data = $request->all();

            // 验证必要参数
            if (empty($data['report_id'])) {
                return json([
                    'code' => 400,
                    'msg' => '报告ID不能为空',
                    'data' => null
                ]);
            }

            if (empty($data['recommendations']) || !is_array($data['recommendations'])) {
                return json([
                    'code' => 400,
                    'msg' => '推荐数据不能为空',
                    'data' => null
                ]);
            }

            $reportId = $data['report_id'];
            $recommendations = $data['recommendations'];
            $pdfUrl = $data['pdf_url'] ?? '';

            Log::info('保存报告信息', [
                'report_id' => $reportId,
                'recommendations_count' => count($recommendations),
                'pdf_url' => $pdfUrl
            ]);

            // 开始事务
            Db::startTrans();

            // 更新ba_report_info表中的记录
            foreach ($recommendations as $recommendation) {
                if (empty($recommendation['school_id'])) {
                    continue;
                }

                $updateData = [
                    'competition_difficulty' => $recommendation['competition_difficulty'] ?? '',
                    'suggestions' => $recommendation['suggestions'] ?? '',
                    'updatetime' => time()
                ];

                // 如果有PDF URL，添加到更新数据中
                if (!empty($pdfUrl)) {
                    $updateData['pdf_url'] = $pdfUrl;
                }

                // 使用 report_id 和 school_id 来定位记录
                ReportInfo::where('report_id', $reportId)
                    ->where('school_id', $recommendation['school_id'])
                    ->update($updateData);
            }

            // 提交事务
            Db::commit();

            return json([
                'code' => 0,
                'msg' => '保存成功',
                'data' => [
                    'report_id' => $reportId,
                    'updated_count' => count($recommendations)
                ]
            ]);

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();

            Log::error('保存报告信息失败', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            return json([
                'code' => 500,
                'msg' => '保存失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 根据报告ID获取学员基本信息
     * @param Request $request
     * @return \support\Response
     */
    public function getReportBasicInfo(Request $request)
    {
        try {
            $reportId = $request->get('report_id');
            if (empty($reportId)) {
                return json([
                    'code' => 400,
                    'msg' => '报告ID不能为空',
                    'data' => null
                ]);
            }

            // 1. 根据report_id获取报告信息
            $report = \app\model\SchoolReport::where('id', $reportId)->find();
            if (!$report) {
                return json([
                    'code' => 404,
                    'msg' => '未找到报告信息',
                    'data' => null
                ]);
            }

            // 2. 根据student_id获取学员详细信息
            $studentModel = new \app\model\Student();
            $studentDetail = $studentModel->getDetail($report->student_id);

            if ($studentDetail['code'] !== 0) {
                return json([
                    'code' => 404,
                    'msg' => '未找到学员信息',
                    'data' => null
                ]);
            }

            // 3. 整合报告和学员信息
            $result = [
                'report_id' => $reportId,
                'student_info' => $studentDetail['data'],
                'report_info' => [
                    'target_major' => $report->target_major,
                    'major_code' => $report->major_code,
                    'dream_school' => $report->dream_school,
                    'region_preference' => $report->target_region,
                    'province_selection' => $report->target_provinces,
                    'school_level' => $report->school_level,
                    'reference_books' => $report->reference_books,
                    'politics_score' => $report->politics_score,
                    'english_score' => $report->english_score,
                    'english_type' => $report->english_type,
                    'math_score' => $report->math_score,
                    'math_type' => $report->math_type,
                    'professional_score' => $report->professional_score,
                    'total_score' => $report->total_score,
                    'personal_needs' => $report->personal_needs,
                    'weak_modules' => $report->weak_modules,
                    'educational_style' => $report->educational_style,
                    'undergraduate_major_name' => $report->undergraduate_major_name,
                    'undergraduate_school_name' => $report->undergraduate_school_name,
                ]
            ];

            return json([
                'code' => 0,
                'msg' => '获取成功',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            \support\Log::error('获取报告基本信息异常: ' . $e->getMessage());
            return json([
                'code' => 500,
                'msg' => '获取报告基本信息失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    // 获取报告的学校信息
    public function getReportDetail(Request $request)
    {
        $reportId = $request->get('report_id');
        if (empty($reportId)) {
            return json(['code' => 400, 'msg' => '报告ID不能为空', 'data' => []]);
        }
        $fieldStr  = 'sr.target_major_code,
        sr.target_major,
        sr.idcode,
        sr.id as report_id,
        si.province_city,
        ri.is_high_recommend, 
        sr.undergraduate_major_name, 
        si.school_name, 
        si.province, 
        si.area, 
        si.school_type, 
        ri.competition_difficulty,
        ri.suggestions,
        ri.reason_recommendation, 
        ri.school_id,
        ri.score_formula,
        ri.study_years,
        ri.tuition_fee,
        ri.exam_subjects,
        ri.reference_books,
        ri.retest_score_requirement,
        ri.retest_content,
        ri.admission_requirements';
        $tmpReportDetails = Db::name('school_report')->alias('sr')
            ->join('report_info ri', 'sr.id = ri.report_id')
            ->join('school_info si', 'ri.school_id = si.id')
            ->where('sr.id', $reportId)
            ->field($fieldStr)
            ->select();

        if ($tmpReportDetails->isEmpty()) {
            return json(['code' => 404, 'msg' => '未找到报告详情', 'data' => []]);
        }
        if(empty($tmpReportDetails[0]['idcode'])) {
            $tmpIdcode = 'zx'.mt_rand(10000.,99999).time();
            Db::name('school_report')->where('id',$reportId)->update(['idcode'=>$tmpIdcode]);
        }
        $recommendData  =  [];
        $highRecommendData = [];
        $reportDetails = [];
        if ($tmpReportDetails && method_exists($tmpReportDetails, 'toArray')) {
            $reportDetails = $tmpReportDetails->toArray();
        } elseif (is_array($tmpReportDetails)) {
            $reportDetails = $tmpReportDetails;
        }
        foreach ($reportDetails as $key => $value) {
            if(!$value['is_high_recommend']) {
                array_push($recommendData,
                [
                    "school_name" => $value['school_name'],
                    "school_id" => $value['school_id'],
                    "major_name" => $value['undergraduate_major_name'],
                    "difficulty_analysis" =>$value['competition_difficulty'],
                    "suggest" => $value['suggestions'],
                    "reason" => $value['reason_recommendation'],
                    "province_city" => $value['province_city'],
                    "target_major_code" => $value['target_major_code'],
                    'score_formula' => $value['score_formula'],
                    'study_years' => $value['study_years'],
                    'tuition_fee' => $value['tuition_fee'],
                    'exam_subjects' => $value['exam_subjects'],
                    'reference_books' => $value['reference_books'],
                    'retest_score_requirement' => $value['retest_score_requirement'],
                    'retest_content' => $value['retest_content'],
                    'admission_requirement' => $value['admission_requirements'],
                ]);
            } else  {
                $highRecommendData =  [
                    "school_name" => $value['school_name'],
                    "school_id" => $value['school_id'],
                    "major_name" =>  $value['undergraduate_major_name'],
                    "reason" => $value['reason_recommendation'],
                    "target_major_code" => $value['target_major_code'],
                    'score_formula' => $value['score_formula'],
                    'study_years' => $value['study_years'],
                    'tuition_fee' => $value['tuition_fee'],
                    'exam_subjects' => $value['exam_subjects'],
                    'reference_books' => $value['reference_books'],
                    'retest_score_requirement' => $value['retest_score_requirement'],
                    'retest_content' => $value['retest_content'],
                    'admission_requirement' => $value['admission_requirements'],
                ];
            }

        }
        foreach($recommendData as $index => &$school){
            //添加院校 id
            $school = $school + (new ReportInfo())->enrichSchoolData($school);

        }
        unset($school); // 解除引用
        if(!empty($highRecommendData)) {
            $highRecommendData  = $highRecommendData + (new ReportInfo())->enrichSchoolData($highRecommendData);
        }



        $recommendedSchools = [
            'recommend_list' =>$recommendData,
            'high_recommend_list' => $highRecommendData
        ];
        $schoolListData = (new ReportInfo())->extractSchoolListFromRecommendations($recommendedSchools, $reportId);


        return json(['code' => 0, 'msg' => 'success',  'data' => array_merge($recommendedSchools, ['school_list' => $schoolListData])]);
    }
    /**
     * 更新报告的PDF URL
     * @param Request $request
     * @return \support\Response
     */
    public function updatePdfUrl(Request $request)
    {
        try {
            $data = $request->all();

            // 验证必要参数
            if (empty($data['report_id'])) {
                return json([
                    'code' => 400,
                    'msg' => '报告ID不能为空',
                    'data' => null
                ]);
            }

            if (empty($data['pdf_url'])) {
                return json([
                    'code' => 400,
                    'msg' => 'PDF URL不能为空',
                    'data' => null
                ]);
            }

            $reportId = $data['report_id'];
            $pdfUrl = $data['pdf_url'];

            Log::info('更新报告PDF URL', [
                'report_id' => $reportId,
                'pdf_url' => $pdfUrl
            ]);

            // 开始事务
            Db::startTrans();

            // 更新ba_school_report表中的pdf_url字段
            $updateResult = SchoolReport::where('id', $reportId)
                ->update([
                    'pdf_url' => $pdfUrl,
                    'update_time' => time()
                ]);

            if ($updateResult === false) {
                throw new \Exception('更新PDF URL失败');
            }

            // 提交事务
            Db::commit();

            return json([
                'code' => 0,
                'msg' => '更新成功',
                'data' => [
                    'report_id' => $reportId,
                    'pdf_url' => $pdfUrl
                ]
            ]);

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();

            Log::error('更新报告PDF URL失败', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            return json([
                'code' => 500,
                'msg' => '更新失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    public function generateIdCode($request)
    {
        $reportId = $request->post('report_id');
        $report = \app\model\SchoolReport::where('id', $reportId)->find();
        if(!empty($report)) {
            if(empty($report['idcode'])) {
                $tmpIdcode = 'zx'.mt_rand(10000.,99999).time();
                Db::name('school_report')->where('id',$reportId)->update(['idcode'=>$tmpIdcode]);
                $report['idcode'] =  $tmpIdcode;
            }

            return json([
                'code' => 0,
                'msg' => '获取成功',
                'data' => $report['idcode']
            ]);
        } else  {
            return json([
                'code' => 500,
                'msg' => '获取失败请稍后重试',
                'data' => null
            ]);
        }
    }

}