<?php

namespace app\model;

use think\Model;

/**
 * 薄弱模块分析模型
 */
class WeakModuleAnalysis extends Model
{
    protected $name = 'weak_module_analysis';
    
    // 设置字段信息
    protected $schema = [
        'id'              => 'int',
        'report_id'       => 'int',
        'subject'         => 'string',
        'problem_analysis' => 'text',
        'solutions'       => 'text',
        'created_at'      => 'datetime',
        'updated_at'      => 'datetime',
    ];
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    /**
     * 根据报告ID删除薄弱模块分析数据
     *
     * @param int $reportId 报告ID
     * @return bool
     */
    public static function deleteByReportId($reportId)
    {
        return self::where('report_id', $reportId)->delete();
    }

    /**
     * 批量插入薄弱模块分析数据
     *
     * @param int $reportId 报告ID
     * @param array $weakModules 薄弱模块数据
     * @return bool
     */
    public static function insertBatch($reportId, $weakModules)
    {
        if (empty($weakModules) || !is_array($weakModules)) {
            return true; // 如果没有数据，返回成功
        }

        $data = [];
        foreach ($weakModules as $module) {
            $data[] = [
                'report_id' => $reportId,
                'subject' => $module['subject'] ?? '',
                'problem_analysis' => $module['problemAnalysis'] ?? '',
                'solutions' => $module['solutions'] ?? '',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ];
        }

        return self::insertAll($data);
    }

    /**
     * 根据报告ID获取薄弱模块分析数据
     *
     * @param int $reportId 报告ID
     * @return array
     */
    public static function getByReportId($reportId)
    {
        return self::where('report_id', $reportId)
            ->order('id', 'asc')
            ->select()
            ->toArray();
    }
}
