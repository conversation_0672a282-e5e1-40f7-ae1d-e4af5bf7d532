<?php
namespace app\model;

use think\Model;

class StudentTag extends Model
{
    protected $table = 'ba_student_tag';
    protected $pk = 'id';
    protected $autoWriteTimestamp = false;

    /**
     * 学生关联
     * @return \think\model\relation\BelongsTo
     */
    public function student()
    {
        return $this->belongsTo(Student::class, 'student_id', 'id');
    }

    /**
     * 标签关联
     * @return \think\model\relation\BelongsTo
     */
    public function tag()
    {
        return $this->belongsTo(Tag::class, 'tag_id', 'id');
    }
}
