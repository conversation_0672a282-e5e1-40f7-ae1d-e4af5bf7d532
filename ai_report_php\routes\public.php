<?php
use Webman\Route;

// 公开接口 - 无需鉴权
Route::get('/api/school/score', [app\controller\SchoolController::class, 'getSchoolsByScore']);
Route::get('/api/school/national-line', [app\controller\SchoolController::class, 'getNationalLineData']);
Route::get('/api/get_user_report', [app\controller\IndexController::class, 'getUserReport']);
Route::get('/api/remote/get_target_scores', [app\controller\RemoteController::class, 'getTargetScores']);
Route::post('/api/remote/get_study_plan_from_database_edit', [app\controller\RemoteController::class, 'getStudyPlanFromDatabase']);
