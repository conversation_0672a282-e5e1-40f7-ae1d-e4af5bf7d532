<?php

namespace app\model;

use think\Model;
use support\Log;

/**
 * 学习模块模型
 */
class StudyModules extends Model
{
    protected $name = 'study_modules';
    
    // 设置字段信息
    protected $schema = [
        'id'              => 'int',
        'report_id'       => 'int',
        'stage_id'        => 'int',
        'module_id'       => 'string',
        'name'            => 'string',
        'study_content'   => 'text',
        'study_method'    => 'text',
        'study_materials' => 'text',
        'study_reminder'  => 'text',
        'sort_order'      => 'int',
        'created_at'      => 'datetime',
        'updated_at'      => 'datetime',
    ];
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    // 自动时间戳
    protected $autoWriteTimestamp = true;

    /**
     * 根据报告ID删除学习模块数据
     *
     * @param int $reportId 报告ID
     * @return bool
     */
    public static function deleteByReportId($reportId)
    {
        return self::where('report_id', $reportId)->delete();
    }

    /**
     * 批量插入学习模块数据
     *
     * @param int $reportId 报告ID
     * @param array $stages 学习阶段数据（包含模块）
     * @param array $stageIdMap 阶段ID映射关系
     * @return bool
     */
    public static function insertBatch($reportId, $stages, $stageIdMap)
    {
        Log::info("StudyModules::insertBatch 开始执行，reportId: {$reportId}");
        Log::info("stages数量: " . count($stages));
        Log::info("stageIdMap: " . json_encode($stageIdMap));

        if (empty($stages) || !is_array($stages) || empty($stageIdMap)) {
            Log::warning("StudyModules::insertBatch 参数检查失败");
            return true;
        }

        $data = [];

        foreach ($stages as $stage) {
            $originalStageId = $stage['id'] ?? '';
            $dbStageId = $stageIdMap[$originalStageId] ?? 0;

            Log::info("处理阶段: {$originalStageId}, 数据库ID: {$dbStageId}");

            if ($dbStageId === 0) {
                Log::warning("找不到阶段ID映射，跳过阶段: {$originalStageId}");
                continue; // 如果找不到对应的阶段ID，跳过
            }

            $modules = $stage['modules'] ?? [];
            Log::info("阶段 {$originalStageId} 包含 " . count($modules) . " 个模块");

            foreach ($modules as $index => $module) {
                $data[] = [
                    'report_id' => $reportId,
                    'stage_id' => $dbStageId,
                    'module_id' => $module['id'] ?? '',
                    'name' => $module['name'] ?? '',
                    'study_content' => $module['studyContent'] ?? '',
                    'study_method' => $module['studyMethod'] ?? '',
                    'study_materials' => $module['studyMaterials'] ?? '',
                    'study_reminder' => $module['studyReminder'] ?? '',
                    'sort_order' => $index + 1,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ];
            }
        }

        if (empty($data)) {
            Log::warning("StudyModules::insertBatch 没有数据需要插入");
            return true;
        }

        Log::info("StudyModules::insertBatch 准备插入 " . count($data) . " 条模块数据");
        $result = self::insertAll($data);
        Log::info("StudyModules::insertBatch 插入结果: " . ($result ? '成功' : '失败'));

        return $result;
    }

    /**
     * 根据报告ID获取学习模块数据
     *
     * @param int $reportId 报告ID
     * @return array
     */
    public static function getByReportId($reportId)
    {
        return self::alias('sm')
            ->join('study_stages ss', 'sm.stage_id = ss.id')
            ->where('sm.report_id', $reportId)
            ->field('sm.*, ss.title as stage_title, ss.sort_order as stage_sort')
            ->order('ss.sort_order, sm.sort_order')
            ->select()
            ->toArray();
    }
}
