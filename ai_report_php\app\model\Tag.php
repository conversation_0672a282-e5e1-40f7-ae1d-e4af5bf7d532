<?php
namespace app\model;

use support\think\Model;

class Tag extends Model
{
    protected $table = 'ba_tags';
    protected $primaryKey = 'id';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    
    // 定义字段类型
    protected $type = [
        'id'          => 'integer',
        'level'       => 'integer',
        'parent_id'   => 'integer',
        'sort'        => 'integer',
        'create_time' => 'integer',
        'update_time' => 'integer',
        'is_delete'   => 'integer',
    ];
    
    /**
     * 获取一级标签列表
     * @return \think\Collection
     */
    public static function getFirstLevelTags()
    {
        return self::where('level', 1)
            ->where('is_delete', 0)
            ->order('sort', 'desc')
            ->order('id', 'asc')
            ->select();
    }
    
    /**
     * 获取二级标签列表
     * @param int $parentId 父级标签ID
     * @return \think\Collection
     */
    public static function getSecondLevelTags($parentId = 0)
    {
        $query = self::where('level', 2)
            ->where('is_delete', 0)
            ->order('sort', 'desc')
            ->order('id', 'asc');
            
        if ($parentId > 0) {
            $query->where('parent_id', $parentId);
        }
        
        return $query->select();
    }
    
    /**
     * 获取三级标签列表
     * @param int $parentId 父级标签ID
     * @return \think\Collection
     */
    public static function getThirdLevelTags($parentId = 0)
    {
        $query = self::where('level', 3)
            ->where('is_delete', 0)
            ->order('sort', 'desc')
            ->order('id', 'asc');
            
        if ($parentId > 0) {
            $query->where('parent_id', $parentId);
        }
        
        return $query->select();
    }
    
    /**
     * 软删除标签
     * @return bool
     */
    public function softDelete()
    {
        $this->is_delete = 1;
        return $this->save();
    }
}
