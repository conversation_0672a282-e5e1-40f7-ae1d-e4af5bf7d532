<template>
  <div class="login-container">
    <!-- 背景装饰元素 -->
    <div class="background-decoration">
      <div class="circle circle-1"></div>
      <div class="circle circle-2"></div>
      <div class="circle circle-3"></div>
    </div>

    <div class="login-wrapper">
      <div class="login-card">
        <!-- Logo和标题区域 -->
        <div class="header-section">
          <div class="logo">
            <svg
              width="48"
              height="48"
              viewBox="0 0 48 48"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <circle cx="24" cy="24" r="20" fill="url(#gradient)" />
              <path
                d="M24 8C30.6274 8 36 13.3726 36 20C36 26.6274 30.6274 32 24 32C17.3726 32 12 26.6274 12 20C12 13.3726 17.3726 8 24 8Z"
                fill="white"
                opacity="0.3"
              />
              <path d="M20 18H28V22H20V18Z" fill="white" />
              <path d="M16 24H32V28H16V24Z" fill="white" />
              <defs>
                <linearGradient
                  id="gradient"
                  x1="4"
                  y1="4"
                  x2="44"
                  y2="44"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop stop-color="#1bb394" />
                  <stop offset="1" stop-color="#16a085" />
                </linearGradient>
              </defs>
            </svg>
          </div>
          <div class="title">AI择校报告管理系统</div>
          <div class="subtitle">智能化择校决策支持平台</div>
        </div>

        <!-- 表单区域 -->
        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="请输入用户名"
              prefix-icon="User"
              size="large"
              class="custom-input"
            />
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              prefix-icon="Lock"
              size="large"
              class="custom-input"
              show-password
              @keyup.enter="handleLogin"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              :loading="loading"
              type="primary"
              size="large"
              class="login-button"
              @click="handleLogin"
            >
              <span v-if="!loading">登录系统</span>
              <span v-else>登录中...</span>
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 底部信息 -->
        <div class="footer-info">
          <p>安全登录 · 数据加密</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { useUserStore } from "../../store/modules/user";

const router = useRouter();
const userStore = useUserStore();

// 表单引用
const loginFormRef = ref(null);

// 加载状态
const loading = ref(false);

// 登录表单
const loginForm = reactive({
  username: "",
  password: "",
});

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: "请输入用户名", trigger: "blur" },
    { min: 3, max: 20, message: "长度在 3 到 20 个字符", trigger: "blur" },
  ],
  password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    { min: 6, max: 20, message: "长度在 6 到 20 个字符", trigger: "blur" },
  ],
};

// 处理登录
const handleLogin = async () => {
  if (loading.value) return;

  // 表单验证
  await loginFormRef.value.validate(async (valid) => {
    if (!valid) return;

    try {
      loading.value = true;
      const result = await userStore.login(loginForm);
      ElMessage({
        message: result.msg || "登录成功",
        type: "success",
      });
      router.push({ path: "/" });
    } catch (error) {
      ElMessage.error(error.message || "登录失败");
    } finally {
      loading.value = false;
    }
  });
};
</script>

<style scoped>
.login-container {
  height: 100vh;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #1bb394 0%, #16a085 100%);
}

/* 背景装饰元素 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: -100px;
  left: -100px;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: -75px;
  animation-delay: 2s;
}

.circle-3 {
  width: 300px;
  height: 300px;
  bottom: -150px;
  left: 50%;
  transform: translateX(-50%);
  animation-delay: 4s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) scale(1);
    opacity: 0.1;
  }

  50% {
    transform: translateY(-20px) scale(1.05);
    opacity: 0.2;
  }
}

.login-wrapper {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 420px;
  padding: 20px;
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: slideIn 0.8s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 头部区域 */
.header-section {
  text-align: center;
  margin-bottom: 32px;
}

.logo {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
  animation: logoSpin 0.8s ease-out;
}

@keyframes logoSpin {
  from {
    opacity: 0;
    transform: rotate(-10deg) scale(0.8);
  }

  to {
    opacity: 1;
    transform: rotate(0deg) scale(1);
  }
}

.title {
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(135deg, #1bb394 0%, #16a085 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 8px;
  line-height: 1.2;
}

.subtitle {
  font-size: 14px;
  color: #64748b;
  font-weight: 400;
  opacity: 0.8;
}

/* 表单样式 */
.login-form {
  margin-top: 32px;
}

.login-form .el-form-item {
  margin-bottom: 20px;
}

:deep(.custom-input .el-input__wrapper) {
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  background: #ffffff;
  padding: 12px 16px;
  height: auto;
}

:deep(.custom-input .el-input__wrapper:hover) {
  border-color: #1bb394;
  box-shadow: 0 2px 8px rgba(27, 179, 148, 0.15);
}

:deep(.custom-input .el-input__wrapper.is-focus) {
  border-color: #1bb394;
  box-shadow: 0 0 0 3px rgba(27, 179, 148, 0.1);
}

:deep(.custom-input .el-input__inner) {
  font-size: 15px;
  font-weight: 400;
  color: #1e293b;
  height: auto;
  line-height: 1.5;
}

:deep(.custom-input .el-input__prefix) {
  color: #64748b;
}

/* 按钮样式 */
.login-button {
  width: 100%;
  height: 48px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #1bb394 0%, #16a085 100%);
  border: none;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  margin-top: 8px;
}

.login-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.login-button:hover::before {
  left: 100%;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(27, 179, 148, 0.3);
}

.login-button:active {
  transform: translateY(0);
}

:deep(.login-button.is-loading) {
  opacity: 0.8;
  cursor: not-allowed;
}

/* 底部信息 */
.footer-info {
  text-align: center;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
}

.footer-info p {
  font-size: 13px;
  color: #64748b;
  margin: 0;
  opacity: 0.7;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-wrapper {
    padding: 16px;
    max-width: 100%;
  }

  .login-card {
    padding: 32px 24px;
    margin: 0 16px;
    border-radius: 16px;
  }

  .title {
    font-size: 24px;
  }

  .circle-1,
  .circle-2,
  .circle-3 {
    display: none;
  }
}

@media (max-width: 480px) {
  .login-card {
    padding: 24px 20px;
  }

  .title {
    font-size: 22px;
  }
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
  .login-card {
    background: rgba(30, 41, 59, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .subtitle {
    color: #94a3b8;
  }

  .footer-info p {
    color: #94a3b8;
  }

  :deep(.custom-input .el-input__wrapper) {
    background: rgba(51, 65, 85, 0.5);
    border-color: rgba(255, 255, 255, 0.1);
  }

  :deep(.custom-input .el-input__inner) {
    color: #f1f5f9;
  }
}
</style>
