<?php
namespace app\controller;

use app\model\User;
use Firebase\JWT\JWT;
use support\Request;
use support\Log;
class UserController
{
    protected $noNeedLogin = ['login']; // 不需要登录的方法
    public function login(Request $request)
    {
        $username = $request->post('username');
        $password = $request->post('password');
        if (!$username || !$password) {
            return json(['code' => 400, 'msg' => '用户名和密码不能为空']);
        }

        $user = User::where('username', $username)->where('status', 1)->find();
        if (!$user) {
            return json(['code' => 400, 'msg' => '用户不存在']);
        }

        // 验证密码
        $encrypted_password = md5(md5($password) . $user->salt);
        if ($encrypted_password !== $user->password) {
            return json(['code' => 400, 'msg' => '密码错误']);
        }

        // 生成 JWT token
        $payload = [
            'uid' => $user->id,
            'username' => $user->username,
            'exp' => time() + 3600*24*7 // 2小时过期
        ];
        $token = JWT::encode($payload, config('app.jwt_key'), 'HS256');

        // 更新登录信息
        $user->last_login_time = time();
        $user->last_login_ip = $request->getRealIp();
        $user->save();

        return json([
            'code' => 0,
            'msg' => '登录成功',
            'data' => [
                'token' => $token,
                'user' => [
                    'id' => $user->id,
                    'username' => $user->username,
                    'nickname' => $user->nickname,
                    'avatar' => $user->avatar,
                    'generated_report_times'=>$user->generated_report_times,
                    'all_report_times'=>$user->all_report_times
                ]
            ]
        ]);
    }
    /**
     * 退出登录
     */
    public function logout(Request $request)
    {
        return json([
            'code' => 0,
            'msg' => '退出成功',
            'data' => null
        ]);
    }

    /**
     * 获取当前用户信息
     */
    public function getUserInfo(Request $request)
    {
        // 从JWT中间件获取用户信息
        $userInfo = $request->user;
        
        if (!$userInfo || !isset($userInfo->uid)) {
            return json(['code' => 401, 'msg' => '用户未登录']);
        }

        // 从数据库获取最新的用户详细信息
        $user = User::where('id', $userInfo->uid)->find();
        if (!$user) {
            return json(['code' => 404, 'msg' => '用户不存在']);
        }

        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => [
                'id' => $user->id,
                'username' => $user->username,
                'nickname' => $user->nickname,
                'avatar' => $user->avatar,
                'generated_report_times' => $user->generated_report_times,
                'all_report_times' => $user->all_report_times,
                'mobile' => $user->mobile,
                'last_login_time' => $user->last_login_time,
                'last_login_ip' => $user->last_login_ip
            ]
        ]);
    }

    /**
     * 验证token有效性
     */
    public function verifyToken(Request $request)
    {
        // JWT中间件已经验证了token，如果能到这里说明token有效
        $userInfo = $request->user;
        
        if (!$userInfo || !isset($userInfo->uid)) {
            return json(['code' => 401, 'msg' => 'token无效']);
        }

        // 验证用户是否仍然存在且状态正常
        $user = User::where('id', $userInfo->uid)->where('status', '1')->find();
        if (!$user) {
            return json(['code' => 401, 'msg' => '用户不存在或已被禁用']);
        }

        return json([
            'code' => 0,
            'msg' => 'token有效',
            'data' => [
                'uid' => $userInfo->uid,
                'username' => $userInfo->username,
                'exp' => $userInfo->exp
            ]
        ]);
    }

    /**
     * 获取老师列表（支持分页和搜索）
     * @param Request $request
     * @return \support\Response
     */
    public function getTeacherList(Request $request)
    {
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 10);
        $keyword = $request->get('keyword', '');

        // 记录请求参数
        Log::info('获取老师列表请求参数: ' . json_encode($request->all()));

        // 构建查询条件 - 默认返回所有用户
        $query = User::where('status', 'enable');

        // 添加搜索条件 - 只根据昵称和手机号搜索
        if (!empty($keyword)) {
            $query->where(function ($q) use ($keyword) {
                $q->where('nickname', 'like', "%{$keyword}%")
                  ->whereOr('mobile', 'like', "%{$keyword}%")
                  ->whereOr('username', 'like', "%{$keyword}%");
            });
        }

        // 获取总数
        $total = $query->count();

        // 获取分页数据
        $teachers = $query->field(['id', 'username', 'nickname', 'mobile', 'avatar'])
            ->order('id', 'desc')
            ->page($page, $limit)
            ->select();

        // 格式化数据
        $result = [];
        foreach ($teachers as $teacher) {
            $result[] = [
                'id' => $teacher['id'],
                'username' => $teacher['username'],
                'nickname' => $teacher['nickname'] ?: $teacher['username'],
                'mobile' => $teacher['mobile'],
                'avatar' => $teacher['avatar']
            ];
        }

        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => $result,
            'total' => $total
        ]);
    }

    /**
     * 搜索教师（用于前端Select组件）
     * @param Request $request
     * @return \support\Response
     */
    public function searchTeacher(Request $request)
    {
        $keyword = $request->get('keyword', '');

        // 记录请求参数
        Log::info('搜索教师请求参数: ' . json_encode($request->all()));

        // 构建查询条件
        $query = User::where('status', 'enable');

        // 添加搜索条件 - 根据昵称和用户名搜索
        if (!empty($keyword)) {
            $query->where(function ($q) use ($keyword) {
                $q->where('nickname', 'like', "%{$keyword}%")
                  ->whereOr('username', 'like', "%{$keyword}%");
            });
        } else {
            // 如果没有关键词，返回空结果
            return json([
                'code' => 0,
                'msg' => 'success',
                'data' => [],
            ]);
        }

        // 限制返回数量，避免数据过多
        $teachers = $query->field(['id', 'username', 'nickname', 'mobile', 'avatar'])
            ->order('id', 'desc')
            ->limit(50)
            ->select();

        // 格式化数据
        $result = [];
        foreach ($teachers as $teacher) {
            $result[] = [
                'id' => $teacher['id'],
                'username' => $teacher['username'],
                'nickname' => $teacher['nickname'] ?: $teacher['username'],
                'mobile' => $teacher['mobile'],
                'avatar' => $teacher['avatar']
            ];
        }

        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => $result
        ]);
    }
}